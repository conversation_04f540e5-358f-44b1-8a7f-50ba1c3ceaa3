package com.enttribe.promptanalyzer.function;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Description;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Description("{description}")
public class ApiTool implements Function<ApiTool.Request, ApiTool.Response> \{

    private static final Logger log = LoggerFactory.getLogger(ApiTool.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private Map<String, String> getMap(String jsonString) \{
        try \{
            log.debug("json string id : \{\}", jsonString);
            return objectMapper.readValue(jsonString, new TypeReference<Map<String, String>>() \{\});
        \} catch (Exception e) \{
            log.error("error inside @method getMap, jsonString : \{\} | error message: \{\}", jsonString, e.getMessage(), e);
            return new HashMap<>();
        \}
    \}

    private String method = "{method}";
    private String url = "{url}";
    private String requestBody = "{requestBody}";
    private String headers = "{headers}";

    @Override
    public Response apply(Request request) \{
        log.info("executing tool api");
        String response = sendHttpRequest();
        return new Response(response);
    \}

    private String sendHttpRequest() \{

        RestTemplate restTemplate = new RestTemplate();

        Map<String, String> headerMap = getMap(headers);
        try \{
            HttpMethod httpMethod = HttpMethod.valueOf(method.toUpperCase());

            // Set headers
            HttpHeaders httpHeaders = new HttpHeaders();
            if (headerMap != null) \{
                headerMap.forEach(httpHeaders::set);
            \}
            // Set request entity (body + headers)
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, httpHeaders);

            // Make request and get response
            ResponseEntity<String> response = restTemplate.exchange(url, httpMethod, requestEntity, String.class);

            return response.getBody();
        \} catch (Exception e) \{
            log.error("error in sending HTTP request : \{\}", e.getMessage(), e);
            return "Error: " + e.getMessage();
        \}
    \}

    public record Request() \{\}
    public record Response(String response) \{\}

\}
