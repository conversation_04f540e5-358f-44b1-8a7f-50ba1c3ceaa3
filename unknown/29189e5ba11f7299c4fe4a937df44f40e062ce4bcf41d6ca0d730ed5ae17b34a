package com.enttribe.conversationalai.function;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Description;



import java.util.function.Function;

@Service
@Description("API Description: DESCRIPTION")
public class FUNC_NAME implements Function<FUNC_NAME.Request, FUNC_NAME.Response> {

    private final ApplicationContext applicationContext;

    private static final Logger logger = LogManager.getLogger(FUNC_NAME.class);

     @Autowired
        public FUNC_NAME(ApplicationContext applicationContext) {
            this.applicationContext = applicationContext;
        }

    @Override
    public Response apply(Request request) {
        String response = "";
        try {
            Object llmServiceBean = applicationContext.getBean("LLMService");
            response = (String) llmServiceBean.getClass()
                    .getMethod("getAgentResponse", String.class, String.class, String.class)
                    .invoke(llmServiceBean, request.userQuery, request.userId.toString(), "FUNC_NAME");
            }
        catch (Exception e) {
        logger.error("Error while invoking getAgentResponse method on LLMService", e);
            return new Response("Error: " + e.getMessage());
        }
        return new Response(response);
    }
    // Record classes for request and response
    public record Request(Integer userId, String userQuery) {}
    public record Response(String response) {}
}
