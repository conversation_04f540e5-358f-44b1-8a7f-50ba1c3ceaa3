INSERT INTO LLM_MODEL (API_KEY, BASE_URL, INFERENCE, MODEL, PROVIDER, TYPE) VALUES ('********************************************************', 'https://api.groq.com/openai/', 'groq' , 'nomic-embed-text-v1_5-preview1', 'groq', 'embedding');
INSERT INTO LLM_MODEL (API_KEY, BASE_URL, INFERENCE, MODEL, PROVIDER, TYPE) VALUES ('xdv6hDyIRvQapMeJ8WjKkoYA9787nkqdXq358jkh37iUlVnHhrHeBP10l3Fa7hwA3PJjlaZZeDXNe5W6DCXII0Sgkw78dDq7gzbM6vjuKgFNVNjWAiSPsYaYOMAGAm56', 'https://vllm-34b-alpha-19-3-21.allam.ai/', 'allam-34b' , 'allam', 'allam-34b', 'chat');
INSERT INTO LLM_MODEL (API_KEY, BASE_URL, INFERENCE, MODEL, PROVIDER, TYPE) VALUES ('89b25e5dfdf84c1696c1de4c72a42362', 'https://vwaves-south-gpt3.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview', 'azure' , 'gpt-4o', 'azure', 'chat');

INSERT INTO TICKET_TRIGGER ( DESCRIPTION, DISPLAY_NAME, NAME, CREATED_TIME, DELETED, TYPE ) VALUES ( 'Recurrence triggers your agent on a regular schedule', 'Recurrence', 'Recurrence', NOW(), FALSE, 'recurrence' );

INSERT INTO TICKET_TRIGGER (DESCRIPTION, NAME, DISPLAY_NAME, TRIGGER_META_DATA, DELETED, CREATED_TIME,TYPE,APPLICATION,ENTITY) 
VALUES 
  ('When a new lead is created', 
    'On lead create', 
    'On lead create', 
    '[
            {
                "label": "Status",
                "key": "status",
                "type": "textfield",
                "operators": [
                    { "key": "isEquals", "label": "Is Equals" },
                    { "key": "isNotEquals", "label": "Is Not Equals" },
                    { "key": "isNotNull", "label": "Is Not Null" },
                    { "key": "isNull", "label": "Is Null" },
                    { "key": "isEmpty", "label": "Is Empty" },
                    { "key": "isNotEmpty", "label": "Is Not Empty" },
                    { "key": "contains", "label": "Contains" },
                    { "key": "doesNotContain", "label": "Does Not Contain" },
                    { "key": "startsWith", "label": "Starts With" },
                    { "key": "endsWith", "label": "Ends With" }
                ]
            },
            {
                "label": "Lead Source",
                "key": "leadSource",
                "type": "textfield",
                "operators": [
                    { "key": "isEquals", "label": "Is Equals" },
                    { "key": "isNotEquals", "label": "Is Not Equals" },
                    { "key": "isNotNull", "label": "Is Not Null" },
                    { "key": "isNull", "label": "Is Null" },
                    { "key": "isEmpty", "label": "Is Empty" },
                    { "key": "isNotEmpty", "label": "Is Not Empty" },
                    { "key": "contains", "label": "Contains" },
                    { "key": "doesNotContain", "label": "Does Not Contain" },
                    { "key": "startsWith", "label": "Starts With" },
                    { "key": "endsWith", "label": "Ends With" }
                ]
            },
            {
                "label": "Lead Type",
                "key": "leadType",
                "type": "textfield",
                "operators": [
                    { "key": "isEquals", "label": "Is Equals" },
                    { "key": "isNotEquals", "label": "Is Not Equals" },
                    { "key": "isNotNull", "label": "Is Not Null" },
                    { "key": "isNull", "label": "Is Null" },
                    { "key": "isEmpty", "label": "Is Empty" },
                    { "key": "isNotEmpty", "label": "Is Not Empty" },
                    { "key": "contains", "label": "Contains" },
                    { "key": "doesNotContain", "label": "Does Not Contain" },
                    { "key": "startsWith", "label": "Starts With" },
                    { "key": "endsWith", "label": "Ends With" }
                ]
            },
            {
                "label": "Client Name",
                "key": "clientName",
                "type": "textfield",
                "operators": [
                    { "key": "isEquals", "label": "Is Equals" },
                    { "key": "isNotEquals", "label": "Is Not Equals" },
                    { "key": "isNotNull", "label": "Is Not Null" },
                    { "key": "isNull", "label": "Is Null" },
                    { "key": "isEmpty", "label": "Is Empty" },
                    { "key": "isNotEmpty", "label": "Is Not Empty" },
                    { "key": "contains", "label": "Contains" },
                    { "key": "doesNotContain", "label": "Does Not Contain" },
                    { "key": "startsWith", "label": "Starts With" },
                    { "key": "endsWith", "label": "Ends With" }
                ]
            }
        ]',
    false, now(),'trigger','CRM_APP_NAME','Leads'
);



INSERT INTO EXCEPTION_AUDIT (
    ID, APPLICATION_NAME, CATEGORY, EXCEPTION_MESSAGE, EXCEPTION_TRACE, 
    IDENTIFIER, METHOD_NAME, METHOD_PARAMETERS, PROMPT_ID, TIMESTAMP, 
    AUDIT_ID, CUSTOMER_ID, CREATOR, LAST_MODIFIER
) VALUES (
    3, 
    'EMAIL_SINGULARITY_APP_NAME', 
    'Event', 
    '400 - {"error":{"message":"model" : property "model" is missing","type":"invalid_request_error"}}', 
    'org.springframework.ai.retry.RetryUtils$1.handleError(RetryUtils.java:63) ... java.base/java.lang.VirtualThread.run(VirtualThread.java:329)', 
    NULL, 
    'public java.lang.Object com.enttribe.commons.ai.chat.impl.AiChatModelImpl.chatCompletion(java.lang.String,java.util.Map,java.lang.Class)', 
    '{"promptId":"EMAIL_SINGULARITY_APP_NAME-Event-Intent_Prompt-v-1","format":"com.enttribe.emailagent.ai.dto.meeting.IntentResponseNew","variableMap":{"meetingDuration":30,"currentDate":"2024-12-12","dayName":"THURSDAY","userMessage":"HI Required participants are: <EMAIL> <EMAIL> subject of the meeting is : Review Request: Project Beta Plan"}}', 
    'EMAIL_SINGULARITY_APP_NAME-Event-Intent_Prompt-v-1', 
    '2024-12-12 16:29:13.055000', 
    NULL, 
    NULL, 
    NULL, 
    NULL
);


INSERT INTO KNOWLEDGE_BASE (
    ID, NAME, TYPE, DESCRIPTION, API_AUTH_TYPE, API_AUTH_VALUE, API_ENDPOINT, API_TYPE, 
    DB_NAME, DB_PASSWORD, DB_USERNAME, DOC_ID, DOC_META_DATA, DOC_TYPE, IS_CONTEXT, 
    SIMILARITY_THRESHOLD, SOURCE_CODE, CLASS_NAME, BYTE_CODE_MAP, TOP_K, CREATED_TIME, 
    MODIFIED_TIME, VECTOR_META_DATA, DELETED, FILE_NAME, FILTER, WEBSITE_URL, 
    WEBSITE_TASK_STATUS, COLLECTION_NAME, WEBSITE_TASK_ID, WEBSITE_TASK_ERROR
) VALUES 
(
    8, 'name', 'DOCUMENT', 'desc', NULL, NULL, NULL, NULL, 
    NULL, NULL, NULL, '73500533-516a-40b3-9429-2e66eada63e5', 
    '{"doc_id":"73500533-516a-40b3-9429-2e66eada63e5"}', NULL, 0x00, 1, 
    NULL, NULL, NULL, 1, '2025-01-31 06:01:25.860000', '2025-01-31 06:01:26.768000', 
    '{"chatModel":"llama-3.3-70b-versatile","vectorDatabase":"milvus","embeddingModel":"nomic-embed-text-v1_5-preview1","provider":"groq","collectionName":"vector_store_knowledge_base","databaseName":"default"}', 
    0, 'Vision AI Product Feature Document.docx', '73500533-516a-40b3-9429-2e66eada63e5', 
    NULL, NULL, NULL, NULL, NULL
),
(
    9, '5G_MIMO_Antenna_Installation_Guide', 'DOCUMENT', 'Installation guide for the 5G MIMO Directional Panel Antenna (LTE-XPOL-002-5G), supporting 5G to 2G networks (698?3800 MHz). Includes mounting instructions, package contents, and optimal setup tips.', 
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, '1ea276b2-015d-4b0b-b9f9-25f340f9ff43', 
    '{"doc_id":"1ea276b2-015d-4b0b-b9f9-25f340f9ff43"}', NULL, 0x00, 0.7, 
    NULL, NULL, NULL, 5, '2025-01-17 16:20:29.363000', NULL, 
    '{"databaseName":"default","collectionName":"vector_store_knowledge_base","provider":"groq","embeddingModel":"nomic-embed-text-v1_5-preview1","vectorDatabase":"milvus","chatModel":"llama-3.3-70b-versatile"}', 
    0, 'LTE-XPOL-002-5G-PCL-V1.pdf', '1ea276b2-015d-4b0b-b9f9-25f340f9ff43', 
    NULL, NULL, NULL, NULL, NULL
),
(
    10, 'Email Categorization Rules', 'DOCUMENT', 'Defines the rules for categorizing emails into Attention, FYI, and Events based on sender, subject, and content.', 
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'daed5e0f-8637-4535-99f1-9aff28feaceb', 
    '{"doc_id":"daed5e0f-8637-4535-99f1-9aff28feaceb"}', NULL, 0x00, 0.36, 
    NULL, NULL, NULL, 6, '2025-01-31 07:01:33.708000', '2025-01-31 07:01:34.444000', 
    '{"chatModel":"llama-3.3-70b-versatile","vectorDatabase":"milvus","embeddingModel":"nomic-embed-text-v1_5-preview1","provider":"groq","collectionName":"vector_store_knowledge_base","databaseName":"default"}', 
    0, 'Email Categorization Rule.pdf', 'daed5e0f-8637-4535-99f1-9aff28feaceb', 
    NULL, NULL, NULL, NULL, NULL
);



INSERT INTO PROCESSOR (
    ID, UNIQUE_KEY, DISPLAY_NAME, ICON, STYLE_TYPE, CATEGORY, SUB_CATEGORY, 
    CREATED_TIME, MODIFIED_TIME, DELETED
) VALUES 
(
    551, 'com.enttribe.custom.action.AcceptMeeting', 'AcceptMeeting', 'AcceptMeeting.svg', 
    'circle', 'block', 'utilities', '2025-02-13 16:52:09.000000', '2025-02-14 08:55:04.309000', 0x00
),
(
    552, 'com.enttribe.custom.action.AlarmCreate', 'AlarmCreate', 'AlarmCreate.svg', 
    'circle', 'block', 'other', '2025-02-13 16:52:09.000000', '2025-02-13 16:52:17.000000', 0x00
),
(
    553, 'org.apache.nifi.processors.stateful.analysis.AttributeRollingWindow', 'AttributeRollingWindow', 
    'AttributeRollingWindow.svg', 'circle', 'block', 'other', 
    '2025-02-13 16:52:09.000000', '2025-02-13 16:52:17.000000', 0x00
);



INSERT INTO MESSAGES (ID, CONTENT, ROLE, PROMPT_ID) VALUES
(1, 'write a letter', 'system', 1),
(2, 'make it professional', 'user', 1),
(5, '{{prompt}}', 'system', 2);



INSERT INTO PROMPT (ID, APPLICATION, ASSERTION_TEMPLATE, CATEGORY, DEFAULT_FORMAT, DELETED, MAX_TOKEN, MODIFIED_TIME, NAME, PROMPT_ID, STATUS, TEMPERATURE, TOP_P, VERSION, LLM_MODEL_ID, CREATED_TIME, JSON_MODE, LLM_GUARD, TYPE) VALUES
(1, 'EMAIL_SINGULARITY_APP_NAME', NULL, 'Category', NULL, 0x00, 1024, NULL, 'NAME', 'EMAIL_SINGULARITY_APP_NAME-Category-Name-v-0', 'DRAFT', 0.7, 1, 'v-0', 16, NULL, 0x00, 0, 'NORMAL'),
(2, 'EMAIL_SINGULARITY_APP_NAME', NULL, 'Category', NULL, 0x00, 1024, '2024-12-10 15:51:35.905000', 'abc', 'EMAIL_SINGULARITY_APP_NAME-Category-abc-v-0', 'DRAFT', 0.7, 1, 'v-0', 16, NULL, 0x00, 0, 'NORMAL'),
(6, 'PROJECT-MANAGEMENT_APP_NAME', NULL, 'TaskGroup', NULL, 0x00, 1024, '2024-12-11 08:38:29.266000', 'RISK_BY_GROUP', 'PROJECT-MANAGEMENT_APP_NAME-TaskGroup-RISK_BY_GROUP-v-1', 'PUBLISH', 0.7, 1, 'v-1', 16, NULL, 0x00, 0, 'NORMAL');


INSERT INTO PROMPT_AUDIT (
    ID, APPLICATION_NAME, CATEGORY, GENERATION_TOKENS, MODEL, PROMPT_ID, PROMPT_TOKEN,
    RESPONSE_TEXT, RESPONSE_TIME, TOTAL_TOKEN, CHAT_OPTIONS, META_TAGS, FUNCTION_ARGS,
    AUDIT_ID, PROMPT_NAME, CREATION_TIME, HTTP_STATUS, REQUEST_TEXT, STATUS, TOTAL_COST, PROVIDER
) VALUES
(1, 'x101', NULL, 7, 'lamma-3.2-3b-ft-intent', 'x101-Intent-Intent_Identification-v-1', 172,
    '{"category":"Update"}', 500, 179, '{"maxTokens":50,"temperature":0.3,"model":"lamma-3.2-3b-ft-intent","topP":0.5}', 
    NULL, NULL, 'x101_a_1739344491902_1739344492380', 'Intent Identification', '2025-02-12 07:14:53', NULL, 
    '[{"messageType":"SYSTEM","metadata":{"messageType":"SYSTEM"},"content":"You are tasked with interpreting user messages expressed in natural language, assessing the intent, and classifying each message into one of the following categories: [List, Analytics, Create, Update, Upload, Delete]\n\n\n\n\nResponse format Instructions:\n- Your response should be in JSON format.\n- The data structure for the JSON should match this Java class: java.util.HashMap\n- Do not include any explanations, only provide a RFC8259 compliant JSON response following this format without deviation.\n*Response Format:*\n```json\n{\n   \"category\": \"<Choose one:[List, Analytics, Create, Update, Upload, Delete]>\"\n}\n```\nUser Message : hey\n```\n"},{"messageType":"USER","metadata":{"messageType":"USER"},"media":[],"content":"User Query: hey"}]', 
    'success', 0.00017549999999999998, 'huggingface'
),
(2, 'x101', NULL, 22, 'llama-3.3-70b-versatile', 'x101-NER_Word-Spell_Corrector-v-1', 856,
    '{"city": [], "person": [], "department": [], "designation": []}', 858, 878, 
    '{"maxTokens":1024,"temperature":0.3,"model":"llama-3.3-70b-versatile","topP":1.0}', 
    NULL, NULL, 'x101_a_1739344491902_1739344492380', 'Spell Corrector', '2025-02-12 07:14:54', NULL, 
    '[{"messageType":"SYSTEM","metadata":{"messageType":"SYSTEM"},"content":"You are a highly skilled Named Entity Recognition (NER) specialist, trained to extract specific entities from text with precision. Your task is to accurately identify and extract entities based on predefined NER criteria, ensuring their original mention is preserved exactly as it appears in the text, including any spelling errors or variations. \n\n### **NER CRITERIA**\n\nYou need to focus on the following type of named entities within the given text for extraction:\n\n1. **CITY**: Names of cities.\n2. **PERSON**: Names of individuals, including full names and single names which indicate any specific person.\n3. **DEPARTMENT**: Names of organizational departments (e.g., \"Human Resources,\" \"Sales,\" \"Engineering\").\n4. **DESIGNATION**: Titles or roles within an organization (e.g., \"Manager,\" \"Software Engineer,\" \"Chief Executive Officer\").\n\n### **INSTRUCTIONS FOR NER EXTRACTION**\n\n- Only extract entities that match one of the specified categories (City, Person, Department, or Designation) in their exact form from the input, and preserve any spelling or grammatical errors.\n- If an entity does not match any of the specifications, do not include it in the output.\n- If a category has no matching entities, leave it as an empty array (e.g., `\"department\": []`).\n- No inferences or assumptions outside of the explicit content given in the text.\n- Distinct entities are not duplicated across categories; extract each unique name only once in the relevant category.\n### **Steps**\n\nFollow these steps to ensure accurate extraction:\n\n1. Read the text carefully and identify instances of each entity type as defined above.\n2. **UNDERSTAND** the entity types:\n- City: Geographic locations that are specifically cities.\n- Person: Names referring to individuals.\n- Department: Organizational units within a company or institution.\n- Designation: Professional titles or roles in an organization.\n\n3. **PARSE** the text sequentially to ensure each word and phrase is checked for possible matches to the predefined entity types.\n\n4. **CATEGORIZE** each identified entity:\n- Verify that each extracted entity conforms strictly to its category.\n- Discard any ambiguous entities that do not clearly fit the specified types.\n\n5. **FORMAT OUTPUT** using the defined structure to present each extracted entity accurately.\n\n6. **DOUBLE-CHECK** for consistency, ensuring no entity is missed or incorrectly classified.\n\n\n\n\n### **Examples for Clarity:**\n\n**Example 1**: User input with no entity\n- **Input**: Please show me list of employees.\n- **Output**:\n```json\n{\n\"city\": [],\n\"person\": [],\n\"department\": [],\n\"designation\": []\n}\n```\n\n**Example 2**: User input with multiple entities in same category\n- **Input**: The event was attended by Sarah and Peter.\n- **Output**:\n```json\n{\n\"city\": [],\n\"person\": [\"Sarah\", \"Peter\"],\n\"department\": [],\n\"designation\": []\n}\n```\n\n**Example 3**: \n- **Input**: Susan from the salse team met with Peter in Los Angles to discuss the marketing strategy.\n- **Output**:\n```json\n{\n\"city\": [\"Los Angles\"],\n\"person\": [\"Susan\", \"Peter\"],\n\"department\": [\"salse\"],\n\"designation\": []\n}\n```\n\n**Example 4** User input with wrong spelling in city name\n- **Input**: I am from New Yor and I work in marketing department as software engineer.\n- **Output**:\n```json\n{\n\"city\": [\"New Yor\"],\n\"person\": [],\n\"department\": [\"marketing\"],\n\"designation\": [\"software engineer\"]\n}\n```\n\n#### **Output Format:**\nThe response should be in the following JSON format:\n```json\n{\n\"city\": [\"distinct city_name(s)\"],\n\"person\": [\"person_name(s)\"],\n\"department\": [\"department_name(s)\"],\n\"designation\": [\"designation_name(s)\"]\n}\n```"},{"messageType":"USER","metadata":{"messageType":"USER"},"media":[],"content":"User Query: hey"}]', 
    'success', 0.0008669999999999999, 'groq'
);



INSERT INTO QUERY (ID, CREATED_TIME, MODIFIED_TIME, QUESTION, TYPE, USER_ID, DELETED, CUSTOMER_ID, CREATOR, LAST_MODIFIER)
VALUES 
(1, '2025-01-28 10:33:21.861000', NULL, 'what is Trigger', 'ques', '<EMAIL>', 1, NULL, NULL, NULL),
(2, '2025-01-28 10:38:54.038000', NULL, 'what is Query', 'ques', '<EMAIL>', 0, NULL, NULL, NULL),
(3, '2025-01-29 06:22:19.320000', NULL, 'hello', 'query', '140366', 1, NULL, NULL, NULL);



INSERT INTO TESTCASE (ID, ASSERTIONS, DELETED, INPUT_JSON, REMARK, TESTCASE_ID, PROMPT_ID, CREATED_TIME, MODIFIED_TIME)
VALUES 
(1, '[{"name":"category","type":"equals","value":"c1"},{"name":"priority","type":"icontains","value":"p1"}]', 0x00, '{"subject":"subject 1 "}', NULL, 'TEST-00001', 11, '2024-12-11 08:56:13.522000', '2024-12-11 08:56:13.522000'),
(2, '[{"name":"Category","type":"equals","value":"Urgent"},{"name":"Priority","type":"contains","value":"High"}]', 0x01, '{"topic":"Economy"}', NULL, 'TEST-00002', 12, '2024-12-11 09:15:58.262000', '2025-01-09 09:22:32.231000'),
(3, '[{"name":"Category","type":"equals","value":"abc"},{"name":"Priority","type":"contains","value":"def"}]', 0x00, '{"topic":"new"}', NULL, 'TEST-00003', 12, '2024-12-11 09:26:18.108000', '2024-12-11 09:26:18.108000');

INSERT INTO TOOL (
    ID, APPLICATION_NAME, CATEGORY, CLASS_NAME, CREATED_TIME, DELETED, DESCRIPTION, 
    DISPLAY_NAME, LANGUAGE, MODIFIED_TIME, REQUEST_TYPE, TOOL_ID, TOOL_NAME, TYPE, 
    VERSION, PROMPT_ID, TOOL_JSON, TOOL_AUTHENTICATION, TAGS, STATUS, HTTP_METHOD, 
    PARAMETERS, URL, AUTH_TYPE, AUTH_VALUE, AUTH_KEY, TOOL_IMAGE, AGENT_ID, API_TOOL_INFO
) VALUES (
    176, 'EMAIL_SINGULARITY_APP_NAME', 'Conversation', 
    'com.enttribe.promptanalyzer.function.AvailableMeetingSlots', 
    '2025-01-15 09:01:16.574000', 0x00, 
    'Always ask user to provide following information prior to function call : subject, recipient and content if not provided', 
    'Available Meeting Slots', NULL, '2025-02-03 08:41:29.941000', 
    NULL, 'EMAIL_SINGULARITY_APP_NAME-Conversation-AvailableMeetingSlots-v-0', 
    'AvailableMeetingSlots', 'sourceCode', 'v-0', NULL, NULL, NULL, NULL, 'PUBLISH', 
    NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
);

