#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Check if an argument is provided
if [ -z "$1" ]; then
    echo "Error: Version argument is required."
    echo "Usage: $0 <version>"
    exit 1
fi

mvn clean install -DskipTests=true -Dproguard.skip=true -Ddependency-check.skip=true

cd prompt-analyzer-service/target/

tar -czf prompt-analyzer.tar prompt-analyzer-service-1.2.0.jar application.properties run.sh Dockerfile melodyposthook.sh

docker build --platform=linux/amd64 -t  registry.visionwaves.com/prompt-analyzer-service:"$1"  --build-arg APP_NAME=prompt-analyzer . --no-cache
docker push registry.visionwaves.com/prompt-analyzer-service:"$1"

