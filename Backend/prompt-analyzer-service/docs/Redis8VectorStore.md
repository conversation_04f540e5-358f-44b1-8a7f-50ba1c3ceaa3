# Redis 8 Vector Store Implementation

This document describes the custom Redis 8 Vector Store implementation that uses Redis 8's native vector sets (VADD, VSIM commands) instead of Spring AI's default Redis Stack FT. commands.

## Overview

Redis 8 introduces **Vector Sets**, a new native data type for vector similarity operations. This implementation provides:

- **Native vector operations** using VADD, VSIM, VDEL commands
- **Built-in quantization** (8-bit by default, configurable to binary or none)
- **Dimensionality reduction** via random projection
- **JSON-based metadata filtering** via VSETATTR
- **Better performance** compared to Redis Stack's FT. commands
- **Simplified API** that's more Redis-native

## Configuration

### Enable Redis 8 Vector Store

Set the following property in your `application.properties`:

```properties
# Switch to Redis 8 vector store (default is redis-stack)
vector.store.type=redis8
```

### Redis 8 Configuration Options

```properties
# Quantization type: Q8 (default), BIN, NOQUANT
vector.store.redis8.quantization=Q8

# Dimensionality reduction
vector.store.redis8.dimensionality-reduction.enabled=false
vector.store.redis8.dimensionality-reduction.target-dimensions=512

# Schema initialization
vector.store.redis8.initialize-schema=true

# Batching configuration
vector.store.redis8.batching.enabled=true
vector.store.redis8.batching.max-tokens=8000
vector.store.redis8.batching.reserve-percentage=0.1
```

## Key Features

### 1. Quantization Options

Redis 8 vector sets support different quantization levels:

- **Q8 quantization** (default): 8-bit quantization reduces memory usage while maintaining good accuracy
- **BIN quantization**: Binary quantization provides maximum memory savings with reduced accuracy
- **NOQUANT**: No quantization maintains full precision with maximum memory usage

```java
Redis8VectorStoreConfig config = Redis8VectorStoreConfig.builder()
    .quantizationType(Redis8VectorStoreConfig.QuantizationType.QUANTIZE_8BIT)  // Q8
    // or .quantizationType(Redis8VectorStoreConfig.QuantizationType.BINARY)     // BIN
    // or .quantizationType(Redis8VectorStoreConfig.QuantizationType.NONE)       // NOQUANT
    .build();
```

### 2. Dimensionality Reduction

Reduce vector dimensions using random projection:

```java
Redis8VectorStoreConfig config = Redis8VectorStoreConfig.builder()
    .withDimensionalityReduction(512)  // Reduce to 512 dimensions
    .build();
```

### 3. JavaScript-like Filtering

Redis 8 supports JavaScript-like filter expressions with dot notation:

```java
SearchRequest request = SearchRequest.builder()
    .query("your search query")
    .filterExpression("doc_id == 'some-id' && custom_agent_id == 'agent-123'")
    .build();
```

Filter expressions use familiar JavaScript syntax:
- Comparison: `==`, `!=`, `>`, `<`, `>=`, `<=`
- Logical: `and`, `or`, `not` (or `&&`, `||`, `!`)
- Containment: `in`
- Dot notation: `.fieldName` to access attributes

## Usage Examples

### Basic Usage

The Redis 8 vector store implements the same `VectorStore` interface, so existing code works without changes:

```java
@Autowired
private VectorStore vectorStore;  // Will be Redis8VectorStore when configured

// Add documents
List<Document> documents = Arrays.asList(
    new Document("Content 1", Map.of("category", "tech")),
    new Document("Content 2", Map.of("category", "science"))
);
vectorStore.add(documents);

// Search
List<Document> results = vectorStore.similaritySearch("search query");
```

### Advanced Configuration

```java
@Bean
public VectorStore customRedis8VectorStore(JedisPooled jedisPooled, 
                                         EmbeddingModel embeddingModel,
                                         BatchingStrategy batchingStrategy) {
    
    Redis8VectorStoreConfig config = Redis8VectorStoreConfig.builder()
        .vectorSetName("my_custom_vector_set")
        .keyPrefix("custom_prefix_")
        .withBinaryQuantization()  // Use binary quantization
        .withDimensionalityReduction(256)  // Reduce to 256 dimensions
        .initializeSchema(true)
        .build();

    return Redis8VectorStore.builder(jedisPooled, embeddingModel)
        .batchingStrategy(batchingStrategy)
        .config(config)
        .build();
}
```

## Migration from Redis Stack

### Automatic Migration

The system supports both Redis Stack and Redis 8 configurations. To migrate:

1. **Test Redis 8 support**:
   ```bash
   curl http://localhost:8080/api/v1/redis8-vector/support-check
   ```

2. **Validate configuration**:
   ```bash
   curl http://localhost:8080/api/v1/redis8-vector/validate
   ```

3. **Switch configuration**:
   ```properties
   vector.store.type=redis8
   ```

4. **Restart the application**

### Manual Migration

Use the migration service to move existing data:

```java
@Autowired
private Redis8VectorMigrationService migrationService;

// Migrate documents
List<Document> existingDocuments = getExistingDocuments();
int migratedCount = migrationService.migrateDocuments(existingDocuments);
```

## API Endpoints

When Redis 8 vector store is enabled, the following endpoints are available:

### Test Vector Search
```bash
GET /api/v1/redis8-vector/test-search?query=test%20query
```

### Get Statistics
```bash
GET /api/v1/redis8-vector/stats?vectorSetName=vector_store_knowledge_base
```

### Validate Configuration
```bash
GET /api/v1/redis8-vector/validate
```

### Check Support
```bash
GET /api/v1/redis8-vector/support-check
```

### Migrate Documents
```bash
POST /api/v1/redis8-vector/migrate
Content-Type: application/json

[
  {
    "id": "doc1",
    "text": "Document content",
    "metadata": {"category": "tech"}
  }
]
```

### Cleanup Vector Set
```bash
DELETE /api/v1/redis8-vector/cleanup/vector_set_name
```

## Performance Benefits

Redis 8 vector sets provide several performance advantages:

1. **Native vector operations**: Direct vector commands instead of search module overhead
2. **Built-in quantization**: Reduced memory usage and faster operations
3. **Optimized storage**: More efficient vector storage format
4. **Simplified architecture**: No need for Redis Stack modules

## Troubleshooting

### Common Issues

1. **Redis 8 not supported**:
   - Ensure you're using Redis 8 or later
   - Check if vector sets module is enabled

2. **Configuration not loading**:
   - Verify `vector.store.type=redis8` is set
   - Check application logs for configuration errors

3. **Vector operations failing**:
   - Test Redis connection: `curl /api/v1/redis8-vector/support-check`
   - Validate configuration: `curl /api/v1/redis8-vector/validate`

### Logging

Enable debug logging for Redis 8 vector operations:

```properties
logging.level.com.enttribe.promptanalyzer.vectorstore=DEBUG
logging.level.com.enttribe.promptanalyzer.service.Redis8VectorMigrationService=DEBUG
```

## Compatibility

- **Redis Version**: Requires Redis 8.0 or later
- **Spring AI**: Compatible with Spring AI VectorStore interface
- **Existing Code**: Drop-in replacement for Redis Stack vector store
- **Configuration**: Conditional configuration based on `vector.store.type` property

## Future Enhancements

Planned improvements:

1. **Automatic fallback**: Fallback to Redis Stack if Redis 8 is not available
2. **Performance monitoring**: Built-in metrics and monitoring
3. **Advanced filtering**: More complex filter expressions
4. **Bulk operations**: Optimized bulk insert/update operations
5. **Clustering support**: Redis Cluster compatibility
