/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.enttribe.promptanalyzer.dao.AgentTestCaseDao;
import com.enttribe.promptanalyzer.dao.TestSuiteDao;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.model.AgentTestCase;
import com.enttribe.promptanalyzer.model.TestSuite;
import com.enttribe.promptanalyzer.service.AgentTestCaseService;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TestSuiteServiceImplTest {

  @Mock private TestSuiteDao testSuiteDao;

  @Mock private AgentTestCaseDao agentTestCaseDao;

  @Mock private AgentTestCaseService agentTestCaseService;

  @InjectMocks private TestSuiteServiceImpl testSuiteService;

  @Test
  void testCreateTestSuiteWithNewAgentTestCasesSuccess() {
    // Given
    TestSuiteRequestDto dto = new TestSuiteRequestDto();
    dto.setName("Regression Suite");
    dto.setAgentId("AGENT-123");
    dto.setAgentName("SmartAgent");

    AgentTestCaseRequestDto tc1 = new AgentTestCaseRequestDto();
    tc1.setName("Login Test");
    tc1.setAgentId("AGENT-123");

    AgentTestCaseRequestDto tc2 = new AgentTestCaseRequestDto();
    tc2.setName("Logout Test");
    tc2.setAgentId("AGENT-123");

    dto.setAgentTestCase(List.of(tc1, tc2));

    // Mock create behavior
    when(agentTestCaseService.create(any())).thenReturn(Map.of("id", "TC-1"), Map.of("id", "TC-2"));
    when(agentTestCaseDao.findByNanoId(anyString())).thenReturn(Optional.of(new AgentTestCase()));

    // When
    Map<String, String> result = testSuiteService.create(dto);

    // Then
    assertEquals("SUCCESS", result.get("result"), "TestSuite save result should be 'SUCCESS'");
    verify(testSuiteDao).save(any(TestSuite.class));
    verify(agentTestCaseService, times(2)).create(any());
  }

  @Test
  void testUpdateTestSuiteWithMixedTestCasesSuccess() {
    // Given
    TestSuite existingSuite = new TestSuite();
    existingSuite.setNanoId("TS-001");
    existingSuite.setAgentTestCases(new HashSet<>());

    when(testSuiteDao.findByNanoId("TS-001")).thenReturn(Optional.of(existingSuite));

    AgentTestCaseRequestDto existingTc = new AgentTestCaseRequestDto();
    existingTc.setId("TC-001");
    existingTc.setName("Login Test Updated");

    AgentTestCaseRequestDto newTc = new AgentTestCaseRequestDto();
    newTc.setName("New Logout Test");

    TestSuiteRequestDto dto = new TestSuiteRequestDto();
    dto.setId("TS-001");
    dto.setAgentTestCase(List.of(existingTc, newTc));

    when(agentTestCaseDao.findByNanoId("TC-001")).thenReturn(Optional.of(new AgentTestCase()));
    when(agentTestCaseService.update(existingTc, false)).thenReturn(Map.of("id", "TC-001"));
    when(agentTestCaseService.create(newTc)).thenReturn(Map.of("id", "TC-NEW"));
    when(agentTestCaseDao.findByNanoId("TC-NEW")).thenReturn(Optional.of(new AgentTestCase()));

    // When
    Map<String, String> result = testSuiteService.update(dto);

    // Then
    assertEquals("SUCCESS", result.get("result"), "TestSuite update result should be 'SUCCESS'");
    verify(agentTestCaseService).update(existingTc, false);
    verify(agentTestCaseService).create(newTc);
    verify(testSuiteDao).save(existingSuite);
  }

  @Test
  void testAddAgentTestCasesToSuiteSuccess() {
    // Given
    TestSuite suite = new TestSuite();
    suite.setNanoId("TS-001");
    suite.setAgentTestCases(new HashSet<>());

    when(testSuiteDao.findByNanoId("TS-001")).thenReturn(Optional.of(suite));

    AgentTestCaseRequestDto newTc = new AgentTestCaseRequestDto();
    newTc.setName("Search Test");
    newTc.setAgentId("AGENT-123");

    TestSuiteRequestDto dto = new TestSuiteRequestDto();
    dto.setId("TS-001");
    dto.setAgentTestCase(List.of(newTc));

    when(agentTestCaseService.create(newTc)).thenReturn(Map.of("id", "TC-SEARCH"));
    when(agentTestCaseDao.findByNanoId("TC-SEARCH")).thenReturn(Optional.of(new AgentTestCase()));

    // When
    Map<String, String> result = testSuiteService.addAgentTestCasesToSuite(dto);

    // Then
    assertEquals("SUCCESS", result.get("result"), "TestSuite delete result should be 'SUCCESS'");
    verify(agentTestCaseService).create(newTc);
    verify(testSuiteDao).save(suite);
  }
}
