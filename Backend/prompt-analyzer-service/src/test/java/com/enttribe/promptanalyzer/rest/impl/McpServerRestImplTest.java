/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.service.McpServerService;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
@WebMvcTest(McpServerRestImpl.class)
class McpServerRestImplTest {

  @Mock private McpServerService mcpServerService;

  @InjectMocks private McpServerRestImpl mcpServerRest;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testCreate() {
    McpServerDto dto = new McpServerDto();
    dto.setName("New Server");

    Map<String, String> expected = Map.of("status", "created");
    when(mcpServerService.create(dto)).thenReturn(expected);

    Map<String, String> result = mcpServerRest.create(dto);
    assertEquals(expected, result, "McpServer create result map should match expected");
    verify(mcpServerService).create(dto);
  }

  @Test
  void testUpdate() {
    McpServerDto dto = new McpServerDto();
    dto.setId("1");
    dto.setName("Updated Server");

    Map<String, String> expected = Map.of("status", "updated");
    when(mcpServerService.update(dto)).thenReturn(expected);

    Map<String, String> result = mcpServerRest.update(dto);
    assertEquals(expected, result, "McpServer update result map should match expected");
    verify(mcpServerService).update(dto);
  }

  @Test
  void testSearch() {
    List<McpServerDto> expectedList = List.of(new McpServerDto());
    when(mcpServerService.search("test", 0, 10, "name", "asc")).thenReturn(expectedList);

    List<McpServerDto> result = mcpServerRest.search("test", 0, 10, "name", "asc");
    assertEquals(expectedList, result, "McpServer search result list should match expected");
    verify(mcpServerService).search("test", 0, 10, "name", "asc");
  }

  @Test
  void testCount() {
    when(mcpServerService.count("filter")).thenReturn(5L);

    Long count = mcpServerRest.count("filter");
    assertEquals(5L, count, "McpServer count should match expected");
    verify(mcpServerService).count("filter");
  }

  @Test
  void testSoftDelete() {
    String id = "1";
    Map<String, String> expected = Map.of("status", "deleted");
    when(mcpServerService.softDelete("id")).thenReturn(expected);

    Map<String, String> result = mcpServerRest.softDelete(id);
    assertEquals(expected, result, "McpServer delete result map should match expected");
    verify(mcpServerService).softDelete(id);
  }

  @Test
  void testGetMcpServerByIds() {
    List<String> ids = List.of("1", "2", "3");
    List<McpServerDto> expected = List.of(new McpServerDto(), new McpServerDto());
    when(mcpServerService.getMcpServerByIds(ids)).thenReturn(expected);

    List<McpServerDto> result = mcpServerRest.getMcpServerByIds(ids);
    assertEquals(expected, result, "McpServer restore result map should match expected");
    verify(mcpServerService).getMcpServerByIds(ids);
  }
}
