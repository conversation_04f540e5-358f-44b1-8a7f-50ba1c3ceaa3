/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.CrawlerService;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.multipart.MultipartFile;

@WebMvcTest(KnowledgeBaseRestImpl.class)
class KnowledgeBaseRestImplTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockitoBean private KnowledgeBaseService knowledgeBaseService;

  @MockitoBean private CrawlerService crawlerService;

  @InjectMocks private KnowledgeBaseRestImpl knowledgeBaseRest;

  private KnowledgeBaseResponseDto knowledgeBaseResponseDto;
  private String testId;
  private MockMultipartFile testFile;
  private Map<String, String> successResponse;
  private List<KnowledgeBaseSdkDto> knowledgeBaseSdkDtos;

  @BeforeEach
  void setUp() {
    testId = "1";
    knowledgeBaseResponseDto =
        KnowledgeBaseResponseDto.builder()
            .id(testId)
            .name("Test Knowledge Base")
            .description("Test Description")
            .type("database")
            .isContext(true)
            .topK(5)
            .similarityThreshold(0.8)
            .build();

    testFile =
        new MockMultipartFile(
            "file", "test.pdf", MediaType.APPLICATION_PDF_VALUE, "test content".getBytes());
    successResponse = Map.of("status", "success", "message", "Operation completed successfully");

    knowledgeBaseSdkDtos =
        Arrays.asList(
            KnowledgeBaseSdkDto.builder()
                .id("1")
                .name("Test KB 1")
                .type("document")
                .description("Test Description 1")
                .topK(5)
                .similarityThreshold(0.8)
                .isContext(true)
                .build(),
            KnowledgeBaseSdkDto.builder()
                .id("2")
                .name("Test KB 2")
                .type("website")
                .description("Test Description 2")
                .topK(10)
                .similarityThreshold(0.7)
                .isContext(false)
                .build());
  }

  @Test
  @DisplayName("Get knowledge base by ID success")
  void testGetKnowledgeBaseByIdSuccess() throws Exception {
    // Arrange
    when(knowledgeBaseService.getKnowledgeBaseById(1)).thenReturn(knowledgeBaseResponseDto);

    // Act & Assert
    mockMvc
        .perform(
            get("/knowledge-base/findById/{id}", testId).contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id").value(testId))
        .andExpect(jsonPath("$.name").value("Test Knowledge Base"))
        .andExpect(jsonPath("$.description").value("Test Description"))
        .andExpect(jsonPath("$.type").value("database"))
        .andExpect(jsonPath("$.isContext").value(true))
        .andExpect(jsonPath("$.topK").value(5))
        .andExpect(jsonPath("$.similarityThreshold").value(0.8))
        .andDo(print());

    verify(knowledgeBaseService).getKnowledgeBaseById(1);
  }

  @Test
  @DisplayName("Get knowledge base by ID not found")
  void testGetKnowledgeBaseByIdNotFound() throws Exception {
    // Arrange
    when(knowledgeBaseService.getKnowledgeBaseById(anyInt()))
        .thenThrow(new BusinessException("Knowledge base not found"));

    // Act & Assert
    mockMvc
        .perform(get("/knowledge-base/findById/{id}", 999).contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(knowledgeBaseService).getKnowledgeBaseById(999);
  }

  @Test
  @DisplayName("Process website KB success")
  void testProcessWebSiteKBSuccess() throws Exception {
    // Arrange
    doNothing().when(crawlerService).processWebSiteKB();

    // Act & Assert
    mockMvc
        .perform(get("/knowledge-base/processWebSiteKB").contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.result").value("done"))
        .andDo(print());

    verify(crawlerService).processWebSiteKB();
  }

  @Test
  @DisplayName("Process website KB with error")
  void testProcessWebSiteKBError() throws Exception {
    // Arrange
    doThrow(new BusinessException("Processing failed")).when(crawlerService).processWebSiteKB();

    // Act & Assert
    mockMvc
        .perform(get("/knowledge-base/processWebSiteKB").contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(crawlerService).processWebSiteKB();
  }

  @Test
  @DisplayName("Save document success")
  void testSaveDocumentSuccess() throws Exception {
    // Arrange
    when(knowledgeBaseService.saveDocument(any(DocumentRequestDto.class)))
        .thenReturn(successResponse);

    // Act & Assert
    mockMvc
        .perform(
            multipart("/knowledge-base/saveDocument")
                .file(testFile)
                .param("name", "Test Document")
                .param("type", "pdf")
                .param("description", "Test Description")
                .param("collectionName", "test_collection")
                .param("docType", "document")
                .param("topK", "5")
                .param("similarityThreshold", "0.8")
                .param("tags", "tag1,tag2")
                .param("integration", "test_integration")
                .param("tables", String.join(",", Arrays.asList("table1", "table2")))
                .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value(PromptConstants.SUCCESS))
        .andExpect(jsonPath("$.message").value("Operation completed successfully"))
        .andDo(print());

    verify(knowledgeBaseService).saveDocument(any(DocumentRequestDto.class));
  }

  @Test
  @DisplayName("Save document with validation error")
  void testSaveDocumentValidationError() throws Exception {
    // Arrange
    when(knowledgeBaseService.saveDocument(any(DocumentRequestDto.class)))
        .thenThrow(new BusinessException("Document validation failed"));

    // Act & Assert
    mockMvc
        .perform(
            multipart("/knowledge-base/saveDocument")
                .file(testFile)
                .param("name", "Test Document")
                .param("type", "pdf")
                .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(knowledgeBaseService).saveDocument(any(DocumentRequestDto.class));
  }

  @Test
  @DisplayName("Update document success")
  void testUpdateDocumentSuccess() throws Exception {
    // Arrange
    String documentId = "34567345";
    when(knowledgeBaseService.updateDocument(eq(documentId), any(DocumentRequestDto.class)))
        .thenReturn(successResponse);

    // Act & Assert
    mockMvc
        .perform(
            multipart("/knowledge-base/updateDocument/{id}", documentId)
                .file(testFile)
                .param("name", "Updated Document")
                .param("type", "pdf")
                .param("description", "Updated Description")
                .param("collectionName", "updated_collection")
                .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value(PromptConstants.SUCCESS))
        .andExpect(jsonPath("$.message").value("Operation completed successfully"))
        .andDo(print());

    verify(knowledgeBaseService).updateDocument(eq(documentId), any(DocumentRequestDto.class));
  }

  @Test
  @DisplayName("Update document with error")
  void testUpdateDocumentError() throws Exception {
    // Arrange
    String documentId = "9995678";
    when(knowledgeBaseService.updateDocument(eq(documentId), any(DocumentRequestDto.class)))
        .thenThrow(new BusinessException("Document not found"));

    // Act & Assert
    mockMvc
        .perform(
            multipart("/knowledge-base/updateDocument/{id}", documentId)
                .file(testFile)
                .param("name", "Updated Document")
                .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(knowledgeBaseService).updateDocument(eq(documentId), any(DocumentRequestDto.class));
  }

  @Test
  @DisplayName("Count knowledge base entries success")
  void testCountSuccess() throws Exception {
    // Arrange
    String filter = "test";
    Long expectedCount = 5L;
    when(knowledgeBaseService.count(filter)).thenReturn(expectedCount);

    // Act & Assert
    mockMvc
        .perform(
            get("/knowledge-base/count")
                .param("filter", filter)
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$").value(expectedCount))
        .andDo(print());

    verify(knowledgeBaseService).count(filter);
  }

  @Test
  @DisplayName("Count knowledge base entries with no filter")
  void testCountWithNoFilter() throws Exception {
    // Arrange
    Long expectedCount = 10L;
    when(knowledgeBaseService.count(null)).thenReturn(expectedCount);

    // Act & Assert
    mockMvc
        .perform(get("/knowledge-base/count").contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$").value(expectedCount));

    verify(knowledgeBaseService).count(null);
  }

  @Test
  @DisplayName("Search knowledge base entries success")
  void testSearchSuccess() throws Exception {
    // Arrange
    List<KnowledgeBaseResponseDto> expectedResults = Arrays.asList(knowledgeBaseResponseDto);
    when(knowledgeBaseService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
        .thenReturn(expectedResults);

    // Act & Assert
    mockMvc
        .perform(
            get("/knowledge-base/search")
                .param("filter", "test")
                .param("offset", "0")
                .param("size", "10")
                .param("orderBy", "name")
                .param("orderType", "asc")
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$[0].id").value(1))
        .andExpect(jsonPath("$[0].name").value("Test Knowledge Base"))
        .andExpect(jsonPath("$[0].description").value("Test Description"))
        .andExpect(jsonPath("$[0].type").value("database"))
        .andExpect(jsonPath("$[0].isContext").value(true))
        .andExpect(jsonPath("$[0].topK").value(5))
        .andExpect(jsonPath("$[0].similarityThreshold").value(0.8))
        .andDo(print());

    verify(knowledgeBaseService).search("test", 0, 10, "name", "asc");
  }

  @Test
  @DisplayName("Search knowledge base entries returns empty list")
  void testSearchReturnsEmpty() throws Exception {
    // Arrange
    when(knowledgeBaseService.search(anyString(), anyInt(), anyInt(), anyString(), anyString()))
        .thenReturn(Collections.emptyList());

    // Act & Assert
    mockMvc
        .perform(
            get("/knowledge-base/search")
                .param("filter", "nonexistent")
                .param("offset", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$").isArray())
        .andExpect(jsonPath("$", hasSize(0)));

    verify(knowledgeBaseService).search("nonexistent", 0, 10, null, null);
  }

  @Test
  @DisplayName("Get knowledge base entries by IDs success")
  void testGetKnowledgeBaseByIdsSuccess() throws Exception {
    // Arrange
    List<String> ids = Arrays.asList("1", "2");
    when(knowledgeBaseService.getKnowledgeBaseByIds(ids)).thenReturn(knowledgeBaseSdkDtos);

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/getKnowledgeBaseByIds")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(ids))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$").isArray())
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].id").value(1))
        .andExpect(jsonPath("$[0].name").value("Test KB 1"))
        .andExpect(jsonPath("$[0].type").value("document"))
        .andExpect(jsonPath("$[0].description").value("Test Description 1"))
        .andExpect(jsonPath("$[0].topK").value(5))
        .andExpect(jsonPath("$[0].similarityThreshold").value(0.8))
        .andExpect(jsonPath("$[0].isContext").value(true))
        .andExpect(jsonPath("$[1].id").value(2))
        .andExpect(jsonPath("$[1].name").value("Test KB 2"))
        .andExpect(jsonPath("$[1].type").value("website"))
        .andExpect(jsonPath("$[1].description").value("Test Description 2"))
        .andExpect(jsonPath("$[1].topK").value(10))
        .andExpect(jsonPath("$[1].similarityThreshold").value(0.7))
        .andExpect(jsonPath("$[1].isContext").value(false))
        .andDo(print());

    verify(knowledgeBaseService).getKnowledgeBaseByIds(ids);
  }

  @Test
  @DisplayName("Get knowledge base entries by IDs returns empty list")
  void testGetKnowledgeBaseByIdsEmpty() throws Exception {
    // Arrange
    List<String> ids = Arrays.asList("999", "998");
    when(knowledgeBaseService.getKnowledgeBaseByIds(ids)).thenReturn(Collections.emptyList());

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/getKnowledgeBaseByIds")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(ids))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$").isArray())
        .andExpect(jsonPath("$", hasSize(0)));

    verify(knowledgeBaseService).getKnowledgeBaseByIds(ids);
  }

  @Test
  @DisplayName("Update tags by ID success")
  void testUpdateTagByIdSuccess() throws Exception {
    // Arrange
    String id = "1";
    Map<String, String> tags = Map.of("tags", "tag1,tag2,tag3");
    Map<String, String> expectedResponse =
        Map.of("status", "success", "message", "Tags updated successfully");
    when(knowledgeBaseService.updateTagById(id, tags)).thenReturn(expectedResponse);

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/updateTagById/{id}", id)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(tags))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value(PromptConstants.SUCCESS))
        .andExpect(jsonPath("$.message").value("Tags updated successfully"))
        .andDo(print());

    verify(knowledgeBaseService).updateTagById(id, tags);
  }

  @Test
  @DisplayName("Update tags by ID with error")
  void testUpdateTagByIdError() throws Exception {
    // Arrange
    String id = "999";
    Map<String, String> tags = Map.of("tags", "tag1,tag2,tag3");
    when(knowledgeBaseService.updateTagById(id, tags))
        .thenThrow(new BusinessException("Knowledge base entry not found"));

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/updateTagById/{id}", id)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(tags))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(knowledgeBaseService).updateTagById(id, tags);
  }

  @Test
  @DisplayName("Get tables name success")
  void testGetTablesNameSuccess() throws Exception {
    // Arrange
    Map<String, String> requestBody = Map.of("name", "test_database");
    List<String> expectedTables = Arrays.asList("table1", "table2", "table3");
    when(knowledgeBaseService.getTablesName("test_database")).thenReturn(expectedTables);

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/show-tables")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$").isArray())
        .andExpect(jsonPath("$", hasSize(3)))
        .andExpect(jsonPath("$[0]").value("table1"))
        .andExpect(jsonPath("$[1]").value("table2"))
        .andExpect(jsonPath("$[2]").value("table3"))
        .andDo(print());

    verify(knowledgeBaseService).getTablesName("test_database");
  }

  @Test
  @DisplayName("Get tables name returns empty list")
  void testGetTablesNameEmpty() throws Exception {
    // Arrange
    Map<String, String> requestBody = Map.of("name", "empty_database");
    when(knowledgeBaseService.getTablesName("empty_database")).thenReturn(Collections.emptyList());

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/show-tables")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestBody))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$").isArray())
        .andExpect(jsonPath("$", hasSize(0)));

    verify(knowledgeBaseService).getTablesName("empty_database");
  }

  @Test
  @DisplayName("Check website URL exists - URL found")
  void testExistsWebsiteUrlFound() throws Exception {
    // Arrange
    Map<String, String> requestMap = Map.of("webSiteUrl", "https://example.com");
    Map<String, Object> expectedResponse =
        Map.of("exists", true, "message", "Website URL already exists");
    when(knowledgeBaseService.existsWebsiteUrl("https://example.com")).thenReturn(expectedResponse);

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/existsWebsiteUrl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestMap))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.exists").value(true))
        .andExpect(jsonPath("$.message").value("Website URL already exists"))
        .andDo(print());

    verify(knowledgeBaseService).existsWebsiteUrl("https://example.com");
  }

  @Test
  @DisplayName("Check website URL exists - URL not found")
  void testExistsWebsiteUrlNotFound() throws Exception {
    // Arrange
    Map<String, String> requestMap = Map.of("webSiteUrl", "https://newsite.com");
    Map<String, Object> expectedResponse =
        Map.of("exists", false, "message", "Website URL does not exist");
    when(knowledgeBaseService.existsWebsiteUrl("https://newsite.com")).thenReturn(expectedResponse);

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/existsWebsiteUrl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestMap))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.exists").value(false))
        .andExpect(jsonPath("$.message").value("Website URL does not exist"));

    verify(knowledgeBaseService).existsWebsiteUrl("https://newsite.com");
  }

  @Test
  @DisplayName("Check website URL exists with error")
  void testExistsWebsiteUrlError() throws Exception {
    // Arrange
    Map<String, String> requestMap = Map.of("webSiteUrl", "invalid-url");
    when(knowledgeBaseService.existsWebsiteUrl("invalid-url"))
        .thenThrow(new BusinessException("Invalid URL format"));

    // Act & Assert
    mockMvc
        .perform(
            post("/knowledge-base/existsWebsiteUrl")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestMap))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(knowledgeBaseService).existsWebsiteUrl("invalid-url");
  }

  @Test
  @DisplayName("Delete knowledge base entry by ID success")
  void testDeleteById() throws Exception {
    String id = "123";
    Map<String, String> mockResponse = Map.of("message", "deleted");

    when(knowledgeBaseService.softDelete(id)).thenReturn(mockResponse);

    mockMvc
        .perform(
            get("/knowledge-base/deleteById/" + id)
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.message").value("deleted"));
    verify(knowledgeBaseService).softDelete(id);
  }

  @Test
  void testExportKnowledgeBases() throws Exception {
    // Mocking service
    ByteArrayResource resource = new ByteArrayResource("csv data".getBytes());
    when(knowledgeBaseService.exportKnowledgeBases(List.of("1", "2", "3")))
        .thenReturn(ResponseEntity.ok(resource));

    // Perform REST call
    String requestBody = "[1,2,3]";
    mockMvc
        .perform(
            post("/knowledge-base/export")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .accept(MediaType.APPLICATION_OCTET_STREAM))
        .andExpect(status().isOk())
        .andExpect(content().bytes("csv data".getBytes()));
  }

  @Test
  void testImportKnowledgeBases() throws Exception {
    MockMultipartFile mockFile =
        new MockMultipartFile("file", "test.csv", "text/csv", "some,kb,data".getBytes());

    ByteArrayResource importedResource = new ByteArrayResource("import result".getBytes());
    when(knowledgeBaseService.importKnowledgeBases(any()))
        .thenReturn(ResponseEntity.ok(importedResource));

    mockMvc
        .perform(multipart("/knowledge-base/import").file(mockFile))
        .andExpect(status().isOk())
        .andExpect(content().bytes("import result".getBytes()));
  }

  @Test
  void testUpdateReturnDirect() throws Exception {
    Map<String, String> responseMap = Map.of("status", "updated");

    when(knowledgeBaseService.updateReturnDirect("123", true)).thenReturn(responseMap);

    String requestBody =
        """
                {
                  "id": "123",
                  "value": "true"
                }
                """;

    mockMvc
        .perform(
            post("/knowledge-base/updateReturnDirect")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("updated"));
  }

  @Test
  void testExistsKnowledgeBaseName() throws Exception {
    Map<String, Boolean> responseMap = Map.of("exists", true);

    when(knowledgeBaseService.existsKnowledgeBaseName("test-kb")).thenReturn(responseMap);

    mockMvc
        .perform(get("/knowledge-base/existsKnowledgeBaseName/test-kb").param("name", "test-kb"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.exists").value(true));
  }

  @Test
  void testSaveWebSite() throws Exception {
    Map<String, String> responseMap = Map.of("status", "saved");

    when(knowledgeBaseService.saveWebSite(any(DocumentRequestDto.class))).thenReturn(responseMap);

    String requestJson =
        """
            {
              "websiteUrl": "https://example.com",
              "content": "This is an example content."
            }
            """;

    mockMvc
        .perform(
            post("/knowledge-base/saveWebSite")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("saved"));
  }

  @Test
  void testSaveContent() throws Exception {
    Map<String, String> responseMap = Map.of("status", "content saved");

    when(knowledgeBaseService.saveContent(any(DocumentRequestDto.class))).thenReturn(responseMap);

    String requestJson =
        """
        {
          "websiteUrl": "https://example.com",
          "name": "Example",
          "content": "Sample content"
        }
        """;

    mockMvc
        .perform(
            post("/knowledge-base/saveContent")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
        .andDo(print()) // <-- Add this to see error details in logs
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value("content saved"));
  }

  // Test for milvusImportCsv
  @Test
  void testMilvusImportCsv() throws Exception {
    MockMultipartFile mockFile =
        new MockMultipartFile("file", "test.csv", "text/csv", "csv,data,here".getBytes());

    when(knowledgeBaseService.milvusImportCsv(any(MultipartFile.class)))
        .thenReturn(ResponseEntity.ok("import success"));

    mockMvc
        .perform(multipart("/knowledge-base/milvusImportCsv").file(mockFile))
        .andExpect(status().isOk())
        .andExpect(content().string("import success"));
  }

  // Test for getAnswer
  @Test
  void testGetAnswer() throws Exception {
    Map<String, String> responseMap = Map.of("answer", "This is the answer.");

    when(knowledgeBaseService.getAnswer("kb1", "What is AI?")).thenReturn(responseMap);

    String requestJson =
        """
            {
              "knowledgeBaseName": "kb1",
              "userQuestion": "What is AI?"
            }
            """;

    mockMvc
        .perform(
            post("/knowledge-base/getAnswer")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.answer").value("This is the answer."));
  }
}
