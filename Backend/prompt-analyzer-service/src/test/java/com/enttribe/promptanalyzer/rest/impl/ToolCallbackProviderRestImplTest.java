/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.service.ToolService;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;

@WebMvcTest(ToolCallbackProviderRestImpl.class)
class ToolCallbackProviderRestImplTest {

  @Mock private ToolService toolService;

  @InjectMocks private ToolCallbackProviderRestImpl toolCallbackProviderRest;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @InjectMocks private ToolRestImpl toolRest;

  @Test
  void testGetToolCallbackProvider() {
    List<HashMap<String, String>> expected =
        List.of(
            new HashMap<>(Map.of("toolName", "ToolA")), new HashMap<>(Map.of("toolName", "ToolB")));

    when(toolService.getToolCallbackProvider()).thenReturn(expected);

    List<HashMap<String, String>> result = toolCallbackProviderRest.getToolCallbackProvider();
    assertEquals(expected, result, "Tool callback provider list should match expected");
    verify(toolService, times(1)).getToolCallbackProvider();
  }

  @Test
  void testGetToolByName() {
    String toolName = "exampleTool";
    ToolDto expected = new ToolDto();
    when(toolService.getToolByName(toolName)).thenReturn(expected);

    ToolDto actual = toolRest.getToolByName(toolName);

    assertEquals(expected, actual, "ToolDto from getToolByName should match expected");
    verify(toolService).getToolByName(toolName);
  }
}
