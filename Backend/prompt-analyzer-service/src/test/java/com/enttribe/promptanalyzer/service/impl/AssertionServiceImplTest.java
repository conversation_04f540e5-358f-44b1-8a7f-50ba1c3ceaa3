/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dao.AssertionDao;
import com.enttribe.promptanalyzer.dto.assertion.AssertionResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Assertion;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

class AssertionServiceImplTest {

  @InjectMocks private AssertionServiceImpl assertionService;

  @Mock private AssertionDao assertionDao;

  @Mock private CustomFilter customFilter;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testSearchAssertionSuccess() {
    String filter = "field=value";
    List<Assertion> mockList = List.of(new Assertion(), new Assertion());

    when(customFilter.searchByFilter(
            ArgumentMatchers.eq(Assertion.class), eq(filter), any(), any(), any(), any()))
        .thenReturn(mockList);

    List<AssertionResponseDto> result =
        assertionService.searchAssertion(filter, 0, 10, "createdTime", "desc");

    assertEquals(2, result.size(), "Assertion result list size should match expected");
  }

  @Test
  void testSearchAssertionException() {
    when(customFilter.searchByFilter(any(), any(), any(), any(), any(), any()))
        .thenThrow(new RuntimeException("filter fail"));

    assertThrows(
        BusinessException.class,
        () -> assertionService.searchAssertion("fail", 0, 10, null, null),
        "Expected BusinessException when a generic error occurs in searchAssertion");
  }
}
