/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteResponseDto;
import com.enttribe.promptanalyzer.rest.impl.TestSuiteRestImpl;
import com.enttribe.promptanalyzer.service.TestSuiteService;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;

@ExtendWith(MockitoExtension.class)
class TestSuiteRestImplTest {

  @Mock private TestSuiteService testSuiteService;

  @InjectMocks private TestSuiteRestImpl testSuiteRest;

  @Test
  void testCreate() {
    TestSuiteRequestDto dto = new TestSuiteRequestDto();
    Map<String, String> expected = Map.of("status", "created");
    when(testSuiteService.create(dto)).thenReturn(expected);

    Map<String, String> response = testSuiteRest.create(dto);
    assertEquals("created", response.get("status"), "TestSuite create status should be 'created'");
  }

  @Test
  void testUpdate() {
    TestSuiteRequestDto dto = new TestSuiteRequestDto();
    Map<String, String> expected = Map.of("status", "updated");
    when(testSuiteService.update(dto)).thenReturn(expected);

    Map<String, String> response = testSuiteRest.update(dto);
    assertEquals("updated", response.get("status"), "TestSuite update status should be 'updated'");
  }

  @Test
  void testSearch() {
    List<TestSuiteResponseDto> expected = List.of(new TestSuiteResponseDto());
    when(testSuiteService.search("filter", 0, 10, "id", "asc")).thenReturn(expected);

    List<TestSuiteResponseDto> response = testSuiteRest.search("filter", 0, 10, "id", "asc");
    assertEquals(1, response.size(), "TestSuite search result size should match expected");
  }

  @Test
  void testCount() {
    when(testSuiteService.count("filter")).thenReturn(5L);

    Long response = testSuiteRest.count("filter");
    assertEquals(5L, response, "TestSuite count should match expected");
  }

  @Test
  void testDeleteById() {
    String nanoId = "abc123";
    Map<String, String> expected = Map.of("status", "deleted");
    when(testSuiteService.deleteById(nanoId)).thenReturn(expected);

    Map<String, String> response = testSuiteRest.deleteById(nanoId);
    assertEquals("deleted", response.get("status"), "TestSuite delete status should be 'deleted'");
  }

  @Test
  void testAddAgentTestCasesToSuite() {
    TestSuiteRequestDto dto = new TestSuiteRequestDto();
    Map<String, String> expected = Map.of("status", "added");
    when(testSuiteService.addAgentTestCasesToSuite(dto)).thenReturn(expected);

    Map<String, String> response = testSuiteRest.addAgentTestCasesToSuite(dto);
    assertEquals(
        "added",
        response.get("status"),
        "TestSuite addAgentTestCasesToSuite status should be 'added'");
  }

  @Test
  void testExportTestCasesBySuiteId() {
    Resource resource = new ByteArrayResource("mock content".getBytes());
    when(testSuiteService.exportTestCasesBySuiteId("suiteId"))
        .thenReturn(ResponseEntity.ok(resource));

    ResponseEntity<Resource> response = testSuiteRest.exportTestCasesBySuiteId("suiteId");

    assertEquals(
        200, response.getStatusCode().value(), "Exported test cases response status should be 200");
  }

  @Test
  void testImportTestCasesToSuite() throws IOException {
    MockMultipartFile file =
        new MockMultipartFile("file", "test.csv", "text/csv", "data".getBytes());
    Resource resource = new ByteArrayResource("imported".getBytes());
    when(testSuiteService.importTestCasesToSuite("suiteId", file))
        .thenReturn(ResponseEntity.ok(resource));

    ResponseEntity<Resource> response = testSuiteRest.importTestCasesToSuite("suiteId", file);
    assertEquals(
        200, response.getStatusCode().value(), "Imported test cases response status should be 200");
  }
}
