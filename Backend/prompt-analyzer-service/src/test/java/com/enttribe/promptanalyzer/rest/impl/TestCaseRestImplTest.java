/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseResponseDto;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.service.TestCaseService;
import com.enttribe.promptanalyzer.util.TestCaseUtils;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;

@WebMvcTest(TestCaseRestImpl.class)
class TestCaseRestImplTest {

  @InjectMocks private TestCaseRestImpl testCaseRest;

  @Mock private TestCaseService testCaseService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testCreate() {
    TestCaseRequestDto dto = new TestCaseRequestDto();
    Map<String, String> expected = Map.of(PromptConstants.STATUS, PromptConstants.SUCCESS);

    when(testCaseService.create(dto)).thenReturn(expected);

    Map<String, String> actual = testCaseRest.create(dto);

    assertEquals(expected, actual, "TestCase create result map should match expected");
    verify(testCaseService).create(dto);
  }

  @Test
  void testCreateBatch() {
    List<TestCaseRequestDto> dtoList = List.of(new TestCaseRequestDto());
    Map<String, String> expected = Map.of(PromptConstants.STATUS, PromptConstants.SUCCESS);

    when(testCaseService.createBatch(dtoList)).thenReturn(expected);

    Map<String, String> actual = testCaseRest.createBatch(dtoList);

    assertEquals(expected, actual, "TestCase create batch result map should match expected");
    verify(testCaseService).createBatch(dtoList);
  }

  @Test
  void testUpdate() {
    TestCaseRequestDto updated = new TestCaseRequestDto();
    Map<String, String> expected = Map.of(PromptConstants.STATUS, "updated");

    when(testCaseService.update(updated)).thenReturn(expected);

    Map<String, String> actual = testCaseRest.update(updated);

    assertEquals(expected, actual, "TestCase update result map should match expected");
    verify(testCaseService).update(updated);
  }

  @Test
  void testDeleteTestcase() {
    Map<String, String> body = Map.of("id", "1");
    Map<String, String> expected = Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);

    when(testCaseService.deleteTestcase("1")).thenReturn(expected);

    Map<String, String> actual = testCaseRest.deleteTestcase(body);

    assertEquals(expected, actual, "TestCase delete result map should match expected");
    verify(testCaseService).deleteTestcase("1");
  }

  @Test
  void testSearch() {
    // Given
    List<TestCase> testCases = List.of(new TestCase());
    List<TestCaseResponseDto> expected = List.of(new TestCaseResponseDto());

    // Mock static method
    try (MockedStatic<TestCaseUtils> mockedUtils = mockStatic(TestCaseUtils.class)) {
      mockedUtils.when(() -> TestCaseUtils.getTestCaseDtoList(testCases)).thenReturn(expected);

      // When
      List<TestCaseResponseDto> result = testCaseRest.search("filter", 0, 10, "name", "ASC");

      // Then
      assertEquals(expected, result, "TestCase search result list should match expected");
      verify(testCaseService).search("filter", 0, 10, "name", "ASC");
      mockedUtils.verify(() -> TestCaseUtils.getTestCaseDtoList(testCases));
    }
  }

  @Test
  void testCount() {
    when(testCaseService.count("filter")).thenReturn(5L);

    Long count = testCaseRest.count("filter");

    assertEquals(5L, count, "TestCase count should match expected");
    verify(testCaseService).count("filter");
  }

  @Test
  void testExportTestCasesByIds() {
    List<String> ids = List.of("1", "2");
    ResponseEntity<Resource> expected = ResponseEntity.ok(new ByteArrayResource(new byte[0]));

    when(testCaseService.exportTestCasesByIds(ids)).thenReturn(expected);

    ResponseEntity<Resource> actual = testCaseRest.exportTestCasesByIds(ids);

    assertEquals(expected, actual, "TestCase export result should match expected");
    verify(testCaseService).exportTestCasesByIds(ids);
  }

  @Test
  void testImportTestCases() {
    MockMultipartFile file =
        new MockMultipartFile("file", "test.csv", "text/csv", "id,name\n1,test".getBytes());
    ResponseEntity<Resource> expected =
        ResponseEntity.ok(new ByteArrayResource("imported".getBytes()));

    when(testCaseService.importTestCase(file)).thenReturn(expected);

    ResponseEntity<Resource> actual = testCaseRest.importTestCases(file);

    assertEquals(expected, actual, "TestCase import result should match expected");
    verify(testCaseService).importTestCase(file);
  }
}
