/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;
import com.enttribe.promptanalyzer.ai.dto.MessageDto;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import java.util.*;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class LlmApiServiceImplTest {
  @Mock InferenceManager inferenceManager;
  @Mock PromptDao promptRepository;
  @Mock OpenAiApi openAiApi;

  @Mock private OpenAiChatModel openAiChatModel;

  @Mock Resource freshSystemPrompt;

  @Mock Resource freshUserPrompt;

  @InjectMocks LlmApiServiceImpl llmApiServiceImpl;

  private ChatCompletionRequestDto requestDto;

  @BeforeEach
  void testSetUp() {

    requestDto = new ChatCompletionRequestDto();
    requestDto.setMaxTokens(100);
    requestDto.setModel("gpt-3.5-turbo");
    requestDto.setTemperature(0.7);
    requestDto.setTopP(0.9);
    requestDto.setJsonMode(true);
    requestDto.setProvider("openai");
    requestDto.setMessages(List.of(new MessageDto("user", "Hello, how are you?")));
  }

  @Test
  @DisplayName("Chat Completion Success")
  void testChatCompletionSuccess() {
    OpenAiApi.ChatCompletionMessage mockMessage = mock(OpenAiApi.ChatCompletionMessage.class);
    when(mockMessage.content()).thenReturn("I'm good, how about you?");
    when(mockMessage.role()).thenReturn(OpenAiApi.ChatCompletionMessage.Role.ASSISTANT);

    OpenAiApi.ChatCompletion.Choice mockChoice = mock(OpenAiApi.ChatCompletion.Choice.class);
    when(mockChoice.message()).thenReturn(mockMessage);

    OpenAiApi.ChatCompletion mockCompletion = mock(OpenAiApi.ChatCompletion.class);
    when(mockCompletion.choices()).thenReturn(List.of(mockChoice));

    ResponseEntity<OpenAiApi.ChatCompletion> mockResponse =
        new ResponseEntity<>(mockCompletion, HttpStatus.OK);

    when(inferenceManager.getOpenAiApiByProvider("openai")).thenReturn(openAiApi);
    when(openAiApi.chatCompletionEntity(any(OpenAiApi.ChatCompletionRequest.class)))
        .thenReturn(mockResponse);

    Object result = llmApiServiceImpl.chatCompletion(requestDto);
    assertNotNull("Result should not be null", result);
    assertTrue(
        "Result should be instance of OpenAiApi.ChatCompletionMessage",
        result instanceof OpenAiApi.ChatCompletionMessage);
    assertEquals(
        "Chat completion message content should match expected",
        "I'm good, how about you?",
        ((OpenAiApi.ChatCompletionMessage) result).content());
    verify(inferenceManager, times(1)).getOpenAiApiByProvider("openai");
    verify(openAiApi, times(1)).chatCompletionEntity(any(OpenAiApi.ChatCompletionRequest.class));
  }

  @Test
  @DisplayName("Chat Completion Success - Default Provider")
  void testChatCompletionWithDefaultProvider() {
    requestDto.setProvider(null);
    OpenAiApi.ChatCompletionMessage mockMessage = mock(OpenAiApi.ChatCompletionMessage.class);
    OpenAiApi.ChatCompletion.Choice mockChoice = mock(OpenAiApi.ChatCompletion.Choice.class);
    when(mockChoice.message()).thenReturn(mockMessage);
    OpenAiApi.ChatCompletion mockCompletion = mock(OpenAiApi.ChatCompletion.class);
    when(mockCompletion.choices()).thenReturn(List.of(mockChoice));
    ResponseEntity<OpenAiApi.ChatCompletion> mockResponse =
        new ResponseEntity<>(mockCompletion, HttpStatus.OK);

    when(inferenceManager.getOpenAiApiByProvider("groq")).thenReturn(openAiApi);
    when(openAiApi.chatCompletionEntity(any(OpenAiApi.ChatCompletionRequest.class)))
        .thenReturn(mockResponse);
    Object result = llmApiServiceImpl.chatCompletion(requestDto);
    assertNotNull(result);
    verify(inferenceManager, times(1)).getOpenAiApiByProvider("groq");
    verify(openAiApi, times(1)).chatCompletionEntity(any(OpenAiApi.ChatCompletionRequest.class));
  }

  @Test
  @DisplayName("Chat Completion - Empty Response")
  void testChatCompletionWithEmptyResponse() {
    OpenAiApi.ChatCompletion mockCompletion = mock(OpenAiApi.ChatCompletion.class);
    when(mockCompletion.choices()).thenReturn(List.of());
    ResponseEntity<OpenAiApi.ChatCompletion> mockResponse =
        new ResponseEntity<>(mockCompletion, HttpStatus.OK);
    when(inferenceManager.getOpenAiApiByProvider("openai")).thenReturn(openAiApi);
    when(openAiApi.chatCompletionEntity(any(OpenAiApi.ChatCompletionRequest.class)))
        .thenReturn(mockResponse);
    assertThrows(
        "Expected BusinessException when chat completion response is empty",
        BusinessException.class,
        () -> llmApiServiceImpl.chatCompletion(requestDto));
    verify(inferenceManager, times(1)).getOpenAiApiByProvider("openai");
    verify(openAiApi, times(1)).chatCompletionEntity(any(OpenAiApi.ChatCompletionRequest.class));
    verify(mockCompletion, times(1)).choices();
  }

  @Test
  @DisplayName("Chat Completion - Custom Configuration")
  void testChatCompletionWithCustomConfiguration() {
    requestDto.setTemperature(0.8);
    requestDto.setTopP(0.9);
    requestDto.setMaxTokens(200);

    OpenAiApi.ChatCompletionMessage mockMessage = mock(OpenAiApi.ChatCompletionMessage.class);
    OpenAiApi.ChatCompletion.Choice mockChoice = mock(OpenAiApi.ChatCompletion.Choice.class);
    when(mockChoice.message()).thenReturn(mockMessage);
    OpenAiApi.ChatCompletion mockCompletion = mock(OpenAiApi.ChatCompletion.class);
    when(mockCompletion.choices()).thenReturn(List.of(mockChoice));
    ResponseEntity<OpenAiApi.ChatCompletion> mockResponse =
        new ResponseEntity<>(mockCompletion, HttpStatus.OK);
    when(inferenceManager.getOpenAiApiByProvider("openai")).thenReturn(openAiApi);
    when(openAiApi.chatCompletionEntity(any(OpenAiApi.ChatCompletionRequest.class)))
        .thenReturn(mockResponse);
    Object result = llmApiServiceImpl.chatCompletion(requestDto);
    assertNotNull(result);
    verify(inferenceManager, times(1)).getOpenAiApiByProvider("openai");
    verify(openAiApi, times(1))
        .chatCompletionEntity(
            argThat(
                request ->
                    request.temperature() == 0.8
                        && request.topP() == 0.9
                        && request.maxTokens() == 200));
    verify(mockCompletion, times(1)).choices();
    verify(mockChoice, times(1)).message();
  }

  @Test
  @DisplayName("Execute Prompt Success - Normal Flow")
  void testExecutePromptSuccess() {
    String promptId = "test-prompt-id";
    Map<String, Object> variableMap = new HashMap<>();
    variableMap.put("name", "John");
    LlmModel llmModel = new LlmModel();
    llmModel.setModel("gpt-3.5-turbo");
    llmModel.setProvider("openai");
    Prompt mockPrompt = new Prompt();
    mockPrompt.setPromptId(promptId);
    mockPrompt.setLlmModel(llmModel);
    mockPrompt.setTemperature(0.7);
    mockPrompt.setMaxToken(100);
    mockPrompt.setTopP(0.9);
    Message message = new Message();
    message.setRole("user");
    message.setContent("Hello, ${name}");
    mockPrompt.setMessages(List.of(message));

    when(promptRepository.getPromptByPromptId(promptId)).thenReturn(mockPrompt);
    when(inferenceManager.getOpenAiApiByProvider(anyString())).thenReturn(openAiApi);

    OpenAiApi.ChatCompletionMessage chatCompletionMessage =
        mock(OpenAiApi.ChatCompletionMessage.class);
    when(chatCompletionMessage.content()).thenReturn("Hello John!");
    when(chatCompletionMessage.role()).thenReturn(OpenAiApi.ChatCompletionMessage.Role.ASSISTANT);

    OpenAiApi.ChatCompletion.Choice choice = mock(OpenAiApi.ChatCompletion.Choice.class);
    when(choice.message()).thenReturn(chatCompletionMessage);

    OpenAiApi.ChatCompletion chatCompletion = mock(OpenAiApi.ChatCompletion.class);
    when(chatCompletion.choices()).thenReturn(List.of(choice));

    ResponseEntity<OpenAiApi.ChatCompletion> responseEntity =
        new ResponseEntity<>(chatCompletion, HttpStatus.OK);
    when(openAiApi.chatCompletionEntity(any())).thenReturn(responseEntity);
    Object result = llmApiServiceImpl.executePrompt(promptId, variableMap, false);
    assertNotNull("Result should not be null", result);
    assertTrue("Result should be instance of String", result instanceof String);
    assertEquals("Chat completion result should match expected", "Hello John!", result);
    verify(promptRepository).getPromptByPromptId(promptId);
    verify(inferenceManager).getOpenAiApiByProvider(anyString());
    verify(openAiApi).chatCompletionEntity(any());
  }

  @Test
  @DisplayName("Execute Prompt - With JSON Format")
  void testExecutePromptWithJsonFormat() {
    String promptId = "test-prompt-id";
    Map<String, Object> variableMap = new HashMap<>();
    variableMap.put("name", "John");

    LlmModel llmModel = new LlmModel();
    llmModel.setModel("gpt-3.5-turbo");
    llmModel.setProvider("openai");

    Prompt mockPrompt = new Prompt();
    mockPrompt.setPromptId(promptId);
    mockPrompt.setLlmModel(llmModel);
    mockPrompt.setTemperature(0.7);
    mockPrompt.setMaxToken(100);
    mockPrompt.setTopP(0.9);
    mockPrompt.setJsonMode(true);

    Message message = new Message();
    message.setRole("user");
    message.setContent("Hello, ${name}");
    mockPrompt.setMessages(List.of(message));

    when(promptRepository.getPromptByPromptId(promptId)).thenReturn(mockPrompt);
    when(inferenceManager.getOpenAiApiByProvider(anyString())).thenReturn(openAiApi);

    OpenAiApi.ChatCompletionMessage chatCompletionMessage =
        mock(OpenAiApi.ChatCompletionMessage.class);
    when(chatCompletionMessage.content()).thenReturn("{\"greeting\": \"Hello John!\"}");
    when(chatCompletionMessage.role()).thenReturn(OpenAiApi.ChatCompletionMessage.Role.ASSISTANT);

    OpenAiApi.ChatCompletion.Choice choice = mock(OpenAiApi.ChatCompletion.Choice.class);
    when(choice.message()).thenReturn(chatCompletionMessage);

    OpenAiApi.ChatCompletion chatCompletion = mock(OpenAiApi.ChatCompletion.class);
    when(chatCompletion.choices()).thenReturn(List.of(choice));

    ResponseEntity<OpenAiApi.ChatCompletion> responseEntity =
        new ResponseEntity<>(chatCompletion, HttpStatus.OK);
    when(openAiApi.chatCompletionEntity(any())).thenReturn(responseEntity);

    Object result = llmApiServiceImpl.executePrompt(promptId, variableMap, true);

    assertNotNull("Result should not be null", result);
    assertTrue("Result should be instance of String", result instanceof String);
    assertTrue(
        "Result string should contain 'Hello John!'", ((String) result).contains("Hello John!"));
    verify(promptRepository).getPromptByPromptId(promptId);
    verify(inferenceManager).getOpenAiApiByProvider(anyString());
    verify(openAiApi).chatCompletionEntity(any());
  }

  @Test
  @DisplayName("Execute Prompt - Prompt Not Found")
  void testExecutePromptNotFound() {
    String promptId = "non-existent-id";
    Map<String, Object> variableMap = new HashMap<>();

    when(promptRepository.getPromptByPromptId(promptId)).thenReturn(null);

    BusinessException exception =
        assertThrows(
            "Expected BusinessException when prompt is not found",
            BusinessException.class,
            () -> llmApiServiceImpl.executePrompt(promptId, variableMap, false));

    assertTrue(
        "Exception message should contain 'prompt is not found'",
        exception.getMessage().contains("prompt is not found"));
    verify(promptRepository).getPromptByPromptId(promptId);
  }
}
