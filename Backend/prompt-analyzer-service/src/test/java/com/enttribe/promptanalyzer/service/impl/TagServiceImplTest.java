/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.TagDao;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Tag;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TagServiceImplTest {

  private static final String TEST_NANO_ID = "r5JU_FgINYB-bzYH5Ltqx";

  @Mock private CustomFilter customFilter;

  @Mock private TagDao tagDao;

  @InjectMocks private TagServiceImpl tagServiceImpl;

  private TagRequestDto testTagRequestDto;
  private Tag testTag;

  @BeforeEach
  void setUp() {
    testTagRequestDto = new TagRequestDto();
    testTagRequestDto.setId(TEST_NANO_ID);
    testTagRequestDto.setName("Test Tag");
    testTagRequestDto.setType("Test Type");

    testTag = new Tag();
    testTag.setId(1);
    testTag.setNanoId(TEST_NANO_ID);
    testTag.setName("Test Tag");
    testTag.setType("Test Type");
  }

  @Test
  @DisplayName("Search tags success case")
  void testTagSearchSuccess() {
    List<Tag> mockTags = Arrays.asList(testTag);
    when(customFilter.searchByFilter(
            eq(Tag.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(mockTags);
    List<TagResponseDto> result = tagServiceImpl.search("filter", 0, 10, "name", "ASC");
    assertNotNull(result, "Tag result should not be null");
    assertEquals(1, result.size(), "Tag search result size should be 1");
    verify(customFilter).searchByFilter(Tag.class, "filter", "name", "ASC", 0, 10);
  }

  @Test
  @DisplayName("Search tags failure case")
  void testTagSearchFailure() {
    when(customFilter.searchByFilter(
            eq(Tag.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenThrow(new RuntimeException("Search failed"));
    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> tagServiceImpl.search("filter", 0, 0, "orderBy", "orderType"));
    assertEquals(
        "Failed to search tags",
        exception.getMessage(),
        "Tag search exception message should match expected");
  }

  @Test
  @DisplayName("Count tags success case")
  void testTagCountSuccess() {
    when(customFilter.countByFilter(eq(Tag.class), anyString())).thenReturn(1L);
    Long result = tagServiceImpl.count("filter");
    assertNotNull(result, "Tag result should not be null");
    assertEquals(1L, result, "Tag count should match expected");
  }

  @Test
  @DisplayName("Save tag success case")
  void testTagSaveSuccess() {
    when(tagDao.save(any(Tag.class))).thenReturn(testTag);
    Map<String, String> result = tagServiceImpl.save(testTagRequestDto);
    assertNotNull(result, "Tag result should not be null");
    assertEquals(
        Map.of("status", PromptConstants.SUCCESS),
        result,
        "Tag save result map should match expected");
    verify(tagDao, times(1)).save(any(Tag.class));
  }

  @Test
  @DisplayName("Save tag failure case")
  void testTagSaveFailure() {
    TagRequestDto requestDto = new TagRequestDto();
    when(tagDao.save(any(Tag.class))).thenThrow(new RuntimeException("Database error"));
    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> tagServiceImpl.save(requestDto),
            "Expected BusinessException when saving tag with invalid data");
    assertEquals(
        "Failed to create tag",
        exception.getMessage(),
        "Tag create exception message should match expected");
    verify(tagDao, times(1)).save(any(Tag.class));
  }

  @Test
  @DisplayName("Update tag success case")
  void testTagUpdateSuccess() {
    when(tagDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(testTag));
    when(tagDao.save(any(Tag.class))).thenReturn(testTag);
    Map<String, String> result = tagServiceImpl.update(testTagRequestDto);
    assertNotNull(result, "Tag result should not be null");
    assertEquals(
        Map.of("status", PromptConstants.SUCCESS),
        result,
        "Tag update result map should match expected");
    verify(tagDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(tagDao, times(1)).save(any(Tag.class));
  }

  @Test
  @DisplayName("Update non-existent tag failure case")
  void testTagUpdateFailure() {
    TagRequestDto requestDto = new TagRequestDto();
    requestDto.setId(TEST_NANO_ID);
    when(tagDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.empty());
    ResourceNotFoundException exception =
        assertThrows(
            ResourceNotFoundException.class,
            () -> tagServiceImpl.update(requestDto),
            "Expected ResourceNotFoundException when updating non-existent tag");
    assertEquals(
        "Tag not found with ID: " + TEST_NANO_ID,
        exception.getMessage(),
        "Tag not found exception message should match expected");
    verify(tagDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(tagDao, never()).save(any(Tag.class));
  }

  @Test
  @DisplayName("Soft delete tag success case")
  void testTagSoftDeleteSuccess() {
    when(tagDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(testTag));
    when(tagDao.save(any(Tag.class))).thenReturn(testTag);
    Map<String, String> result = tagServiceImpl.softDelete(TEST_NANO_ID);
    assertNotNull(result, "Tag result should not be null");
    assertEquals(
        Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS),
        result,
        "Tag delete result map should match expected");
    verify(tagDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(tagDao, times(1)).save(any(Tag.class));
  }

  @Test
  @DisplayName("Soft delete non-existent tag failure case")
  void testTagSoftDeleteFailure() {
    when(tagDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.empty());
    ResourceNotFoundException exception =
        assertThrows(
            ResourceNotFoundException.class, () -> tagServiceImpl.softDelete(TEST_NANO_ID));
    assertEquals(
        "Tag not found",
        exception.getMessage(),
        "Tag not found exception message should match expected");
    verify(tagDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(tagDao, never()).save(any(Tag.class));
  }
}
