/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.service.AuditService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

@ActiveProfiles("local")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
class AuditRestImplTest {

  @Autowired private MockMvc mockMvc;

  @MockitoBean private AuditService auditService;

  private final ObjectMapper objectMapper = new ObjectMapper();
  private ExceptionAuditDto exceptionAuditDto;

  @BeforeEach
  void setUp() {
    exceptionAuditDto = new ExceptionAuditDto();
    exceptionAuditDto.setAuditId("test-audit-id");
  }

  @Test
  @DisplayName("POST /audit/exception/save returns audit id")
  void saveExceptionAudit_ReturnsAuditId() throws Exception {
    when(auditService.saveExceptionAudit(any())).thenReturn("test-audit-id");

    mockMvc
        .perform(
            post("/audit/exception/save")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(exceptionAuditDto)))
        .andExpect(status().isOk())
        .andExpect(content().string("test-audit-id"));

    verify(auditService).saveExceptionAudit(any());
  }

  @Test
  @DisplayName("GET /audit/getPromptAuditListByAuditId/{id}")
  void getPromptAuditListByAuditId_ReturnsList() throws Exception {
    List<PromptAudit> expectedList = Collections.singletonList(new PromptAudit());
    when(auditService.getPromptAuditListByAuditId("abc")).thenReturn(expectedList);

    mockMvc
        .perform(get("/audit/getPromptAuditListByAuditId/abc"))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(expectedList)));

    verify(auditService).getPromptAuditListByAuditId("abc");
  }

  @Test
  @DisplayName("POST /audit/tool/save returns status map")
  void saveToolAudit_ReturnsStatusMap() throws Exception {
    ToolAuditDto dto = new ToolAuditDto();
    Map<String, String> response = Map.of("status", "success");

    when(auditService.saveToolAudit(any())).thenReturn(response);

    mockMvc
        .perform(
            post("/audit/tool/save")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(dto)))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(response)));

    verify(auditService).saveToolAudit(any());
  }
}
