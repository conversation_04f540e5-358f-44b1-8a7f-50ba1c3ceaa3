/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dao.TestCaseDao;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;

@ExtendWith(MockitoExtension.class)
class TestCaseServiceImplTest {

  @Mock TestCaseDao testCaseDao;

  @Mock CustomFilter customFilter;

  @Mock PromptDao promptDao;

  @InjectMocks TestCaseServiceImpl testCaseServiceImpl;

  private TestCase testCase;
  private TestCase existingTestCase;

  private static final String NANO_ID = "sQSu84MLpNRseHcnqtBHl";

  @BeforeEach
  void setUp() {
    testCase = new TestCase();
    testCase.setNanoId(NANO_ID);
    testCase.setRemark("Remark");

    existingTestCase = new TestCase();
    existingTestCase.setNanoId(NANO_ID);
    existingTestCase.setRemark("Remark");
  }

  @Test
  @DisplayName("Create TestCase successfully")
  void testCreate() {
    when(testCaseDao.save(any(TestCase.class))).thenReturn(testCase);
    TestCaseRequestDto requestDto = new TestCaseRequestDto();
    Map<String, String> result = testCaseServiceImpl.create(requestDto);
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "TestCase creation result should be 'success'");
    verify(testCaseDao, times(2)).save(any(TestCase.class));
  }

  @Test
  @DisplayName("Create TestCase failure")
  void testCreateTestFailure() {
    TestCaseRequestDto requestDto = new TestCaseRequestDto();
    when(testCaseDao.save(any(TestCase.class))).thenThrow(new RuntimeException("DB error"));
    assertThrows(
        BusinessException.class,
        () -> testCaseServiceImpl.create(requestDto),
        "Expected BusinessException when creating test case fails");
    verify(testCaseDao, times(1)).save(any(TestCase.class));
  }

  @Test
  @DisplayName("Update TestCase successfully")
  void testUpdateSuccess() {
    when(testCaseDao.findByNanoId(NANO_ID)).thenReturn(Optional.of(existingTestCase));
    when(testCaseDao.save(any(TestCase.class))).thenReturn(existingTestCase);
    // Mock promptDao.findByNanoId("1") to return a Prompt entity
    Prompt promptEntity = new Prompt();
    promptEntity.setNanoId("1");
    when(promptDao.findByNanoId("1")).thenReturn(Optional.of(promptEntity));

    TestCaseRequestDto updatedTestCase = new TestCaseRequestDto();
    updatedTestCase.setId(NANO_ID);
    updatedTestCase.setRemark("Updated Remark");
    updatedTestCase.setInputJson("{\"key\":\"value\"}");
    updatedTestCase.setRemark("Updated Remark");
    updatedTestCase.setAssertions("Assertions");

    PromptConvertorDto prompt = new PromptConvertorDto();
    prompt.setId("1");
    prompt.setApplication("Prompt Application Test");
    prompt.setPromptId("prompt-123");

    updatedTestCase.setPrompt(prompt);

    Map<String, String> result = testCaseServiceImpl.update(updatedTestCase);
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "TestCase update result should be 'success'");
    verify(testCaseDao).save(any(TestCase.class));
  }

  @Test
  @DisplayName("Update TestCase failure")
  void testUpdateFailure() {
    when(testCaseDao.findById(1)).thenReturn(Optional.of(existingTestCase));
    when(testCaseDao.save(any(TestCase.class))).thenThrow(new RuntimeException("Database error"));
    TestCaseRequestDto updatedTestCase = new TestCaseRequestDto();
    updatedTestCase.setId("1");
    updatedTestCase.setRemark("Updated Remark");
    assertThrows(
        RuntimeException.class,
        () -> testCaseServiceImpl.update(updatedTestCase),
        "Expected RuntimeException when updating test case fails");
    verify(testCaseDao).findByNanoId("1");
    verify(testCaseDao).save(any(TestCase.class));
  }

  @Test
  @DisplayName("Update TestCase - Not Found")
  void testUpdateNotFound() {
    when(testCaseDao.findByNanoId("1")).thenReturn(Optional.empty());
    TestCaseRequestDto updatedTestCase = new TestCaseRequestDto();
    updatedTestCase.setId("1");
    updatedTestCase.setRemark("Updated Remark");
    assertThrows(
        BusinessException.class,
        () -> testCaseServiceImpl.update(updatedTestCase),
        "Expected BusinessException when updating test case fails");
    verify(testCaseDao, times(1)).findByNanoId("1");
    verify(testCaseDao, never()).save(any(TestCase.class));
  }

  @Test
  @DisplayName("Delete TestCase - Not Found")
  void testDeleteNotFound() {
    when(testCaseDao.findById(1)).thenReturn(Optional.empty());
    assertThrows(
        BusinessException.class,
        () -> testCaseServiceImpl.deleteTestcase("1"),
        "Expected BusinessException when deleting test case fails");
    verify(testCaseDao).findById(1);
    verify(testCaseDao, never()).save(any());
  }

  @Test
  @DisplayName("Delete TestCase - Success")
  void testDeleteSuccess() {
    TestCase existingTest = new TestCase();
    existingTest.setNanoId(NANO_ID);
    existingTest.setDeleted(false);

    when(testCaseDao.findByNanoId(NANO_ID)).thenReturn(Optional.of(existingTest));
    when(testCaseDao.save(any(TestCase.class))).thenReturn(existingTest);

    Map<String, String> result = testCaseServiceImpl.deleteTestcase(NANO_ID);

    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "TestCase delete result should be 'success'");
    verify(testCaseDao, times(1)).findByNanoId(NANO_ID);
    verify(testCaseDao, times(1)).save(any(TestCase.class));
  }

  @Test
  @DisplayName("Search Test Cases")
  void testSearch() {
    // Ensure testCase is fully initialized
    testCase.setNanoId(NANO_ID);
    testCase.setRemark("Remark");
    testCase.setInputJson("{\"key\":\"value\"}");
    testCase.setAssertions("Assertions");
    Prompt prompt = new Prompt();
    prompt.setNanoId("1");
    testCase.setPrompt(prompt);

    when(customFilter.searchByFilter(
            any(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(List.of(testCase));
    List<TestCaseResponseDto> result =
        testCaseServiceImpl.search("nanoId:" + NANO_ID, 0, 10, "nanoId", "asc");
    assertFalse(result.isEmpty(), "TestCaseService search result should not be empty");
    verify(customFilter, times(1))
        .searchByFilter(any(), anyString(), anyString(), anyString(), anyInt(), anyInt());
  }

  @Test
  @DisplayName("Empty list when no testCase are found")
  void testSearchFailure() {
    when(customFilter.searchByFilter(
            any(), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(List.of());
    List<TestCaseResponseDto> result = testCaseServiceImpl.search("filter", 0, 10, "id", "asc");
    assertNotNull(result, "TestCaseService result should not be null");
    assertEquals(0, result.size(), "Result size should be 0 when no data is returned");
  }

  @Test
  @DisplayName("Count Test Cases")
  void testCount() {
    when(customFilter.countByFilter(any(), anyString())).thenReturn(5L);
    Long count = testCaseServiceImpl.count("filter");
    assertEquals(5L, count, "TestCase count should match expected");
    verify(customFilter, times(1)).countByFilter(any(), anyString());
  }

  @Test
  void testCreateBatchSuccess() {
    // Arrange
    TestCaseRequestDto dto = new TestCaseRequestDto();
    TestCase test = new TestCase();
    test.setId(1);
    test.setRemark("Batch Test Case");

    List<TestCaseRequestDto> requestDtos = List.of(dto);
    List<TestCase> initialMapped = List.of(testCase);

    when(testCaseDao.saveAll(anyList())).thenReturn(initialMapped);

    // Act
    Map<String, String> result = testCaseServiceImpl.createBatch(requestDtos);

    // Assert
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "TestCase batch creation result should be 'success'");
    verify(testCaseDao, times(2)).saveAll(anyList());
  }

  @Test
  void testCreateBatchWithEmptyList() {
    List<TestCaseRequestDto> emptyList = Collections.emptyList();

    ResourceNotFoundException ex =
        assertThrows(
            ResourceNotFoundException.class,
            () -> {
              testCaseServiceImpl.createBatch(emptyList);
            },
            "Expected ResourceNotFoundException when creating batch with empty list");

    assertEquals("no data is provided", ex.getMessage());
  }

  @Test
  void testCreateBatchThrowsException() {
    // Arrange
    TestCaseRequestDto dto = new TestCaseRequestDto();
    List<TestCaseRequestDto> dtos = List.of(dto);

    when(testCaseDao.saveAll(anyList())).thenThrow(new RuntimeException("DB failure"));

    // Act & Assert
    BusinessException ex =
        assertThrows(
            BusinessException.class,
            () -> testCaseServiceImpl.createBatch(dtos),
            "Expected BusinessException when creating batch of test cases fails");

    assertTrue(
        ex.getMessage().contains("Unable to create test cases"),
        "Exception message should contain 'Unable to create test cases'");
  }

  @Test
  void testImportTestCaseCreateNew() throws Exception {
    // Prepare mock CSV content for a new test case
    String csvContent =
        "TestcaseId,InputJson,Assertions,Remark,PromptId\n"
            + "TC001,{\"input\":123},{\"assert\":true},Sample Remark,PID123";

    MockMultipartFile mockFile =
        new MockMultipartFile(
            "file", "testcases.csv", "text/csv", csvContent.getBytes(StandardCharsets.UTF_8));

    Prompt prompt = new Prompt();
    prompt.setPromptId("PID123");
    when(promptDao.findByPromptId("PID123")).thenReturn(prompt);
    when(testCaseDao.findByTestcaseId("TC001")).thenReturn(Optional.empty());
    when(testCaseDao.save(any(TestCase.class))).thenAnswer(invocation -> invocation.getArgument(0));

    ResponseEntity<Resource> response = testCaseServiceImpl.importTestCase(mockFile);

    assertEquals(
        HttpStatus.OK, response.getStatusCode(), "Import test case response status should be OK");
    assertNotNull(response.getBody(), "TestCaseService response body should not be null");
    assertTrue(response.getBody().contentLength() > 0, "Response body should have content");

    // Verify save is called once with a new TestCase (deleted=false)
    verify(testCaseDao, times(1))
        .save(argThat(tc -> !tc.getDeleted() && "TC001".equals(tc.getTestcaseId())));
  }

  @Test
  void testImportTestCaseUpdateExisting() throws Exception {
    String csvContent =
        "TestcaseId,InputJson,Assertions,Remark,PromptId\n"
            + "TC001,{\"input\":456},{\"assert\":false},Updated Remark,PID123";

    MockMultipartFile mockFile =
        new MockMultipartFile(
            "file", "testcases.csv", "text/csv", csvContent.getBytes(StandardCharsets.UTF_8));

    Prompt prompt = new Prompt();
    prompt.setPromptId("PID123");
    when(promptDao.findByPromptId("PID123")).thenReturn(prompt);

    TestCase existingTest = new TestCase();
    existingTest.setTestcaseId("TC001");
    existingTest.setInputJson("{\"input\":123}");
    existingTest.setAssertions("{\"assert\":true}");
    existingTest.setRemark("Old Remark");
    existingTest.setPrompt(prompt);

    when(testCaseDao.findByTestcaseId("TC001")).thenReturn(Optional.of(existingTest));
    when(testCaseDao.save(any(TestCase.class))).thenAnswer(invocation -> invocation.getArgument(0));

    ResponseEntity<Resource> response = testCaseServiceImpl.importTestCase(mockFile);

    assertEquals(
        HttpStatus.OK, response.getStatusCode(), "Import test case response status should be OK");
    assertNotNull(response.getBody(), "TestCaseService response body should not be null");
    assertTrue(response.getBody().contentLength() > 0, "Response body should have content");

    ArgumentCaptor<TestCase> captor = ArgumentCaptor.forClass(TestCase.class);
    verify(testCaseDao).save(captor.capture());

    TestCase saved = captor.getValue();
    assertEquals("TC001", saved.getTestcaseId(), "TestCase ID should match expected");
    assertEquals("{\"input\":456}", saved.getInputJson(), "Input JSON should match expected");
    assertEquals("{\"assert\":false}", saved.getAssertions(), "Assertions should match expected");
    assertEquals("Updated Remark", saved.getRemark(), "Remark should match expected");
    assertNotNull(saved.getPrompt(), "Prompt should not be null");
    assertEquals("PID123", saved.getPrompt().getPromptId(), "Prompt ID should match expected");
  }

  @Test
  void testImportTestCaseParseError() throws Exception {
    String invalidCsv = "InvalidHeader\nBadData";
    MockMultipartFile mockFile =
        new MockMultipartFile("file", "invalid.csv", "text/csv", invalidCsv.getBytes());

    ResponseEntity<Resource> response = testCaseServiceImpl.importTestCase(mockFile);

    assertEquals(
        HttpStatus.INTERNAL_SERVER_ERROR,
        response.getStatusCode(),
        "Import test case response status should be INTERNAL_SERVER_ERROR");
    assertNotNull(response.getBody(), "TestCaseService response body should not be null");
    assertTrue(
        new String(response.getBody().getContentAsByteArray()).contains("Error"),
        "Response should contain 'Error'");
  }

  @Test
  void testExportTestCasesByIdsSuccess() {
    List<TestCase> testCaseList = getTestCases();

    when(testCaseDao.findAllById(List.of(1, 2))).thenReturn(testCaseList);

    ResponseEntity<Resource> response = testCaseServiceImpl.exportTestCasesByIds(List.of("1", "2"));

    assertNotNull(response, "TestCaseService response should not be null");
    assertEquals(
        HttpStatus.OK, response.getStatusCode(), "Export test case response status should be OK");
    assertNotNull(response.getBody(), "TestCaseService response body should not be null");

    // Optionally check if the CSV content contains expected strings
    // For example:
    try (InputStream is = response.getBody().getInputStream()) {
      String content = new String(is.readAllBytes(), StandardCharsets.UTF_8);
      assertTrue(content.contains("TC001"), "Exported content should contain 'TC001'");
      assertFalse(
          content.contains("TC002"),
          "Exported content should not contain 'TC002'"); // filtered out because deleted = true
    } catch (IOException e) {
      fail("Failed to read response body");
    }

    verify(testCaseDao, times(1)).findAllById(List.of(1, 2));
  }

  @NotNull
  private static List<TestCase> getTestCases() {
    TestCase testCase1 = new TestCase();
    testCase1.setTestcaseId("TC001");
    testCase1.setInputJson("{\"input\":123}");
    testCase1.setAssertions("{\"assert\":true}");
    testCase1.setRemark("Remark 1");
    testCase1.setDeleted(false);
    Prompt prompt = new Prompt();
    prompt.setPromptId("PROMPT1");
    testCase1.setPrompt(prompt);

    TestCase testCase2 = new TestCase();
    testCase2.setTestcaseId("TC002");
    testCase2.setDeleted(true); // This one should be filtered out

    List<TestCase> testCaseList = new ArrayList<>();
    testCaseList.add(testCase1);
    testCaseList.add(testCase2);
    return testCaseList;
  }
}
