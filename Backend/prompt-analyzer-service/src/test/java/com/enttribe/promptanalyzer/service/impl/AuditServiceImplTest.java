/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dao.ExceptionAuditDao;
import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptAuditDao;
import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.ExceptionAudit;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AuditServiceImplTest {

  @Mock private ExceptionAuditDao exceptionDao;

  @Mock private PromptAuditDao promptAuditDao;

  @Mock private LlmModelDao llmModelDao;

  @Mock private CustomFilter customFilter;

  @InjectMocks private AuditServiceImpl auditServiceImpl;

  private ExceptionAuditDto exceptionAuditDto;
  private PromptAuditDto promptAuditDto;
  private LlmModel llmModel;

  @BeforeEach
  void testSetUp() {
    // Setup ExceptionAuditDto
    exceptionAuditDto = new ExceptionAuditDto();
    Map<String, Object> methodParams = new HashMap<>();
    methodParams.put("userId", "user123");
    methodParams.put("requestId", "req-456");
    methodParams.put("timestamp", "2024-03-20T10:30:00Z");
    exceptionAuditDto.setMethodParameters(methodParams);

    Map<String, Object> identifiers = new HashMap<>();
    identifiers.put("className", "UserService");
    identifiers.put("methodName", "createUser");
    identifiers.put("exceptionType", "ValidationException");
    exceptionAuditDto.setIdentifier(identifiers);

    // Setup PromptAuditDto
    promptAuditDto = new PromptAuditDto();
    promptAuditDto.setAuditId("audit-123");
    promptAuditDto.setModel("gpt-4");
    promptAuditDto.setProvider("openai");
    promptAuditDto.setPromptToken(100L);
    promptAuditDto.setTotalToken(200L);

    // Setup LlmModel
    llmModel = new LlmModel();
    llmModel.setId(1);
    llmModel.setModel("gpt-4");
    llmModel.setProvider("openai");
    llmModel.setInputCost(30.0);
    llmModel.setOutputCost(60.0);
  }

  @Test
  @DisplayName("Save Exception Audit - Success Case")
  void testSaveExceptionAuditSuccess() {
    ExceptionAudit exceptionAudit = new ExceptionAudit();
    exceptionAudit.setMethodParameters("test-parameters");
    exceptionAudit.setIdentifier("test-identifier");

    when(exceptionDao.save(any(ExceptionAudit.class))).thenReturn(exceptionAudit);

    String result = auditServiceImpl.saveExceptionAudit(exceptionAuditDto);
    Assertions.assertEquals("success", result, "Exception audit save result should be 'success'");

    exceptionAuditDto.setMethodParameters(null);
    exceptionAuditDto.setIdentifier(null);
    result = auditServiceImpl.saveExceptionAudit(exceptionAuditDto);
    Assertions.assertEquals("success", result, "Exception audit save result should be 'success'");

    verify(exceptionDao, times(2)).save(any(ExceptionAudit.class));
  }

  @Test
  @DisplayName("Save Exception Audit - Failure Case")
  void testSaveExceptionAuditFailure() {
    when(exceptionDao.save(any(ExceptionAudit.class))).thenThrow(new RuntimeException("DB Error"));

    String result = auditServiceImpl.saveExceptionAudit(exceptionAuditDto);

    Assertions.assertEquals("failed", result, "Exception audit save result should be 'failed'");
    verify(exceptionDao, times(1)).save(any(ExceptionAudit.class));
  }

  @Test
  @DisplayName("Save Prompt Audit - Success Case")
  void testSavePromptAuditSuccess() {
    when(promptAuditDao.save(any(PromptAudit.class))).thenReturn(new PromptAudit());
    when(llmModelDao.findModelAndProvider("gpt-4", "openai")).thenReturn(llmModel);

    String result = auditServiceImpl.savePromptAudit(promptAuditDto);

    Assertions.assertEquals("success", result, "Prompt audit save result should be 'success'");
    verify(promptAuditDao, times(1)).save(any(PromptAudit.class));
    verify(llmModelDao, times(1)).findModelAndProvider("gpt-4", "openai");
  }

  @Test
  @DisplayName("Save Prompt Audit - Failure Case")
  void testSavePromptAuditFailure() {
    when(promptAuditDao.save(any(PromptAudit.class))).thenThrow(new RuntimeException("DB Error"));

    String result = auditServiceImpl.savePromptAudit(promptAuditDto);

    Assertions.assertEquals("failed", result, "Prompt audit save result should be 'failed'");
    verify(promptAuditDao, times(1)).save(any(PromptAudit.class));
  }

  @Test
  @DisplayName("Get Prompt Audit List By Audit ID - Success Case")
  void testGetPromptAuditListByAuditId() {
    when(promptAuditDao.getPromptAuditListByAuditId(anyString()))
        .thenReturn(List.of(new PromptAudit()));

    List<PromptAudit> result = auditServiceImpl.getPromptAuditListByAuditId("auditId");

    Assertions.assertEquals(1, result.size(), "Prompt audit list size should match expected");
    verify(promptAuditDao, times(1)).getPromptAuditListByAuditId(anyString());
  }

  @Test
  @DisplayName("Search Prompt Audit - Success Case")
  void testSearchPromptAuditSuccess() {
    // Arrange
    String filter = "model:gpt-4";
    Integer offset = 0;
    Integer size = 10;
    String orderBy = "creationTime";
    String orderType = "DESC";

    List<PromptAudit> mockAuditList = List.of(new PromptAudit());
    when(customFilter.searchByFilter(PromptAudit.class, filter, orderBy, orderType, offset, size))
        .thenReturn(mockAuditList);

    // Act
    List<PromptAuditDto> result =
        auditServiceImpl.searchPromptAudit(filter, offset, size, orderBy, orderType);

    // Assert
    Assertions.assertNotNull(result, "Audit result should not be null");
    verify(customFilter)
        .searchByFilter(PromptAudit.class, filter, orderBy, orderType, offset, size);
  }

  @Test
  @DisplayName("Search Prompt Audit - Empty Result Case")
  void testSearchPromptAuditEmptyResult() {
    // Arrange
    String filter = "model:unknown";
    Integer offset = 0;
    Integer size = 10;
    String orderBy = "creationTime";
    String orderType = "DESC";

    when(customFilter.searchByFilter(PromptAudit.class, filter, orderBy, orderType, offset, size))
        .thenReturn(List.of());

    // Act
    List<PromptAuditDto> result =
        auditServiceImpl.searchPromptAudit(filter, offset, size, orderBy, orderType);

    // Assert
    Assertions.assertNotNull(result, "Audit result should not be null");
    Assertions.assertTrue(result.isEmpty(), "Audit result should be empty");
    verify(customFilter)
        .searchByFilter(PromptAudit.class, filter, orderBy, orderType, offset, size);
  }

  @Test
  @DisplayName("Count Prompt Audit - Success Case")
  void testCountPromptAuditSuccess() {
    // Arrange
    String filter = "model:gpt-4";
    when(customFilter.countByFilter(PromptAudit.class, filter)).thenReturn(5L);

    // Act
    Long result = auditServiceImpl.countPromtAudit(filter);

    // Assert
    Assertions.assertEquals(5L, result, "Prompt audit count should match expected");
    verify(customFilter).countByFilter(PromptAudit.class, filter);
  }

  @Test
  @DisplayName("Count Prompt Audit - Zero Count Case")
  void testCountPromptAuditZeroCount() {
    // Arrange
    String filter = "model:unknown";
    when(customFilter.countByFilter(PromptAudit.class, filter)).thenReturn(0L);

    // Act
    Long result = auditServiceImpl.countPromtAudit(filter);

    // Assert
    Assertions.assertEquals(0L, result, "Prompt audit count should match expected");
    verify(customFilter).countByFilter(PromptAudit.class, filter);
  }
}
