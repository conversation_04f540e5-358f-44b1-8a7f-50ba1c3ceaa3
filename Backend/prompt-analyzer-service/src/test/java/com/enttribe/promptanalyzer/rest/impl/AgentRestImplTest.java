/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.service.AgentHistoryService;
import com.enttribe.promptanalyzer.service.CustomAgentService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@WebMvcTest(AgentRestImpl.class)
class AgentRestImplTest {

  private static final String TEST_AGENT = "testAgent";
  private static final String TEST_QUERY = "test_query";
  private static final String TEST_PROCESS_GROUP_ID = "test-process-group-123";
  private static final String TEST_CONVERSATION_ID = "conv-123";

  @MockitoBean private CustomAgentService customAgentService;

  @MockitoBean private NifiFlowService nifiFlowService;

  @MockitoBean private AgentHistoryService agentHistoryService;

  @Autowired private MockMvc mockMvc;

  private CustomAgentDto customAgentDto;
  private NifiFlowDto flowDto;
  private AgentHistory agentHistory;

  @BeforeEach
  void setUp() {
    // Setup CustomAgentDto
    customAgentDto = new CustomAgentDto();
    customAgentDto.setConversationId(TEST_CONVERSATION_ID);
    customAgentDto.setUserQuery(TEST_QUERY);
    customAgentDto.setProcessGroupId(TEST_PROCESS_GROUP_ID);
    customAgentDto.setTimeStamp(System.currentTimeMillis());

    // Setup NifiFlowDto
    flowDto = new NifiFlowDto();
    flowDto.setProcessGroupId(TEST_PROCESS_GROUP_ID);
    flowDto.setFlowObject("{\"flow\": \"test\"}");

    // Setup AgentHistory
    agentHistory = new AgentHistory();
    agentHistory.setId(1);
    agentHistory.setAgentName(TEST_AGENT);
    agentHistory.setCurrentPlan("Test plan");
    agentHistory.setLastFlow("Test flow");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_WRITE"})
  @DisplayName("Get plan for user query successfully")
  void testGetPlanForUserQuerySuccess() throws Exception {
    // Arrange
    Map<String, String> expectedPlan =
        Map.of(
            "plan", "Generated plan for user query",
            "status", "success");
    when(customAgentService.getPlanforUserQuery(any(CustomAgentDto.class)))
        .thenReturn(expectedPlan);

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.post("/agent/getPlanforUserQuery")
                .content(asJsonString(customAgentDto))
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.plan").value("Generated plan for user query"))
        .andExpect(jsonPath("$.status").value(PromptConstants.SUCCESS));

    verify(customAgentService).getPlanforUserQuery(any(CustomAgentDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_WRITE"})
  @DisplayName("Get plan for user query with error")
  void testGetPlanForUserQueryError() throws Exception {
    // Arrange
    when(customAgentService.getPlanforUserQuery(any(CustomAgentDto.class)))
        .thenThrow(new BusinessException("Failed to generate plan"));

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.post("/agent/getPlanforUserQuery")
                .content(asJsonString(customAgentDto))
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isBadRequest()); // Changed from isInternalServerError to isBadRequest

    verify(customAgentService).getPlanforUserQuery(any(CustomAgentDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_READ"})
  @DisplayName("Get agent history successfully")
  void testGetAgentHistorySuccess() throws Exception {
    // Arrange
    when(agentHistoryService.getAgentHistory(TEST_AGENT)).thenReturn(agentHistory);

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.get("/agent/getAgentHistory")
                .param("processGroupId", TEST_AGENT))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id").value(1))
        .andExpect(jsonPath("$.processGroupId").value(TEST_AGENT))
        .andExpect(jsonPath("$.currentPlan").value("Test plan"))
        .andExpect(jsonPath("$.lastFlow").value("Test flow"));

    verify(agentHistoryService).getAgentHistory(TEST_AGENT);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_READ"})
  @DisplayName("Get agent history not found")
  void testGetAgentHistoryNotFound() throws Exception {
    // Arrange
    when(agentHistoryService.getAgentHistory(anyString())).thenReturn(null);

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.get("/agent/getAgentHistory")
                .param("processGroupId", "nonexistent"))
        .andExpect(status().isOk());

    verify(agentHistoryService).getAgentHistory("nonexistent");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_WRITE"})
  @DisplayName("Create trigger in NiFi successfully")
  void testCreateTriggerInNifiSuccess() throws Exception {
    // Arrange
    Map<String, String> expectedNifiResponse =
        Map.of(
            "status", "success",
            "triggerId", "trigger-123",
            "message", "Trigger created successfully");
    when(nifiFlowService.createTriggerNifi(any(NifiFlowDto.class)))
        .thenReturn(expectedNifiResponse);

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.post("/agent/createTriggerInNifi")
                .content(asJsonString(flowDto))
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.status").value(PromptConstants.SUCCESS))
        .andExpect(jsonPath("$.triggerId").value("trigger-123"))
        .andExpect(jsonPath("$.message").value("Trigger created successfully"));

    verify(nifiFlowService).createTriggerNifi(any(NifiFlowDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_WRITE"})
  @DisplayName("Create trigger in NiFi with error")
  void testCreateTriggerInNifiError() throws Exception {
    // Arrange
    when(nifiFlowService.createTriggerNifi(any(NifiFlowDto.class)))
        .thenThrow(new BusinessException("Failed to create trigger in NiFi"));

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.post("/agent/createTriggerInNifi")
                .content(asJsonString(flowDto))
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isBadRequest()); // Changed from isInternalServerError to isBadRequest

    verify(nifiFlowService).createTriggerNifi(any(NifiFlowDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_WRITE"})
  @DisplayName("Create trigger name successfully")
  void testCreateTriggerNameSuccess() throws Exception {
    // Arrange
    Map<String, String> requestBody = Map.of("userQuery", TEST_QUERY);
    Map<String, String> expectedTriggerNameResponse =
        Map.of(
            "triggerName", "GeneratedTriggerName",
            "description", "Generated description for trigger");

    when(customAgentService.createTriggerNameDescription(TEST_QUERY))
        .thenReturn(expectedTriggerNameResponse);

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.post("/agent/createTriggerName")
                .content(asJsonString(requestBody))
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.triggerName").value("GeneratedTriggerName"))
        .andExpect(jsonPath("$.description").value("Generated description for trigger"));

    verify(customAgentService).createTriggerNameDescription(TEST_QUERY);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_WRITE"})
  @DisplayName("Create trigger name with missing userQuery")
  void testCreateTriggerNameMissingUserQuery() throws Exception {
    // Arrange
    Map<String, String> requestBody = new HashMap<>(); // Empty request body

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.post("/agent/createTriggerName")
                .content(asJsonString(requestBody))
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isOk()); // The controller doesn't validate, it passes null to service

    verify(customAgentService).createTriggerNameDescription(null);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_AGENT_WRITE"})
  @DisplayName("Create trigger name with service error")
  void testCreateTriggerNameServiceError() throws Exception {
    // Arrange
    Map<String, String> requestBody = Map.of("userQuery", TEST_QUERY);
    when(customAgentService.createTriggerNameDescription(TEST_QUERY))
        .thenThrow(new BusinessException("Failed to generate trigger name"));

    // Act & Assert
    mockMvc
        .perform(
            MockMvcRequestBuilders.post("/agent/createTriggerName")
                .content(asJsonString(requestBody))
                .contentType(MediaType.APPLICATION_JSON)
                .with(csrf()))
        .andExpect(status().isBadRequest()); // Changed from isInternalServerError to isBadRequest

    verify(customAgentService).createTriggerNameDescription(TEST_QUERY);
  }

  /** Utility method to convert objects to JSON string for testing */
  private static String asJsonString(final Object obj) {
    try {
      return new ObjectMapper().writeValueAsString(obj);
    } catch (Exception e) {
      throw new BusinessException("Failed to convert object to JSON", e);
    }
  }
}
