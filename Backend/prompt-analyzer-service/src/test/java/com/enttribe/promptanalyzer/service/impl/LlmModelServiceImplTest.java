/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.LlmModel;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LlmModelServiceImplTest {

  @Mock private LlmModelDao llmModelDao;

  @InjectMocks private LlmModelServiceImpl llmModelService;

  private LlmModel mockLlmModel;
  private List<Object[]> mockResults;
  private List<LlmModelSdkDto> mockResponse;

  @BeforeEach
  void setUp() {
    mockLlmModel = new LlmModel();
    mockLlmModel.setId(1);
    mockLlmModel.setProvider("OpenAI");
    mockLlmModel.setModel("GPT-4");
    mockLlmModel.setType("chat");

    mockResponse =
        List.of(
            new LlmModelSdkDto("OpenAI", "https://api.openai.com", "dummy-key", "GPT-3"),
            new LlmModelSdkDto("OpenAI", "https://api.openai.com", "dummy-key", "GPT-4"));

    mockResults =
        Arrays.asList(
            new Object[] {"Google", "Gemini"},
            new Object[] {"OpenAI", "GPT-3"},
            new Object[] {"OpenAI", "GPT-4"});
  }

  @Test
  @DisplayName("Test getAllProvidersWithModels success")
  void testGetModelsByProvider() {
    when(llmModelDao.getModelsByProvider("OpenAI", "chat")).thenReturn(List.of("GPT-3", "GPT-4"));
    ProviderModelDto result = llmModelService.getModelsByProvider("OpenAI");
    assertNotNull(result, "LlmModel result should not be null");
    assertEquals("OpenAI", result.getProvider(), "Provider should match expected");
    assertEquals(2, result.getModels().size(), "Models list size should match expected");
    verify(llmModelDao, times(1)).getModelsByProvider("OpenAI", "chat");
  }

  @Test
  @DisplayName("Test getAllProvidersWithModels failed")
  void testGetAllProvidersWithModels() {
    when(llmModelDao.getAllProvidersWithModels("chat")).thenReturn(mockResults);
    List<ProviderModelDto> result = llmModelService.getAllProvidersWithModels();
    assertEquals("Google", result.get(0).getProvider(), "First provider should be 'Google'");
    assertEquals("OpenAI", result.get(1).getProvider(), "Second provider should be 'OpenAI'");
    verify(llmModelDao, times(1)).getAllProvidersWithModels("chat");
  }

  @Test
  @DisplayName("Test getAllProvidersWithModels no records")
  void testGetAllProvidersWithModelsNoRecords() {
    when(llmModelDao.getAllProvidersWithModels("chat")).thenReturn(Collections.emptyList());
    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> llmModelService.getAllProvidersWithModels(),
            "Expected BusinessException when getting all providers with models fails");
    assertEquals(
        "No records found in LlmModel for the given query",
        exception.getMessage(),
        "Exception message should match expected");
    verify(llmModelDao, times(1)).getAllProvidersWithModels("chat");
  }

  @Test
  @DisplayName("Test get unique inferences by type success")
  void testGetUniqueInferencesByType() {
    when(llmModelDao.getUniqueInferencesByType("chat")).thenReturn(mockResponse);
    List<LlmModelSdkDto> result = llmModelService.getUniqueInferencesByType("chat");
    assertEquals(2, result.size(), "Unique inferences list size should match expected");
    verify(llmModelDao, times(1)).getUniqueInferencesByType("chat");
  }

  @Test
  @DisplayName("Test get LlmModelsForSDK success")
  void testGetLlmModelsForSDK() {
    when(llmModelDao.getLlmModelsForSDK("AppX", "chat")).thenReturn(mockResponse);
    List<LlmModelSdkDto> result = llmModelService.getLlmModelsForSDK("AppX", "chat");
    assertEquals(2, result.size(), "LlmModelsForSDK list size should match expected");
    verify(llmModelDao, times(1)).getLlmModelsForSDK("AppX", "chat");
  }

  @Test
  @DisplayName("Test get LlmModelsForSDK failed")
  void testGetLlmModelsByTypeForSDK() {
    when(llmModelDao.getLlmModelsByTypeForSDK("chat")).thenReturn(mockResponse);
    List<LlmModelSdkDto> result = llmModelService.getLlmModelsByTypeForSDK("chat");
    assertEquals(2, result.size(), "LlmModelsByTypeForSDK list size should match expected");
    verify(llmModelDao, times(1)).getLlmModelsByTypeForSDK("chat");
  }
}
