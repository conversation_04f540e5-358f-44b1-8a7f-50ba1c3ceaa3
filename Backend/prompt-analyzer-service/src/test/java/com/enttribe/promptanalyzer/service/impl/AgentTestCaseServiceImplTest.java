/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.dao.*;
import com.enttribe.promptanalyzer.dto.agent.AgentApiResponse;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseResponseDto;
import com.enttribe.promptanalyzer.dto.assertion.AssertionRequestDto;
import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.*;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AgentTestCaseServiceImplTest {

  @Mock private AgentTestCaseDao agentTestCaseDao;
  @Mock private PromptDao promptDao;
  @Mock private ToolDao toolDao;
  @Mock private KnowledgeBaseDao knowledgeBaseDao;
  @Mock private CustomFilter customFilter;
  @Mock private ApiService apiService;
  @Mock private AssertionDao assertionDao;
  @Mock private AgentTestCaseAssertionDao agentTestCaseAssertionDao;

  @InjectMocks private AgentTestCaseServiceImpl agentTestCaseService;

  @Test
  void testCreateSuccess() {
    AgentTestCaseRequestDto requestDto = new AgentTestCaseRequestDto();
    requestDto.setName("TestCase");
    requestDto.setAgentId("agent123");
    requestDto.setAgentType("custom");
    List<AssertionRequestDto> assertions =
        List.of(
            AssertionRequestDto.builder()
                .id("assertion123")
                .assertionType(AssertionType.LLM_RESPONSE)
                .matchStrategy(MatchStrategy.EXACT)
                .expectedValue("Expected result")
                .jsonPointer(null)
                .toolName("Waves")
                .build());

    requestDto.setAssertions(assertions);
    AgentApiResponse response = new AgentApiResponse();
    response.setPromptId("prompt123");
    response.setToolIds(List.of("tool1"));
    response.setKnowledgeBaseIds(List.of("kb1"));

    when(apiService.getAgentDetails(any(), any())).thenReturn(response);
    when(promptDao.findByNanoId("prompt123")).thenReturn(Optional.of(new Prompt()));
    when(toolDao.findByNanoIds(any())).thenReturn(List.of(new Tool()));
    when(knowledgeBaseDao.findAllByNanoIds(any())).thenReturn(List.of(new KnowledgeBase()));
    when(assertionDao.findByNanoId("assertion123")).thenReturn(Optional.of(new Assertion()));
    when(agentTestCaseDao.save(any()))
        .thenAnswer(
            inv -> {
              AgentTestCase atc = inv.getArgument(0);
              atc.setNanoId("nanoId123");
              return atc;
            });

    Map<String, String> result = agentTestCaseService.create(requestDto);
    assertEquals("success", result.get("result"), "AgentTestCase save result should be 'success'");
    assertEquals("nanoId123", result.get("id"), "AgentTestCase ID should match expected");
  }

  @Test
  void testCreatePromptNotFound() {
    AgentTestCaseRequestDto requestDto = new AgentTestCaseRequestDto();
    requestDto.setName("Test");
    requestDto.setAgentId("agentId");
    requestDto.setAgentType("custom");

    AgentApiResponse agentDetails = new AgentApiResponse();
    agentDetails.setPromptId("missingPrompt");
    when(apiService.getAgentDetails(any(), any())).thenReturn(agentDetails);
    when(promptDao.findByNanoId("missingPrompt")).thenReturn(Optional.empty());

    assertThrows(
        ResourceNotFoundException.class,
        () -> agentTestCaseService.create(requestDto),
        "Expected ResourceNotFoundException when prompt is not found");
  }

  @Test
  void testUpdateSuccess() {
    AgentTestCase existing = new AgentTestCase();
    existing.setName("ExistingTest");
    existing.setAgentTestCaseAssertions(new HashSet<>()); // Important!

    AgentTestCaseRequestDto requestDto = new AgentTestCaseRequestDto();
    requestDto.setName("ExistingTest");
    requestDto.setAssertions(
        List.of(
            AssertionRequestDto.builder()
                .id("assertion123")
                .assertionType(AssertionType.LLM_RESPONSE)
                .matchStrategy(MatchStrategy.EXACT)
                .expectedValue("expected")
                .toolName("tool-1")
                .build()));

    when(agentTestCaseDao.findByName("ExistingTest")).thenReturn(Optional.of(existing));
    when(assertionDao.findByNanoId("assertion123")).thenReturn(Optional.of(new Assertion()));
    when(agentTestCaseAssertionDao.findByTestCaseNameAndStrategyAndType(any(), any(), any(), any()))
        .thenReturn(Optional.of(new AgentTestCaseAssertion()));
    when(agentTestCaseDao.save(any()))
        .thenAnswer(
            inv -> {
              AgentTestCase atc = inv.getArgument(0);
              atc.setNanoId("id123");
              return atc;
            });

    Map<String, String> result = agentTestCaseService.update(requestDto, false);

    assertEquals("success", result.get("result"), "AgentTestCase save result should be 'success'");
    assertEquals("id123", result.get("id"), "AgentTestCase ID should match expected");
  }

  @Test
  void testSearchSuccess() {
    AgentTestCase atc = new AgentTestCase();
    atc.setNanoId("id");
    atc.setName("TestCase");

    when(customFilter.searchByFilter(any(), any(), any(), any(), any(), any()))
        .thenReturn(List.of(atc));

    List<AgentTestCaseResponseDto> result =
        agentTestCaseService.search("{}", 0, 10, "createdTime", "desc");
    assertEquals(1, result.size(), "AgentTestCase list size should match expected");
    assertEquals("id", result.get(0).getId(), "AgentTestCase ID in list should match expected");
  }

  @Test
  void testCountSuccess() {
    when(customFilter.countByFilter(any(), any())).thenReturn(5L);
    Long count = agentTestCaseService.count("{}");
    assertEquals(5L, count, "AgentTestCase count should match expected");
  }

  @Test
  void testDeleteByIdSuccess() {
    AgentTestCase atc = new AgentTestCase();
    atc.setNanoId("id123");
    when(agentTestCaseDao.findByNanoId("id123")).thenReturn(Optional.of(atc));
    when(agentTestCaseDao.save(any())).thenReturn(atc);
    Map<String, String> result = agentTestCaseService.deleteById("id123");
    assertEquals(
        "success", result.get("result"), "AgentTestCase delete result should be 'success'");
  }
}
