/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;
import com.enttribe.promptanalyzer.ai.dto.MessageDto;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.LlmApiService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

@WebMvcTest(LlmApiRestImpl.class)
class LlmApiRestImplTest {

  private static final String PROMPT_ID = "promptId";
  private static final String TEST_PROMPT_1 = "test-prompt-1";
  private static final String JSON_PATH_RESULT = "$.result";
  private static final String GENERAL_TYPE = "general";
  private static final String TEST_USER_INPUT = "Create a greeting message";
  private static final String TEST_OLD_PROMPT = "Hello there!";

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockitoBean private LlmApiService llmApiService;

  private ChatCompletionRequestDto chatCompletionRequestDto;
  private Map<String, Object> executePromptRequest;
  private Map<String, Object> executePromptV1Request;
  private Map<String, String> generateSystemPromptRequest;
  private Map<String, String> improveSystemPromptRequest;

  @BeforeEach
  void setUp() {
    // Setup ChatCompletionRequestDto
    chatCompletionRequestDto = new ChatCompletionRequestDto();
    chatCompletionRequestDto.setMaxTokens(2000);
    chatCompletionRequestDto.setModel("gpt-4");
    chatCompletionRequestDto.setTemperature(0.7);
    chatCompletionRequestDto.setTopP(1.0);
    chatCompletionRequestDto.setJsonMode(false);
    chatCompletionRequestDto.setProvider("openai");
    chatCompletionRequestDto.setMessages(
        List.of(
            new MessageDto("system", "You are a helpful assistant."),
            new MessageDto("user", "Hello, how are you?")));

    // Setup executePrompt request
    executePromptRequest = new HashMap<>();
    executePromptRequest.put(PROMPT_ID, TEST_PROMPT_1);
    executePromptRequest.put("variableMap", Map.of("variable1", "value1"));
    executePromptRequest.put("format", true);

    // Setup executePromptV1 request
    executePromptV1Request = new HashMap<>();
    executePromptV1Request.put(PROMPT_ID, TEST_PROMPT_1);
    executePromptV1Request.put("variableMap", Map.of("key", "value"));

    // Setup generateSystemPrompt request
    generateSystemPromptRequest = new HashMap<>();
    generateSystemPromptRequest.put("userInput", TEST_USER_INPUT);
    generateSystemPromptRequest.put("type", GENERAL_TYPE);

    // Setup improveSystemPrompt request
    improveSystemPromptRequest = new HashMap<>();
    improveSystemPromptRequest.put("userInput", "Make it more formal");
    improveSystemPromptRequest.put("oldPrompt", TEST_OLD_PROMPT);
    improveSystemPromptRequest.put("type", GENERAL_TYPE);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_CHAT_COMPLETE"})
  @DisplayName("Chat completion with error")
  void testChatCompletionError() throws Exception {
    // Arrange
    when(llmApiService.chatCompletion(any(ChatCompletionRequestDto.class)))
        .thenThrow(new BusinessException("LLM service unavailable"));

    // Act & Assert
    mockMvc
        .perform(
            post("/chat/completions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(chatCompletionRequestDto))
                .with(csrf()))
        .andExpect(status().is5xxServerError());
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_EXECUTE_PROMPT"})
  @DisplayName("Execute prompt with format=true success")
  void testExecutePromptWithFormatTrueSuccess() throws Exception {
    // Arrange
    Map<String, Object> expectedResponse =
        Map.of(
            PromptConstants.RESULT,
            "This is the formatted response",
            "status",
            "success",
            "promptId",
            "test-prompt-1");
    when(llmApiService.executePrompt(anyString(), any(Map.class), eq(true)))
        .thenReturn(expectedResponse);

    // Act & Assert
    mockMvc
        .perform(
            post("/chat/executePrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(executePromptRequest))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.result").value("This is the formatted response"))
        .andExpect(jsonPath("$.status").value(PromptConstants.SUCCESS))
        .andExpect(jsonPath("$.promptId").value("test-prompt-1"))
        .andDo(print());

    verify(llmApiService).executePrompt(anyString(), any(Map.class), eq(true));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_EXECUTE_PROMPT"})
  @DisplayName("Execute prompt with format=false success")
  void testExecutePromptWithFormatFalseSuccess() throws Exception {
    // Arrange
    Map<String, Object> requestWithoutFormat = new HashMap<>();
    requestWithoutFormat.put("promptId", "test-prompt-1");
    requestWithoutFormat.put("variableMap", Map.of("variable1", "value1"));
    // No format field - should default to false

    Map<String, Object> expectedResponse =
        Map.of(
            PromptConstants.RESULT,
            "This is the unformatted response",
            "status",
            "success",
            "promptId",
            "test-prompt-1");
    when(llmApiService.executePrompt(anyString(), any(Map.class), eq(false)))
        .thenReturn(expectedResponse);

    // Act & Assert
    mockMvc
        .perform(
            post("/chat/executePrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(requestWithoutFormat))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.result").value("This is the unformatted response"))
        .andExpect(jsonPath("$.status").value(PromptConstants.SUCCESS))
        .andExpect(jsonPath("$.promptId").value("test-prompt-1"))
        .andDo(print());

    verify(llmApiService).executePrompt(anyString(), any(Map.class), eq(false));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_EXECUTE_PROMPT"})
  @DisplayName("Execute prompt with error")
  void testExecutePromptError() throws Exception {
    // Arrange
    when(llmApiService.executePrompt(anyString(), any(Map.class), anyBoolean()))
        .thenThrow(new BusinessException("Prompt execution failed"));

    // Act & Assert
    mockMvc
        .perform(
            post("/chat/executePrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(executePromptRequest))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(llmApiService).executePrompt(anyString(), any(Map.class), anyBoolean());
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_EXECUTE_PROMPT_V1"})
  @DisplayName("Execute prompt V1 success")
  void testExecutePromptV1Success() throws Exception {
    when(llmApiService.executePromptV1(anyString(), any(Map.class)))
        .thenReturn("This is the V1 response");

    mockMvc
        .perform(
            post("/chat/v1/executePrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    objectMapper.writeValueAsString(
                        Map.of(PROMPT_ID, TEST_PROMPT_1, "variableMap", Map.of("key", "value"))))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath(JSON_PATH_RESULT).value("This is the V1 response"))
        .andDo(print());

    verify(llmApiService).executePromptV1(anyString(), any(Map.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_EXECUTE_PROMPT_V1"})
  @DisplayName("Execute prompt V1 with error")
  void testExecutePromptV1Error() throws Exception {
    // Arrange
    when(llmApiService.executePromptV1(anyString(), any(Map.class)))
        .thenThrow(new BusinessException("V1 execution failed"));

    // Act & Assert
    mockMvc
        .perform(
            post("/chat/v1/executePrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(executePromptV1Request))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(llmApiService).executePromptV1(anyString(), any(Map.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_GENERATE_SYSTEM_PROMPT"})
  @DisplayName("Generate fresh system prompt success")
  void testGenerateFreshSystemPromptSuccess() throws Exception {
    when(llmApiService.generateFreshSystemPrompt("Create a greeting message", GENERAL_TYPE))
        .thenReturn("You are a helpful assistant that creates professional greetings.");

    mockMvc
        .perform(
            post("/chat/generateSystemPrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    objectMapper.writeValueAsString(
                        Map.of("userInput", "Create a greeting message", "type", GENERAL_TYPE)))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(
            jsonPath(JSON_PATH_RESULT)
                .value("You are a helpful assistant that creates professional greetings."))
        .andDo(print());

    verify(llmApiService).generateFreshSystemPrompt("Create a greeting message", GENERAL_TYPE);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_GENERATE_SYSTEM_PROMPT"})
  @DisplayName("Generate improved system prompt success")
  void testGenerateImprovedSystemPromptSuccess() throws Exception {
    when(llmApiService.improveSystemPrompt("Make it more formal", "Hello there!", GENERAL_TYPE))
        .thenReturn("You are a helpful assistant that creates formal and professional greetings.");

    mockMvc
        .perform(
            post("/chat/generateSystemPrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    objectMapper.writeValueAsString(
                        Map.of(
                            "userInput", "Make it more formal",
                            "oldPrompt", "Hello there!",
                            "type", GENERAL_TYPE)))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(
            jsonPath("$.result")
                .value(
                    "You are a helpful assistant that creates formal and professional greetings."))
        .andDo(print());
    verify(llmApiService).improveSystemPrompt("Make it more formal", "Hello there!", "general");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_GENERATE_SYSTEM_PROMPT"})
  @DisplayName("Generate system prompt with error")
  void testGenerateSystemPromptError() throws Exception {
    // Arrange
    when(llmApiService.generateFreshSystemPrompt(anyString(), anyString()))
        .thenThrow(new BusinessException("System prompt generation failed"));

    // Act & Assert
    mockMvc
        .perform(
            post("/chat/generateSystemPrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(generateSystemPromptRequest))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(llmApiService).generateFreshSystemPrompt(anyString(), anyString());
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_GENERATE_SYSTEM_PROMPT"})
  @DisplayName("Improve system prompt with error")
  void testImproveSystemPromptError() throws Exception {
    // Arrange
    when(llmApiService.improveSystemPrompt(anyString(), anyString(), anyString()))
        .thenThrow(new BusinessException("System prompt improvement failed"));

    // Act & Assert
    mockMvc
        .perform(
            post("/chat/generateSystemPrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(improveSystemPromptRequest))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(llmApiService).improveSystemPrompt(anyString(), anyString(), anyString());
  }
}
