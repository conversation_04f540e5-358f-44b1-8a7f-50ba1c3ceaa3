/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.ProcessorDao;
import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Processor;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataAccessException;

@ExtendWith(MockitoExtension.class)
class ProcessorServiceImplTest {
  @Mock ProcessorDao processorDao;
  @Mock CustomFilter customFilter;
  @InjectMocks ProcessorServiceImpl processorServiceImpl;

  private Processor mockProcessor;
  private ProcessorRequestDto mockProcessorDto;

  @BeforeEach
  void setUp() {
    mockProcessor = new Processor();
    mockProcessor.setId(1);
    mockProcessor.setKey("sample Key");
    mockProcessor.setDisplayName("Sample Processor");
    mockProcessor.setIcon("icon");
    mockProcessor.setStyleType("type1");
    mockProcessor.setCategory("categoryA");
    mockProcessor.setSubCategory("subcategoryA");
    mockProcessor.setCreatedTime(new Date());
    mockProcessor.setModifiedTime(new Date());

    mockProcessorDto = new ProcessorRequestDto();
    mockProcessorDto.setKey("sample Key");
    mockProcessorDto.setDisplayName("Sample Processor");
    mockProcessorDto.setIcon("icon");
    mockProcessorDto.setStyleType("type1");
    mockProcessorDto.setCategory("categoryA");
    mockProcessorDto.setSubCategory("subcategoryA");
  }

  @Test
  @DisplayName("Test save success")
  void testSave() {
    when(processorDao.save(any(Processor.class))).thenReturn(mockProcessor);
    Map<String, Object> result = processorServiceImpl.save(mockProcessorDto);
    assertNotNull(result, "Processor result should not be null");
    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Processor save result should be 'success'");
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("save failure DataAccessException")
  void testSaveFailureDuplicateEntry() {
    DataAccessException dataAccessException = new DataAccessException("unique_key violation") {};
    when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.save(mockProcessorDto),
            "Expected BusinessException when processor save fails");
    assertTrue(
        thrown.getMessage().contains("duplicate entry for sample Key"),
        "Exception message should contain 'duplicate entry for sample Key'");
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("Save failure DataAccessException")
  void testSaveFailureDataAccessException() {
    DataAccessException dataAccessException = new DataAccessException("Some DB Error") {};
    when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.save(mockProcessorDto),
            "Expected BusinessException when processor save fails");
    assertEquals(
        "unable to save processor : Some DB Error",
        thrown.getMessage(),
        "Processor save exception message should match expected");
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("save failure Business Exception")
  void testSaveFailureBusinessException() {
    when(processorDao.save(any(Processor.class)))
        .thenThrow(new BusinessException("Unexpected Error"));
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.save(mockProcessorDto),
            "Expected BusinessException when processor save fails");
    assertTrue(
        thrown.getMessage().contains("Unexpected Error"),
        "Exception message should contain 'Unexpected Error'");
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("update success")
  void testUpdateSuccess() {
    when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
    when(processorDao.save(any(Processor.class))).thenReturn(mockProcessor);
    Map<String, Object> result = processorServiceImpl.update("5678", mockProcessorDto);
    assertNotNull(result, "Processor result should not be null");
    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "Processor update result should be 'success'");
    verify(processorDao, times(1)).findById(1);
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("update failure BusinessException")
  void testUpdateFailure() {
    when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
    when(processorDao.save(any(Processor.class)))
        .thenThrow(new BusinessException("Unexpected Error"));
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.update("5678", mockProcessorDto),
            "Expected BusinessException when processor update fails");
    assertTrue(
        thrown.getMessage().contains("Unexpected Error"),
        "Exception message should contain 'Unexpected Error'");
    verify(processorDao, times(1)).findById(1);
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("Update failure Processor Not Found")
  void testUpdateFailureNotFound() {
    when(processorDao.findById(1)).thenReturn(Optional.empty());
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.update("5678", mockProcessorDto),
            "Expected BusinessException when processor update fails");
    assertEquals(
        "processor is not found with id : 1",
        thrown.getMessage(),
        "Processor not found exception message should match expected");
    verify(processorDao, times(1)).findById(1);
    verify(processorDao, never()).save(any(Processor.class)); // Ensure `save` is NOT called
  }

  @Test
  @DisplayName("Update failure DataAccessException (Duplicate Key)")
  void testUpdateFailureDuplicateEntry() {
    when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
    DataAccessException dataAccessException = new DataAccessException("unique_key violation") {};
    when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.update("5678", mockProcessorDto),
            "Expected BusinessException when processor update fails");
    assertEquals(
        "duplicate entry for sample Key",
        thrown.getMessage(),
        "Processor duplicate key exception message should match expected");
    verify(processorDao, times(1)).findById(1);
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("Update failure DataAccessException (Other DB Error)")
  void testUpdateFailureDataAccessException() {
    when(processorDao.findById(1)).thenReturn(Optional.of(mockProcessor));
    DataAccessException dataAccessException = new DataAccessException("Some DB Error") {};
    when(processorDao.save(any(Processor.class))).thenThrow(dataAccessException);
    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.update("345", mockProcessorDto),
            "Expected BusinessException when processor update fails");
    assertEquals(
        "Some DB Error",
        thrown.getMessage(),
        "Processor DB error exception message should match expected");
    verify(processorDao, times(1)).findById(1);
    verify(processorDao, times(1)).save(any(Processor.class));
  }

  @Test
  @DisplayName("Search processor success")
  void testSearchSuccess() {
    when(customFilter.searchByFilter(
            eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(List.of(mockProcessor));
    List<ProcessorResponseDto> result =
        processorServiceImpl.search("filter", 0, 10, "orderBy", "ASC");
    assertNotNull(result, "Processor result should not be null");
    assertEquals(1, result.size(), "Processor list size should match expected");
    verify(customFilter, times(1))
        .searchByFilter(
            eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
  }

  @Test
  @DisplayName("Search processor with null filter")
  void testSearchWithNullFilter() {
    when(customFilter.searchByFilter(
            eq(Processor.class), isNull(), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(List.of(mockProcessor));
    List<ProcessorResponseDto> result = processorServiceImpl.search(null, 0, 10, "orderBy", "ASC");
    assertNotNull(result, "Processor result should not be null");
    assertEquals(1, result.size(), "Processor list size should match expected");
    verify(customFilter, times(1))
        .searchByFilter(
            eq(Processor.class), isNull(), anyString(), anyString(), anyInt(), anyInt());
  }

  @Test
  @DisplayName("Search processor failure")
  void testSearchFailure() {
    when(customFilter.searchByFilter(
            eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt()))
        .thenThrow(new BusinessException("Test exception"));
    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.search("filter", 0, 10, "orderBy", "ASC"),
            "Expected BusinessException when processor search fails");
    assertEquals(
        "Test exception",
        exception.getMessage(),
        "Processor exception message should match expected");
    verify(customFilter, times(1))
        .searchByFilter(
            eq(Processor.class), anyString(), anyString(), anyString(), anyInt(), anyInt());
  }

  @Test
  @DisplayName("Count processor success")
  void testCountSuccess() {
    when(customFilter.countByFilter(eq(Processor.class), anyString())).thenReturn(1L);
    Long result = processorServiceImpl.count("(filter)");
    assertNotNull(result, "Processor result should not be null");
    assertEquals(1L, result, "Processor count should match expected");
    verify(customFilter, times(1)).countByFilter(eq(Processor.class), anyString());
  }

  @Test
  @DisplayName("count processor success with null filter")
  void testCountSuccessWithNullFilter() {
    when(customFilter.countByFilter(eq(Processor.class), isNull())).thenReturn(1L);
    Long result = processorServiceImpl.count(null);
    assertNotNull(result, "Processor result should not be null");
    assertEquals(1L, result, "Processor count should match expected");
    verify(customFilter, times(1)).countByFilter(eq(Processor.class), isNull());
  }

  @Test
  @DisplayName("Count processor failure")
  void testCountFailure() {
    when(customFilter.countByFilter(eq(Processor.class), anyString()))
        .thenThrow(new BusinessException("Count operation failed"));
    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> processorServiceImpl.count("(filter)"),
            "Expected BusinessException when processor count fails");
    assertEquals(
        "Count operation failed",
        exception.getMessage(),
        "Processor count exception message should match expected");
    verify(customFilter, times(1)).countByFilter(eq(Processor.class), anyString());
  }
}
