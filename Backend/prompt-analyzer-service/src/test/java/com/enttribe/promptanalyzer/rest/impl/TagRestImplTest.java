/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.service.TagService;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;

@WebMvcTest(TagRestImpl.class)
class TagRestImplTest {

  @Mock private TagService tagService;

  @InjectMocks private TagRestImpl tagRest; // Replace with actual class name

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testSearchDelegation() {
    List<TagResponseDto> mockList = Collections.singletonList(new TagResponseDto());
    when(tagService.search("filter", 0, 10, "name", "ASC")).thenReturn(mockList);

    List<TagResponseDto> result = tagRest.search("filter", 0, 10, "name", "ASC");

    assertEquals(1, result.size(), "Tag search result size should be 1");
    verify(tagService).search("filter", 0, 10, "name", "ASC");
  }

  @Test
  void testCountDelegation() {
    when(tagService.count("filter")).thenReturn(42L);
    Long count = tagRest.count("filter");

    assertEquals(42L, count, "Tag count should match expected");
    verify(tagService).count("filter");
  }

  @Test
  void testSaveDelegation() {
    TagRequestDto dto = new TagRequestDto();
    Map<String, String> mockResponse = Map.of("status", PromptConstants.SUCCESS);

    when(tagService.save(dto)).thenReturn(mockResponse);
    Map<String, String> result = tagRest.save(dto);

    assertEquals("success", result.get("status"), "Tag save status should be 'success'");
    verify(tagService).save(dto);
  }

  @Test
  void testUpdateDelegation() {
    TagRequestDto dto = new TagRequestDto();
    dto.setId("1");
    Map<String, String> mockResponse = Map.of("status", PromptConstants.SUCCESS);

    when(tagService.update(dto)).thenReturn(mockResponse);
    Map<String, String> result = tagRest.update(dto);

    assertEquals("success", result.get("status"), "Tag save status should be 'success'");
    verify(tagService).update(dto);
  }

  @Test
  void testDeleteDelegation() {
    Map<String, String> mockResponse = Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);

    when(tagService.softDelete("5")).thenReturn(mockResponse);
    Map<String, String> result = tagRest.deleteById("5");

    assertEquals(
        "success", result.get(PromptConstants.RESULT), "Tag save result should be 'success'");
    verify(tagService).softDelete("5");
  }
}
