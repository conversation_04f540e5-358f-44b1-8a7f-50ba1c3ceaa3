/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.config.RestTemplateSingleton;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
class NifiFlowServiceImplTest {

  @Mock private HttpServletRequest httpServletRequest;

  @Mock private RestTemplate restTemplate;

  @InjectMocks private NifiFlowServiceImpl nifiFlowService;

  private static final String TEST_PROCESS_GROUP_ID = "test-process-group-id";
  private static final String TEST_AUTHORIZATION = "Bearer test-token";
  private static final String TEST_NIFI_API_URL = "http://test-nifi-api";
  private static final String TEST_CUSTOM_API_URL = "http://test-custom-api";

  @BeforeEach
  void setUp() {
    ReflectionTestUtils.setField(nifiFlowService, "nifiApiBaseUrl", TEST_NIFI_API_URL);
    ReflectionTestUtils.setField(nifiFlowService, "customApiBaseUrl", TEST_CUSTOM_API_URL);
    ReflectionTestUtils.setField(nifiFlowService, "processGroups", "/process-groups/");

    ReflectionTestUtils.setField(RestTemplateSingleton.class, "restTemplate", restTemplate);
  }

  @Test
  void testGetValidationErrorsProcessGroupSuccess() {
    // Arrange
    when(httpServletRequest.getHeader(PromptConstants.AUTHORIZATION))
        .thenReturn(TEST_AUTHORIZATION);

    String mockResponse =
        "{\"processGroupFlow\":{\"flow\":{\"processors\":[{\"component\":{\"name\":\"TestProcessor\",\"validationStatus\":\"INVALID\",\"validationErrors\":[\"Error1\",\"Error2\"]}}]}}}";

    when(restTemplate.exchange(
            anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

    // Act
    List<String> errors = nifiFlowService.getValidationErrorsProcessGroup(TEST_PROCESS_GROUP_ID);

    // Assert
    assertFalse(errors.isEmpty());
    assertTrue(
        errors.get(0).contains("validation failed for TestProcessor"),
        "First error should indicate validation failed for TestProcessor");
  }

  @Test
  void testGetValidationErrorsProcessGroupEmptyResponse() {
    // Arrange
    when(httpServletRequest.getHeader(PromptConstants.AUTHORIZATION))
        .thenReturn(TEST_AUTHORIZATION);

    String mockResponse = "{\"processGroupFlow\":{\"flow\":{\"processors\":[]}}}";

    when(restTemplate.exchange(
            anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenReturn(new ResponseEntity<>(mockResponse, HttpStatus.OK));

    // Act
    List<String> errors = nifiFlowService.getValidationErrorsProcessGroup(TEST_PROCESS_GROUP_ID);

    // Assert
    assertTrue(errors.isEmpty(), "Errors list should be empty when no processors are present");
  }

  @Test
  void testGetValidationErrorsProcessGroupError() {
    // Arrange
    when(httpServletRequest.getHeader(PromptConstants.AUTHORIZATION))
        .thenReturn(TEST_AUTHORIZATION);

    // Mock RestTemplate to throw RestClientException instead of RuntimeException
    when(restTemplate.exchange(
            anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(String.class)))
        .thenThrow(new RestClientException("Test error"));

    // Act
    List<String> errors = nifiFlowService.getValidationErrorsProcessGroup(TEST_PROCESS_GROUP_ID);

    // Assert
    assertTrue(errors.isEmpty(), "Errors list should be empty when RestClientException is thrown");
  }

  @Test
  void testCreateTriggerNifiError() {
    // Arrange
    when(httpServletRequest.getHeader(PromptConstants.AUTHORIZATION))
        .thenReturn(TEST_AUTHORIZATION);

    NifiFlowDto flowDto = new NifiFlowDto();
    flowDto.setProcessGroupId(TEST_PROCESS_GROUP_ID);
    flowDto.setFlowObject("invalid json");

    // Act
    Map<String, String> result = nifiFlowService.createTriggerNifi(flowDto);

    // Assert
    assertEquals("failed", result.get("status"), "NifiFlow status should be 'failed'");
  }
}
