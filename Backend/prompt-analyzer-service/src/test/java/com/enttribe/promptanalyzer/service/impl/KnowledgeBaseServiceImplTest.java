/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class KnowledgeBaseServiceImplTest {

  private static final String TEST_NANO_ID = "aU2FNPtxXRQfzTk3r_lWF";

  @Mock private KnowledgeBaseDao knowledgeDao;

  @Mock private CustomFilter customFilter;

  @Mock private VectorStore vectorStore;

  @Mock private ApiService apiService;

  @Mock private InferenceManager inferenceManager;

  @Mock private S3Service s3Service;

  @InjectMocks private KnowledgeBaseServiceImpl knowledgeBaseService;

  private KnowledgeBase mockKnowledgeBase;
  private DocumentRequestDto mockDocumentRequestDto;

  private String databaseName = "test_database";
  private String collectionName = "test_collection";

  @BeforeEach
  void setUp() {
    ReflectionTestUtils.setField(knowledgeBaseService, "databaseName", databaseName);
    ReflectionTestUtils.setField(knowledgeBaseService, "collectionName", collectionName);

    mockKnowledgeBase = new KnowledgeBase();
    mockKnowledgeBase.setId(1);
    mockKnowledgeBase.setNanoId(TEST_NANO_ID);
    mockKnowledgeBase.setName("Test Knowledge Base");
    mockKnowledgeBase.setDeleted(false);
    mockKnowledgeBase.setDocId("[]");
    mockKnowledgeBase.setS3FileNames("[]");
    mockKnowledgeBase.setDocMetaData("{}");
    mockKnowledgeBase.setFilter("test-filter-123");

    mockDocumentRequestDto = new DocumentRequestDto();
    mockDocumentRequestDto.setName("Test Document");
    mockDocumentRequestDto.setType("DOCUMENT");
    mockDocumentRequestDto.setFile(new ArrayList<>());
    mockDocumentRequestDto.setTags("tag1,tag2");
    mockDocumentRequestDto.setSimilarityThreshold(0.8);
    mockDocumentRequestDto.setTopK(5);
    mockDocumentRequestDto.setDeleted(false);
    mockDocumentRequestDto.setReturnDirect(false);
    mockDocumentRequestDto.setDescription("Test Description");
    mockDocumentRequestDto.setDocType("PDF");
  }

  @Test
  @DisplayName("Get Knowledge Base By ID - Success")
  void testGetKnowledgeBaseByIdSuccess() {
    when(knowledgeDao.findById(1)).thenReturn(Optional.of(mockKnowledgeBase));

    KnowledgeBaseResponseDto result = knowledgeBaseService.getKnowledgeBaseById(1);

    assertNotNull(result, "KnowledgeBase result should not be null");
    assertEquals(
        mockKnowledgeBase.getName(), result.getName(), "KnowledgeBase name should match expected");
    verify(knowledgeDao, times(1)).findById(1);
  }

  @Test
  @DisplayName("Get Knowledge Base By ID - Not Found")
  void testGetKnowledgeBaseByIdNotFound() {
    when(knowledgeDao.findById(anyInt())).thenReturn(Optional.empty());

    assertThrows(
        ResourceNotFoundException.class,
        () -> knowledgeBaseService.getKnowledgeBaseById(99),
        "knowledge Base is not found");

    verify(knowledgeDao, times(1)).findById(99);
  }

  @Test
  @DisplayName("Soft Delete Knowledge Base - Success")
  void testSoftDeleteSuccess() {
    when(knowledgeDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(mockKnowledgeBase));
    when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);

    Map<String, String> result = knowledgeBaseService.softDelete(TEST_NANO_ID);

    assertNotNull(result, "Result map should not be null");
    assertEquals(
        "success",
        result.get(APIConstants.RESULT),
        "KnowledgeBase save result should be 'success'");
    assertTrue(mockKnowledgeBase.getDeleted(), "KnowledgeBase should be marked as deleted");
    verify(knowledgeDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(knowledgeDao, times(1)).save(mockKnowledgeBase);
  }

  @Test
  @DisplayName("Soft Delete Knowledge Base - Not Found")
  void testSoftDeleteNotFound() {
    when(knowledgeDao.findByNanoId(anyString())).thenReturn(Optional.empty());

    Map<String, String> result = knowledgeBaseService.softDelete("aU2FNPtxXRQfzTk3r_lWF");

    assertNotNull(result, "Result map should not be null");
    assertEquals(
        "failed", result.get(APIConstants.RESULT), "KnowledgeBase save result should be 'failed'");
    verify(knowledgeDao, times(1)).findByNanoId("aU2FNPtxXRQfzTk3r_lWF");
    verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Count Knowledge Bases - Success")
  void testCountKnowledgeBasesSuccess() {
    String filter = "someFilter";
    when(customFilter.countByFilter(KnowledgeBase.class, filter)).thenReturn(10L);

    Long result = knowledgeBaseService.count(filter);

    assertEquals(10L, result, "KnowledgeBase count should match expected");
    verify(customFilter, times(1)).countByFilter(KnowledgeBase.class, filter);
  }

  @Test
  @DisplayName("Search Knowledge Bases - Success")
  void testSearchKnowledgeBasesSuccess() {
    String filter = "someFilter";
    List<KnowledgeBase> mockList = new ArrayList<>();
    mockList.add(mockKnowledgeBase);

    when(customFilter.searchByFilter(
            eq(KnowledgeBase.class), eq(filter), anyString(), anyString(), anyInt(), anyInt()))
        .thenReturn(mockList);

    List<KnowledgeBaseResponseDto> result =
        knowledgeBaseService.search(filter, 0, 10, "name", "asc");

    assertNotNull(result, "Search result should not be null");
    assertFalse(result.isEmpty(), "Search result should not be empty");
    assertEquals(
        mockKnowledgeBase.getName(), result.get(0).getName(), "First result name should match");
    verify(customFilter, times(1))
        .searchByFilter(KnowledgeBase.class, filter, "name", "asc", 0, 10);
  }

  @Test
  @DisplayName("Exists Website URL - Exists")
  void testExistsWebsiteUrlExists() {
    List<KnowledgeBase> mockList = new ArrayList<>();
    mockList.add(mockKnowledgeBase);
    when(knowledgeDao.existsWebsiteUrl(anyString())).thenReturn(mockList);

    Map<String, Object> result = knowledgeBaseService.existsWebsiteUrl("http://test.com");

    assertNotNull(result, "Result map should not be null");
    assertTrue((Boolean) result.get("exists"), "Result map should indicate existence");
    assertTrue(result.containsKey("nanoIds"), "Result map should contain 'nanoIds'");
    verify(knowledgeDao, times(1)).existsWebsiteUrl("http://test.com");
  }

  @Test
  @DisplayName("Exists Website URL - Does Not Exist")
  void testExistsWebsiteUrlDoesNotExist() {
    when(knowledgeDao.existsWebsiteUrl(anyString())).thenReturn(new ArrayList<>());

    Map<String, Object> result = knowledgeBaseService.existsWebsiteUrl("http://test.com");

    assertNotNull(result, "Result map should not be null");
    assertFalse((Boolean) result.get("exists"), "Result map should indicate non-existence");
    assertFalse(result.containsKey("ids"), "Result map should not contain 'ids'");
    verify(knowledgeDao, times(1)).existsWebsiteUrl("http://test.com");
  }

  @Test
  @DisplayName("Exists Knowledge Base Name - Exists")
  void testExistsKnowledgeBaseNameExists() {
    when(knowledgeDao.findByName(anyString())).thenReturn(mockKnowledgeBase);

    Map<String, Boolean> result = knowledgeBaseService.existsKnowledgeBaseName("Test Name");

    assertNotNull(result, "Result map should not be null");
    assertTrue(
        result.get(PromptConstants.RESULT), "Result should be true for successful operation");
    verify(knowledgeDao, times(1)).findByName("Test Name");
  }

  @Test
  @DisplayName("Exists Knowledge Base Name - Does Not Exist")
  void testExistsKnowledgeBaseNameDoesNotExist() {
    when(knowledgeDao.findByName(anyString())).thenReturn(null);

    Map<String, Boolean> result = knowledgeBaseService.existsKnowledgeBaseName("Non Existent Name");

    assertNotNull(result, "Result map should not be null");
    assertFalse(
        result.get(PromptConstants.RESULT), "Result should be false for unsuccessful operation");
    verify(knowledgeDao, times(1)).findByName("Non Existent Name");
  }

  @Test
  @DisplayName("Save Document - Type SQL Success")
  void testSaveDocumentTypeSqlSuccess() {
    mockDocumentRequestDto.setType("SQL");
    mockDocumentRequestDto.setIntegration("test_integration");
    mockDocumentRequestDto.setTables(List.of("table1", "table2"));

    when(apiService.getTableSchema(anyString(), anyString())).thenReturn("table schema");
    when(knowledgeDao.save(any(KnowledgeBase.class)))
        .thenAnswer(
            invocation -> {
              KnowledgeBase kb = invocation.getArgument(0);
              kb.setId(1);
              kb.setNanoId(TEST_NANO_ID);
              return kb;
            });
    doNothing().when(vectorStore).accept(anyList());

    Map<String, String> result = knowledgeBaseService.saveDocument(mockDocumentRequestDto);

    assertNotNull(result, "Save document result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Save document result should be 'success'");
    assertEquals(TEST_NANO_ID, result.get("nanoid"), "NanoId should match expected");

    verify(apiService, times(mockDocumentRequestDto.getTables().size()))
        .getTableSchema(eq("test_integration"), anyString());
    verify(vectorStore, times(mockDocumentRequestDto.getTables().size())).accept(anyList());
    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Save Document - Type WEBSITE Success")
  void testSaveDocumentTypeWebsiteSuccess() {
    mockDocumentRequestDto.setType("WEBSITE");
    mockDocumentRequestDto.setWebsiteUrl("http://testwebsite.com");

    when(apiService.triggerCrawl(anyString())).thenReturn("mockTaskId");
    when(knowledgeDao.save(any(KnowledgeBase.class)))
        .thenAnswer(
            invocation -> {
              KnowledgeBase kb = invocation.getArgument(0);
              kb.setId(1);
              kb.setNanoId(TEST_NANO_ID);
              return kb;
            });

    Map<String, String> result = knowledgeBaseService.saveDocument(mockDocumentRequestDto);

    assertNotNull(result, "Save document result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Save document result should be 'success'");
    assertEquals(TEST_NANO_ID, result.get("nanoid"), "NanoId should match expected");

    verify(apiService, times(1)).triggerCrawl("http://testwebsite.com");
    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Save Document - Type COLLECTION Success")
  void testSaveDocumentTypeCollectionSuccess() {
    mockDocumentRequestDto.setType("COLLECTION");
    mockDocumentRequestDto.setCollectionName("test_collection_name");

    when(knowledgeDao.save(any(KnowledgeBase.class)))
        .thenAnswer(
            invocation -> {
              KnowledgeBase kb = invocation.getArgument(0);
              kb.setId(1);
              kb.setNanoId(TEST_NANO_ID);
              return kb;
            });

    Map<String, String> result = knowledgeBaseService.saveDocument(mockDocumentRequestDto);

    assertNotNull(result, "Save document result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Save document result should be 'success'");
    assertEquals(TEST_NANO_ID, result.get("nanoid"), "NanoId should match expected");

    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Update Document - Knowledge Base Not Found")
  void testUpdateDocumentNotFound() {
    when(knowledgeDao.findByNanoId(anyString())).thenReturn(Optional.empty());

    assertThrows(
        ResourceNotFoundException.class,
        () -> knowledgeBaseService.updateDocument("kb_nano_456", mockDocumentRequestDto),
        "Expected ResourceNotFoundException when updating non-existent document");

    verify(knowledgeDao, times(1)).findByNanoId("kb_nano_456");
    verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Update Document - Type SQL Success")
  void testUpdateDocumentTypeSqlSuccess() throws JsonProcessingException {
    mockKnowledgeBase.setDocId(JsonUtils.convertToJSON(List.of("old_sql_id")));
    mockKnowledgeBase.setTables(JsonUtils.convertToJSON(List.of("new_table1")));
    mockKnowledgeBase.setIntegration("new_test_integration");
    mockKnowledgeBase.setType("SQL");
    mockKnowledgeBase.setName("Test Knowledge Base");
    mockKnowledgeBase.setSimilarityThreshold(0.8);
    mockKnowledgeBase.setTopK(5);
    when(knowledgeDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(mockKnowledgeBase));

    mockDocumentRequestDto.setType("SQL");
    mockDocumentRequestDto.setIntegration("new_test_integration");
    mockDocumentRequestDto.setTables(List.of("new_table1"));

    when(apiService.getTableSchema(anyString(), anyString())).thenReturn("new table schema");
    when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);
    doNothing().when(vectorStore).accept(anyList());
    doNothing().when(vectorStore).delete(any(Filter.Expression.class));
    doNothing().when(vectorStore).delete(anyList());

    Map<String, String> result =
        knowledgeBaseService.updateDocument(TEST_NANO_ID, mockDocumentRequestDto);

    assertNotNull(result, "Update document result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Update document result should be 'success'");

    verify(knowledgeDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(apiService, times(mockDocumentRequestDto.getTables().size()))
        .getTableSchema(eq("new_test_integration"), anyString());
    verify(vectorStore, times(mockDocumentRequestDto.getTables().size())).accept(anyList());
    verify(vectorStore, times(1)).delete(List.of("old_sql_id"));
    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Update Document - Type WEBSITE Success")
  void testUpdateDocumentTypeWebsiteSuccess() throws JsonProcessingException {
    mockKnowledgeBase.setDocId(JsonUtils.convertToJSON(List.of("old_website_id")));
    mockKnowledgeBase.setFilter("filter");
    when(knowledgeDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(mockKnowledgeBase));

    mockDocumentRequestDto.setType("WEBSITE");
    mockDocumentRequestDto.setWebsiteUrl("http://newwebsite.com");

    when(apiService.triggerCrawl(anyString())).thenReturn("newMockTaskId");
    when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);
    doNothing().when(vectorStore).delete(any(Filter.Expression.class));
    doNothing().when(vectorStore).delete(anyList());

    Map<String, String> result =
        knowledgeBaseService.updateDocument(TEST_NANO_ID, mockDocumentRequestDto);

    assertNotNull(result, "Update document result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Update document result should be 'success'");

    verify(knowledgeDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(apiService, times(1)).triggerCrawl("http://newwebsite.com");
    verify(vectorStore, times(1)).delete(List.of("old_website_id"));
    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Update Document - Type COLLECTION Success")
  void testUpdateDocumentTypeCollectionSuccess() throws JsonProcessingException {
    mockKnowledgeBase.setDocId(JsonUtils.convertToJSON(List.of("old_collection_id")));
    mockKnowledgeBase.setCollectionName("new_test_collection_name");
    mockKnowledgeBase.setType("COLLECTION");
    mockKnowledgeBase.setName("Test Knowledge Base");
    mockKnowledgeBase.setSimilarityThreshold(0.8);
    mockKnowledgeBase.setTopK(5);
    when(knowledgeDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(mockKnowledgeBase));

    mockDocumentRequestDto.setType("COLLECTION");
    mockDocumentRequestDto.setCollectionName("new_test_collection_name");

    when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);
    doNothing().when(vectorStore).delete(any(Filter.Expression.class));
    doNothing().when(vectorStore).delete(anyList());

    Map<String, String> result =
        knowledgeBaseService.updateDocument(TEST_NANO_ID, mockDocumentRequestDto);

    assertNotNull(result, "Update document result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Update document result should be 'success'");

    verify(knowledgeDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(vectorStore, times(1)).delete(List.of("old_collection_id"));
    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Get Knowledge Base By IDs - Success")
  void tesGetKnowledgeBaseByIdsSuccess() {
    List<String> ids = List.of(TEST_NANO_ID, "kb_nano_456");
    KnowledgeBase kb2 = new KnowledgeBase();
    kb2.setId(2);
    kb2.setNanoId("kb_nano_456");
    kb2.setName("KB Two");
    List<KnowledgeBase> mockList = List.of(mockKnowledgeBase, kb2);

    when(knowledgeDao.findAllByNanoIds(ids)).thenReturn(mockList);

    List<KnowledgeBaseSdkDto> result = knowledgeBaseService.getKnowledgeBaseByIds(ids);

    assertNotNull(result, "KnowledgeBaseSdkDto list should not be null");
    assertEquals(2, result.size(), "KnowledgeBaseSdkDto list size should be 2");
    assertEquals(
        "Test Knowledge Base", result.get(0).getName(), "First KnowledgeBase name should match");
    assertEquals("KB Two", result.get(1).getName(), "Second KnowledgeBase name should match");
    verify(knowledgeDao, times(1)).findAllByNanoIds(ids);
  }

  @Test
  @DisplayName("Update Tag By ID - Success")
  void testUpdateTagByIdSuccess() {
    Map<String, String> tags = Map.of("tags", "newtag1,newtag2");
    when(knowledgeDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(mockKnowledgeBase));
    when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);

    Map<String, String> result = knowledgeBaseService.updateTagById(TEST_NANO_ID, tags);

    assertNotNull(result, "Update tag result should not be null");
    assertEquals(
        APIConstants.SUCCESS,
        result.get(APIConstants.RESULT),
        "Update tag result should be 'success'");
    assertEquals("newtag1,newtag2", mockKnowledgeBase.getTag(), "Tag should be updated");
    verify(knowledgeDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(knowledgeDao, times(1)).save(mockKnowledgeBase);
  }

  @Test
  @DisplayName("Update Tag By ID - Knowledge Base Not Found")
  void testUpdateTagByIdNotFound() {
    Map<String, String> tags = Map.of("tags", "newtag1,newtag2");
    when(knowledgeDao.findByNanoId(anyString())).thenReturn(Optional.empty());

    assertThrows(
        BusinessException.class,
        () -> knowledgeBaseService.updateTagById("kb_nano_999", tags),
        "Expected BusinessException for non-existent KnowledgeBase");

    verify(knowledgeDao, times(1)).findByNanoId("kb_nano_999");
    verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Save Content - Success")
  void testSaveContentSuccess() {
    mockDocumentRequestDto.setContent("Some raw text content");
    mockDocumentRequestDto.setType("CONTENT");

    VectorResponseDto mockVectorResponse =
        new VectorResponseDto("contentFilter", Map.of("key", "value"), List.of("content_id"));

    when(knowledgeDao.save(any(KnowledgeBase.class)))
        .thenAnswer(
            invocation -> {
              KnowledgeBase kb = invocation.getArgument(0);
              kb.setFilter(mockVectorResponse.getFilter());
              kb.setDocMetaData(JsonUtils.convertToJSON(mockVectorResponse.getMetadata()));
              kb.setDocId(JsonUtils.convertToJSON(mockVectorResponse.getDocIds()));
              kb.setId(1);
              kb.setNanoId(TEST_NANO_ID);
              return kb;
            });

    Map<String, String> result = knowledgeBaseService.saveContent(mockDocumentRequestDto);

    assertNotNull(result, "Save content result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Save content result should be 'success'");
    assertEquals(TEST_NANO_ID, result.get("nanoid"), "NanoId should match expected");

    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
    ArgumentCaptor<KnowledgeBase> kbCaptor = ArgumentCaptor.forClass(KnowledgeBase.class);
    verify(knowledgeDao).save(kbCaptor.capture());
    KnowledgeBase savedKb = kbCaptor.getValue();
    assertEquals(mockVectorResponse.getFilter(), savedKb.getFilter());
    assertEquals(
        JsonUtils.convertToJSON(mockVectorResponse.getMetadata()), savedKb.getDocMetaData());
    assertEquals(JsonUtils.convertToJSON(mockVectorResponse.getDocIds()), savedKb.getDocId());
  }

  @Test
  @DisplayName("Milvus Import Csv - Success")
  void testMilvusImportCsvSuccess() {
    String csvContent = "header1,header2,content\nvalue1,value2,content1\nvalue3,value4,content2";
    MockMultipartFile mockFile =
        new MockMultipartFile("file", "test.csv", "text/csv", csvContent.getBytes());

    doNothing().when(vectorStore).delete(any(Filter.Expression.class));
    doNothing().when(vectorStore).accept(anyList());

    ResponseEntity<String> result = knowledgeBaseService.milvusImportCsv(mockFile);

    assertNotNull(result, "Milvus import CSV result should not be null");
    assertEquals(
        "CSV data imported successfully.", result.getBody(), "CSV import message should match");
    verify(vectorStore, times(11)).delete(any(Filter.Expression.class));
    verify(vectorStore, times(1)).accept(anyList());
  }

  @Test
  @DisplayName("Milvus Import Csv - No Content Column")
  void testMilvusImportCsvNoContentColumn() {
    String csvContent = "header1,header2\nvalue1,value2";
    MockMultipartFile mockFile =
        new MockMultipartFile("file", "test.csv", "text/csv", csvContent.getBytes());

    ResponseEntity<String> result = knowledgeBaseService.milvusImportCsv(mockFile);

    assertNotNull(result, "Milvus import CSV result should not be null");
    assertEquals(
        400,
        result.getStatusCode().value(),
        "Status code should be 400 for missing content column");
    assertEquals(
        "CSV file does not contain 'content' column.",
        result.getBody(),
        "Error message should match");
    verify(vectorStore, never()).delete(anyString());
    verify(vectorStore, never()).accept(anyList());
  }

  @Test
  @DisplayName("Milvus Import Csv - No Valid Content Rows")
  void testMilvusImportCsvNoValidContentRows() {
    String csvContent = "header1,header2,content\nvalue1,value2,";
    MockMultipartFile mockFile =
        new MockMultipartFile("file", "test.csv", "text/csv", csvContent.getBytes());

    ResponseEntity<String> result = knowledgeBaseService.milvusImportCsv(mockFile);

    assertNotNull(result, "Milvus import CSV result should not be null");
    assertEquals(
        400, result.getStatusCode().value(), "Status code should be 400 for no valid content rows");
    assertEquals("No valid content found in CSV.", result.getBody(), "Error message should match");
    verify(vectorStore, never()).delete(anyString());
    verify(vectorStore, never()).accept(anyList());
  }

  @Test
  @DisplayName("Update Return Direct - Success")
  void testUpdateReturnDirectSuccess() {
    when(knowledgeDao.findByNanoId(TEST_NANO_ID)).thenReturn(Optional.of(mockKnowledgeBase));
    when(knowledgeDao.save(any(KnowledgeBase.class))).thenReturn(mockKnowledgeBase);

    Map<String, String> result = knowledgeBaseService.updateReturnDirect(TEST_NANO_ID, true);

    assertNotNull(result, "Update return direct result should not be null");
    assertEquals(
        APIConstants.SUCCESS,
        result.get(APIConstants.RESULT),
        "Update return direct result should be 'success'");
    assertTrue(mockKnowledgeBase.getReturnDirect(), "ReturnDirect should be true");
    verify(knowledgeDao, times(1)).findByNanoId(TEST_NANO_ID);
    verify(knowledgeDao, times(1)).save(mockKnowledgeBase);
  }

  @Test
  @DisplayName("Update Return Direct - Knowledge Base Not Found")
  void testUpdateReturnDirectNotFound() {
    when(knowledgeDao.findByNanoId(anyString())).thenReturn(Optional.empty());

    BusinessException ex =
        assertThrows(
            BusinessException.class,
            () -> knowledgeBaseService.updateReturnDirect("kb_nano_999", true),
            "Expected BusinessException for non-existent KnowledgeBase");
    assertEquals("Knowledge base not found", ex.getMessage(), "Exception message should match");
    verify(knowledgeDao, times(1)).findByNanoId("kb_nano_999");
    verify(knowledgeDao, never()).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Save WebSite - Success")
  void testSaveWebSiteSuccess() {
    mockDocumentRequestDto.setType("WEBSITE");
    mockDocumentRequestDto.setWebsiteUrl("http://testwebsite.com/save");

    when(apiService.triggerCrawl(anyString())).thenReturn("mockTaskId");
    when(knowledgeDao.save(any(KnowledgeBase.class)))
        .thenAnswer(
            invocation -> {
              KnowledgeBase kb = invocation.getArgument(0);
              kb.setId(1);
              kb.setNanoId("kb_nano_123");
              return kb;
            });

    Map<String, String> result = knowledgeBaseService.saveWebSite(mockDocumentRequestDto);

    assertNotNull(result, "Save website result should not be null");
    assertEquals(
        PromptConstants.SUCCESS,
        result.get(PromptConstants.RESULT),
        "Save website result should be 'success'");
    assertEquals("kb_nano_123", result.get("nanoid"), "NanoId should match expected");

    verify(apiService, times(1)).triggerCrawl("http://testwebsite.com/save");
    verify(knowledgeDao, times(1)).save(any(KnowledgeBase.class));
  }

  @Test
  @DisplayName("Get Tables Name - Success")
  void testGetTablesNameSuccess() {
    List<String> mockTableNames = List.of("table1", "table2");
    when(apiService.getTablesName(anyString())).thenReturn(mockTableNames);

    List<String> result = knowledgeBaseService.getTablesName("test_integration");

    assertNotNull(result, "Get tables name result should not be null");
    assertEquals(2, result.size(), "Tables name list size should be 2");
    assertEquals("table1", result.get(0), "First table name should match");
    assertEquals("table2", result.get(1), "Second table name should match");
    verify(apiService, times(1)).getTablesName("test_integration");
  }
}
