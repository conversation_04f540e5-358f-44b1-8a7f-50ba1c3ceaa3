/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import static org.hamcrest.Matchers.hasSize;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.enttribe.promptanalyzer.dto.prompt.*;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.PromptService;
import com.enttribe.promptanalyzer.util.PromptFooUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.multipart.MultipartFile;

@WebMvcTest(PromptRestImpl.class)
class PromptRestImplTest {

  @Autowired private MockMvc mockMvc;

  @MockitoBean private PromptService promptService;

  @Autowired private ObjectMapper objectMapper;

  private PromptDto basePromptDto;
  private PromptRequestDto basePromptRequestDto;

  @BeforeEach
  void setUp() {
    basePromptDto = new PromptDto();
    basePromptDto.setApplication("testApplication");
    basePromptDto.setPromptId("test-prompt-id");
    basePromptDto.setTemperature(1.0);
    basePromptDto.setMaxTokens(1000);
    basePromptDto.setTopP(0.7);
    basePromptDto.setName("Test Prompt");

    basePromptRequestDto = new PromptRequestDto();
    basePromptRequestDto.setApplication("testApplication");
    basePromptRequestDto.setName("Test Prompt");
    basePromptRequestDto.setCategory("testCategory");
    basePromptRequestDto.setStatus("active");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Health check endpoint success")
  void testSuccess() throws Exception {
    mockMvc
        .perform(get("/prompt/ping"))
        .andExpect(status().isOk())
        .andExpect(content().json("{\"status\":\"success\"}"))
        .andDo(print());
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Save prompt success")
  void testPromptSave() throws Exception {
    // Arrange
    when(promptService.savePrompt(any(PromptDto.class))).thenReturn(Map.of("status", "saved"));

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/save")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptDto))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(content().json("{\"status\":\"saved\"}"))
        .andDo(print());

    verify(promptService).savePrompt(any(PromptDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Save prompt with validation error")
  void testPromptSaveValidationError() throws Exception {
    // Arrange
    when(promptService.savePrompt(any(PromptDto.class)))
        .thenThrow(new BusinessException("Validation failed"));

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/save")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptDto))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(promptService).savePrompt(any(PromptDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get versions of prompt success")
  void testGetVersionsOfPrompt() throws Exception {
    // Arrange
    List<PromptVersionDetailsDto> versions =
        List.of(new PromptVersionDetailsDto("1", "1.0"), new PromptVersionDetailsDto("2", "2.0"));
    when(promptService.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenReturn(versions);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/getVersionsOfPrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptRequestDto))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0].id").value("1"))
        .andExpect(jsonPath("$[0].version").value("1.0"))
        .andDo(print());

    verify(promptService).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get versions of prompt with error")
  void testGetVersionsOfPromptError() throws Exception {
    // Arrange
    when(promptService.getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString()))
        .thenThrow(new BusinessException("Prompt not found"));

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/getVersionsOfPrompt")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptRequestDto))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(promptService).getVersionsOfPrompt(anyString(), anyString(), anyString(), anyString());
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Edit prompt success")
  void testPromptEdit() throws Exception {
    // Arrange
    Map<String, String> response = Map.of("status", "updated");
    when(promptService.updatePrompt(any(PromptDto.class))).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/edit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptDto))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(content().json("{\"status\":\"updated\"}"))
        .andDo(print());

    verify(promptService).updatePrompt(any(PromptDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Edit prompt with error")
  void testPromptEditError() throws Exception {
    // Arrange
    when(promptService.updatePrompt(any(PromptDto.class)))
        .thenThrow(new BusinessException("Update failed"));

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/edit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptDto))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(promptService).updatePrompt(any(PromptDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Delete prompt by ID success")
  void testDeleteById() throws Exception {
    // Arrange
    Map<String, String> request = Map.of("id", "1");
    Map<String, String> response = Map.of("status", "deleted");
    when(promptService.softDelete("1")).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/deleteById")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(content().json("{\"status\":\"deleted\"}"))
        .andDo(print());

    verify(promptService).softDelete("1");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Delete prompt by ID with error")
  void testDeleteByIdError() throws Exception {
    // Arrange
    Map<String, String> request = Map.of("id", "999");
    when(promptService.softDelete("999")).thenThrow(new BusinessException("Prompt not found"));

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/deleteById")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(promptService).softDelete("999");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get prompt basic details by application success")
  void testGetPromptBasicDetailByApplication() throws Exception {
    // Arrange
    Map<String, String> request = Map.of("application", "testApp");
    List<Map<String, String>> response = List.of(Map.of("name", "Test Prompt", "status", "active"));
    when(promptService.getPromptBasicDetailByApplication("testApp")).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/getPromptBasicDetailByApplication")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(1)))
        .andExpect(jsonPath("$[0].name").value("Test Prompt"))
        .andDo(print());

    verify(promptService).getPromptBasicDetailByApplication("testApp");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get prompt basic details by application returns empty")
  void testGetPromptBasicDetailByApplicationEmpty() throws Exception {
    // Arrange
    Map<String, String> request = Map.of("application", "nonexistent");
    when(promptService.getPromptBasicDetailByApplication("nonexistent"))
        .thenReturn(Collections.emptyList());

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/getPromptBasicDetailByApplication")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(0)));

    verify(promptService).getPromptBasicDetailByApplication("nonexistent");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get prompt by ID success")
  void testGetPromptById() throws Exception {
    // Arrange
    Map<String, Integer> request = Map.of("id", 1);
    PromptConvertorDto response = PromptConvertorDto.builder().id("1").name("Test Prompt").build();
    when(promptService.getPromptById("1")).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/viewById")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id").value(1))
        .andExpect(jsonPath("$.name").value("Test Prompt"))
        .andDo(print());

    verify(promptService).getPromptById("1");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get prompt by ID not found")
  void testGetPromptByIdNotFound() throws Exception {
    // Arrange
    Map<String, String> request = Map.of("id", "999");
    when(promptService.getPromptById("999")).thenThrow(new BusinessException("Prompt not found"));

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/viewById")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .with(csrf()))
        .andExpect(status().isBadRequest());

    verify(promptService).getPromptById("999");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get SDK prompt by ID success")
  void testGetPromptByIdSdk() throws Exception {
    // Arrange
    PromptDtoSdk response = PromptDtoSdk.builder().id("1").promptName("Test Prompt").build();
    when(promptService.findPromptById(1)).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(get("/prompt/getPromptById/{id}", 1).contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id").value("1"))
        .andExpect(jsonPath("$.promptName").value("Test Prompt"))
        .andDo(print());

    verify(promptService).findPromptById(1);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get SDK prompt by ID not found")
  void testGetPromptByIdSdkNotFound() throws Exception {
    // Arrange
    when(promptService.findPromptById(999)).thenThrow(new BusinessException("Prompt not found"));

    // Act & Assert
    mockMvc
        .perform(get("/prompt/getPromptById/{id}", 999).contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(promptService).findPromptById(999);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Check prompt exists success")
  void testExists() throws Exception {
    // Arrange
    Map<String, Object> response = Map.of("exists", true);
    when(promptService.exists(any(PromptRequestDto.class))).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/exists")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptRequestDto))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.exists").value(true))
        .andDo(print());

    verify(promptService).exists(any(PromptRequestDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Check prompt exists - not found")
  void testExistsNotFound() throws Exception {
    // Arrange
    Map<String, Object> response = Map.of("exists", false);
    when(promptService.exists(any(PromptRequestDto.class))).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/exists")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(basePromptRequestDto))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.exists").value(false));

    verify(promptService).exists(any(PromptRequestDto.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get distinct applications success")
  void testGetDistinctApplications() throws Exception {
    // Arrange
    List<String> response = List.of("app1", "app2");
    when(promptService.getDistinctApplications(anyString())).thenReturn(response);

    // Act & Assert
    mockMvc
        .perform(get("/prompt/getDistinctApplications").param("applicationName", "test"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(2)))
        .andExpect(jsonPath("$[0]").value("app1"))
        .andDo(print());

    verify(promptService).getDistinctApplications("test");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get distinct applications returns empty")
  void testGetDistinctApplicationsEmpty() throws Exception {
    // Arrange
    when(promptService.getDistinctApplications(anyString())).thenReturn(Collections.emptyList());

    // Act & Assert
    mockMvc
        .perform(get("/prompt/getDistinctApplications").param("applicationName", "nonexistent"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(0)));

    verify(promptService).getDistinctApplications("nonexistent");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get distinct categories by app success")
  void testGetDistinctCategoriesByAppSuccess() throws Exception {
    // Arrange
    String applicationName = "testApp";
    List<String> expectedCategories = List.of("category1", "category2", "category3");
    when(promptService.getDistinctCategoriesByApp(applicationName)).thenReturn(expectedCategories);

    // Act & Assert
    mockMvc
        .perform(
            get("/prompt/getDistinctCategoriesByApp")
                .param("applicationName", applicationName)
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(3)))
        .andExpect(jsonPath("$[0]").value("category1"))
        .andExpect(jsonPath("$[1]").value("category2"))
        .andExpect(jsonPath("$[2]").value("category3"))
        .andDo(print());

    verify(promptService, times(1)).getDistinctCategoriesByApp(applicationName);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Filter prompts")
  void testFilter() throws Exception {
    // Arrange
    Map<String, Object> filterMap = Map.of("key", "value");
    List<Prompt> promptList = List.of(new Prompt());
    List<PromptFooDto> dtoList = List.of(new PromptFooDto());

    when(promptService.filter(filterMap)).thenReturn(promptList);

    // Act & Assert
    try (MockedStatic<PromptFooUtils> utilities = mockStatic(PromptFooUtils.class)) {
      utilities.when(() -> PromptFooUtils.getPromptFooDtoList(promptList)).thenReturn(dtoList);

      mockMvc
          .perform(
              post("/prompt/filter")
                  .contentType(MediaType.APPLICATION_JSON)
                  .content(objectMapper.writeValueAsString(filterMap))
                  .with(csrf()))
          .andExpect(status().isOk())
          .andDo(print());

      verify(promptService).filter(filterMap);
    }
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Search prompts")
  void testSearch() throws Exception {
    // Arrange
    List<PromptConvertorDto> expected = List.of(new PromptConvertorDto());

    when(promptService.search("filter", 0, 10, "name", "asc")).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(
            get("/prompt/search")
                .param("filter", "filter")
                .param("offset", "0")
                .param("size", "10")
                .param("orderBy", "name")
                .param("orderType", "asc"))
        .andExpect(status().isOk())
        .andDo(print());

    verify(promptService).search("filter", 0, 10, "name", "asc");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Search prompts V1")
  void testSearchV1() throws Exception {
    // Arrange
    List<Prompt> promptList = List.of(new Prompt());
    List<PromptFooDto> dtoList = List.of(new PromptFooDto());

    when(promptService.searchV1("filter", 0, 10, "name", "asc")).thenReturn(promptList);

    // Act & Assert
    try (MockedStatic<PromptFooUtils> utilities = mockStatic(PromptFooUtils.class)) {
      utilities.when(() -> PromptFooUtils.getPromptFooDtoList(promptList)).thenReturn(dtoList);

      mockMvc
          .perform(
              get("/prompt/v1/search")
                  .param("filter", "filter")
                  .param("offset", "0")
                  .param("size", "10")
                  .param("orderBy", "name")
                  .param("orderType", "asc"))
          .andExpect(status().isOk())
          .andDo(print());

      verify(promptService).searchV1("filter", 0, 10, "name", "asc");
    }
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Count prompts")
  void testCount() throws Exception {
    // Arrange
    when(promptService.count("filter")).thenReturn(42L);

    // Act & Assert
    mockMvc
        .perform(get("/prompt/count").param("filter", "filter"))
        .andExpect(status().isOk())
        .andExpect(content().string("42"))
        .andDo(print());

    verify(promptService).count("filter");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get prompts by application")
  void testGetPromptByApplication() throws Exception {
    // Arrange
    List<PromptDtoSdk> expected = List.of(new PromptDtoSdk());

    when(promptService.getPromptByApplication("appName")).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(get("/prompt/getPromptByApplication/{applicationName}", "appName"))
        .andExpect(status().isOk())
        .andDo(print());

    verify(promptService).getPromptByApplication("appName");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Export CSV")
  void testExportCSV() throws Exception {
    // Arrange
    ResponseEntity<Resource> expected = ResponseEntity.ok().build();

    when(promptService.exportPrompt("appName")).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(get("/prompt/exportPrompt/{appName}", "appName"))
        .andExpect(status().isOk())
        .andDo(print());

    verify(promptService).exportPrompt("appName");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Export prompts by IDs")
  void testExportPromptsByIds() throws Exception {
    // Arrange
    List<String> ids = List.of("1", "2");
    ResponseEntity<Resource> expected = ResponseEntity.ok().build();

    when(promptService.exportPromptsByIds(ids)).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/exportPromptsByIds")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(ids))
                .with(csrf()))
        .andExpect(status().isOk())
        .andDo(print());

    verify(promptService).exportPromptsByIds(ids);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Import prompt")
  void testImportPrompt() throws Exception {
    // Arrange
    ResponseEntity<Resource> expected = ResponseEntity.ok().build();
    when(promptService.importPrompt(any(MultipartFile.class))).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(
            multipart("/prompt/importPrompt").file("file", "test content".getBytes()).with(csrf()))
        .andExpect(status().isOk())
        .andDo(print());

    verify(promptService).importPrompt(any(MultipartFile.class));
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_WRITE"})
  @DisplayName("Update tag by ID")
  void testUpdateTagById() throws Exception {
    // Arrange
    String id = "123";
    Map<String, String> tags = Map.of("tag1", "value1");
    Map<String, String> expected = Map.of("status", "updated");

    when(promptService.updateTagById(id, tags)).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(
            post("/prompt/updateTagById/{id}", id)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(tags))
                .with(csrf()))
        .andExpect(status().isOk())
        .andExpect(content().json("{\"status\":\"updated\"}"))
        .andDo(print());

    verify(promptService).updateTagById(id, tags);
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Get prompt by name")
  void testGetPromptByName() throws Exception {
    // Arrange
    PromptDto expected = new PromptDto();

    when(promptService.getPromptByName("promptName")).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(get("/prompt/findByName/{promptName}", "promptName"))
        .andExpect(status().isOk())
        .andDo(print());

    verify(promptService).getPromptByName("promptName");
  }

  @Test
  @WithMockUser(
      username = "testUser",
      roles = {"API_PROMPT_READ"})
  @DisplayName("Find prompt by ID")
  void testFindPromptById() throws Exception {
    // Arrange
    PromptDto expected = new PromptDto();

    when(promptService.fetchPromptById("1")).thenReturn(expected);

    // Act & Assert
    mockMvc
        .perform(get("/prompt/findPromptById/{id}", 1))
        .andExpect(status().isOk())
        .andDo(print());

    verify(promptService).fetchPromptById("1");
  }
}
