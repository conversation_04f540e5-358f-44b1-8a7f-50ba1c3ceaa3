/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.McpServerDao;
import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

class McpServerServiceImplTest {

  @Mock private McpServerDao mcpServerDao;

  @Mock private CustomFilter customFilter;

  @InjectMocks private McpServerServiceImpl mcpServerService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testCreateSuccess() {
    McpServerDto dto = new McpServerDto();
    dto.setName("Test Server");

    when(mcpServerDao.save(any(McpServer.class))).thenReturn(new McpServer());

    Map<String, String> result = mcpServerService.create(dto);

    assertEquals(
        "success", result.get(PromptConstants.RESULT), "McpServer save result should be 'success'");
    verify(mcpServerDao, times(1)).save(any(McpServer.class));
  }

  @Test
  void testCreateThrowsException() {
    McpServerDto dto = new McpServerDto();
    dto.setName("Failing Server");

    doThrow(new RuntimeException("DB error")).when(mcpServerDao).save(any());

    BusinessException ex =
        assertThrows(
            BusinessException.class,
            () -> mcpServerService.create(dto),
            "Expected BusinessException when MCP Server creation fails");
    assertTrue(
        ex.getMessage().contains("Unable to save MCP Server"),
        "Exception message should contain 'Unable to save MCP Server'");
  }

  @Test
  void testUpdateSuccess() {
    // Arrange
    McpServerDto dto = new McpServerDto();
    dto.setId("nano112wsx23456");
    dto.setName("Updated Server");
    dto.setIcon("new-icon.png");
    dto.setDescription("Updated description");
    dto.setUrl("http://new-url.com");
    dto.setIsCustomServer(true);
    dto.setCommandToRun("new-command.sh");

    McpServer existingServer = new McpServer();
    existingServer.setNanoId("nano112wsx23456"); // Matching nanoId
    existingServer.setId(1);

    when(mcpServerDao.findByNanoId("nano112wsx23456")).thenReturn(Optional.of(existingServer));
    when(mcpServerDao.save(any(McpServer.class))).thenReturn(existingServer);

    // Act
    Map<String, String> result = mcpServerService.update(dto);

    // Assert
    assertEquals("success", result.get(PromptConstants.RESULT));
    verify(mcpServerDao).save(any(McpServer.class));
  }

  @Test
  void testUpdateWithNullId() {
    McpServerDto dto = new McpServerDto(); // no ID set

    BusinessException ex =
        assertThrows(
            BusinessException.class,
            () -> mcpServerService.update(dto),
            "Expected BusinessException when MCP Server update fails due to null ID");
    assertEquals(
        "Unable to update MCP Server: Server ID cannot be null for update operation",
        ex.getMessage(),
        "Exception message should match expected");
  }

  @Test
  void testUpdateNotFound() {
    McpServerDto dto = new McpServerDto();
    dto.setId("1");

    when(mcpServerDao.findById(1)).thenReturn(Optional.empty());

    BusinessException ex =
        assertThrows(
            BusinessException.class,
            () -> mcpServerService.update(dto),
            "Expected BusinessException when MCP Server update fails due to not found");
    assertEquals(
        "Unable to update MCP Server: MCP Server not found with ID: 1",
        ex.getMessage(),
        "Exception message should match expected");
  }

  @Test
  void testSearchSuccess() {
    // Arrange
    McpServer mockServer = new McpServer();
    when(customFilter.searchByFilter(any(), any(), any(), any(), any(), any()))
        .thenReturn(List.of(mockServer));

    // Act
    List<McpServerDto> result = mcpServerService.search("name:Test", 0, 10, "name", "asc");

    // Assert
    assertEquals(1, result.size(), "McpServer list size should match expected");
    verify(customFilter)
        .searchByFilter(any(), eq("name:Test"), eq("name"), eq("asc"), eq(0), eq(10));
  }

  @Test
  void testSearchFailure() {
    // Arrange
    when(customFilter.searchByFilter(any(), any(), any(), any(), any(), any()))
        .thenThrow(new RuntimeException("Database error"));

    // Act & Assert
    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> mcpServerService.search("name:Test", 0, 10, "name", "asc"),
            "Expected BusinessException when MCP Server search fails");

    assertTrue(
        exception.getMessage().contains("Failed to search MCP Servers"),
        "Exception message should contain 'Failed to search MCP Servers'");
    verify(customFilter)
        .searchByFilter(any(), eq("name:Test"), eq("name"), eq("asc"), eq(0), eq(10));
  }

  @Test
  void testCountSuccess() {
    // Arrange
    when(customFilter.countByFilter(any(), any())).thenReturn(5L);

    // Act
    Long count = mcpServerService.count("active:true");

    // Assert
    assertEquals(5L, count, "McpServer count should match expected");
    verify(customFilter).countByFilter(McpServer.class, "active:true");
  }

  @Test
  void testCountFailure() {
    // Arrange
    when(customFilter.countByFilter(any(), any())).thenThrow(new RuntimeException("DB down"));

    // Act & Assert
    BusinessException exception =
        assertThrows(
            BusinessException.class,
            () -> mcpServerService.count("active:true"),
            "Expected BusinessException when MCP Server count fails");

    assertTrue(
        exception.getMessage().contains("Failed to count MCP Servers"),
        "Exception message should contain 'Failed to count MCP Servers'");
    verify(customFilter).countByFilter(McpServer.class, "active:true");
  }

  @Test
  void testSoftDeleteSuccess() {
    McpServer server = new McpServer();
    server.setId(1);

    when(mcpServerDao.findByNanoId("1")).thenReturn(Optional.of(server));
    when(mcpServerDao.save(any())).thenReturn(server);

    Map<String, String> result = mcpServerService.softDelete("nano112wsx23456");

    assertEquals(
        "success",
        result.get(PromptConstants.RESULT),
        "McpServer delete result should be 'success'");
    verify(mcpServerDao).save(any(McpServer.class));
  }

  @Test
  void testSoftDeleteNotFound() {
    when(mcpServerDao.findByNanoId("999")).thenReturn(Optional.empty());

    Map<String, String> result = mcpServerService.softDelete("999");

    assertEquals(
        "failed",
        result.get(PromptConstants.RESULT),
        "McpServer operation result should be 'failed'");
    assertTrue(
        result.get("message").contains("MCP Server not found with ID:"),
        "Result message should indicate MCP Server not found");
  }

  @Test
  void testSoftDeleteFailure() {
    when(mcpServerDao.findByNanoId("1")).thenThrow(new RuntimeException("DB error"));

    BusinessException thrown =
        assertThrows(
            BusinessException.class,
            () -> {
              mcpServerService.softDelete("1");
            },
            "Expected BusinessException when soft deleting MCP Server fails");

    assertTrue(
        thrown.getMessage().contains("Unable to soft delete MCP Server"),
        "Exception message should contain 'Unable to soft delete MCP Server'");
    verify(mcpServerDao, never()).save(any());
  }

  @Test
  void testGetMcpServerByIds() {
    McpServer mcpServer = new McpServer();
    mcpServer.setId(1);
    mcpServer.setName("Test Server");
    mcpServer.setDescription("Test Description");
    mcpServer.setType(
        com.enttribe.promptanalyzer.model.McpServer.ServerType
            .API); // <-- Set this correctly to match expected
    mcpServer.setIcon("icon.png");
    mcpServer.setUrl("http://example.com");
    mcpServer.setIsCustomServer(true);
    mcpServer.setCommandToRun("run-command.sh");

    when(mcpServerDao.findAllById(List.of(1))).thenReturn(List.of(mcpServer));

    List<McpServerDto> result = mcpServerService.getMcpServerByIds(List.of("1"));

    assertEquals(1, result.size(), "McpServer list size should match expected");
    McpServerDto dto = result.get(0);
    assertEquals("Test Server", dto.getName(), "McpServer name should match expected");
    assertEquals(
        "Test Description", dto.getDescription(), "McpServer description should match expected");
    assertEquals(
        com.enttribe.promptanalyzer.model.McpServer.ServerType.API,
        dto.getType()); // <-- This must match
    assertEquals("icon.png", dto.getIcon(), "McpServer icon should match expected");
    assertEquals("http://example.com", dto.getUrl(), "McpServer URL should match expected");
    assertTrue(dto.getIsCustomServer(), "McpServerDto isCustomServer should be true");
    assertEquals(
        "run-command.sh", dto.getCommandToRun(), "McpServer command should match expected");
  }

  public enum ServerType {
    API,
    JSON
  }
}
