/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.AgentTestCase;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface AgentTestCaseDao extends JpaRepository<AgentTestCase, Long> {

  Optional<AgentTestCase> findByNanoId(String nanoId);

  Optional<AgentTestCase> findByName(String name);

  @Query("SELECT a FROM AgentTestCase a WHERE a.nanoId IN :nanoIds")
  List<AgentTestCase> findByNanoIdIn(List<String> nanoIds);
}
