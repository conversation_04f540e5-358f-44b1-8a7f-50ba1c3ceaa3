/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.tool.ApiToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.model.Tool;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for converting Tool entities to ToolConvertorDto objects. Provides methods to
 * convert individual entities and lists of entities.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public final class ToolConverter {

  // Private constructor to prevent instantiation
  private ToolConverter() {}

  /**
   * Converts a Tool entity to a ToolConvertorDto object.
   *
   * @param tool the Tool entity to convert
   * @return the converted ToolConvertorDto object
   */
  public static ToolConvertorDto convertToToolDto(Tool tool) {
    return ToolConvertorDto.builder()
        .id(tool.getNanoId())
        .applicationName(tool.getApplicationName())
        .category(tool.getCategory())
        .name(tool.getToolName())
        .displayName(tool.getDisplayName())
        .version(tool.getVersion())
        .toolId(tool.getToolId())
        .language(tool.getLanguage())
        .byteCodeMap(tool.getByteCodeMap())
        .agentId(tool.getAgentId())
        .type(tool.getType())
        .sourceCode(tool.getSourceCode())
        .description(tool.getDescription())
        .status(tool.getStatus())
        .toolImage(tool.getToolImage())
        .className(tool.getClassName())
        .tags(tool.getTags())
        .apiTool(tool.getApiToolInfo())
        .returnDirect(tool.getReturnDirect())
        .toolName(tool.getToolName())
        .build();
  }

  /**
   * Converts a list of Tool entities to a list of ToolConvertorDto objects.
   *
   * @param tool the list of Tool entities to convert
   * @return a list of converted ToolConvertorDto objects
   */
  public static List<ToolConvertorDto> getToolDtoList(List<Tool> tool) {
    return tool.stream().map(ToolConverter::convertToToolDto).toList();
  }

  public static List<ToolDto> mapToToolDtoList(List<Tool> tool) {
    return tool.stream().map(ToolConverter::mapToToolDto).toList();
  }

  public static ToolDto mapToToolDto(Tool tool) {
    if (tool == null) {
      return null;
    }

    return ToolDto.builder()
        .id(tool.getNanoId())
        .toolId(tool.getToolId())
        .name(tool.getToolName())
        .displayName(tool.getDisplayName())
        .applicationName(tool.getApplicationName())
        .category(tool.getCategory())
        .description(tool.getDescription())
        .language(tool.getLanguage())
        .type(tool.getType())
        .className(tool.getClassName())
        .sourceCode(tool.getSourceCode())
        .requestType(tool.getRequestType())
        .status(tool.getStatus())
        .version(tool.getVersion())
        .tags(tool.getTags())
        .httpMethod(tool.getHttpMethod())
        .url(tool.getUrl())
        .parameters(tool.getParameters())
        .toolJson(tool.getToolJson())
        .toolImage(tool.getToolImage())
        .agentId(tool.getAgentId())
        .returnDirect(tool.getReturnDirect())
        .deleted(tool.getDeleted())
        // Handle API tool info using Optional
        .apiTool(
            Optional.ofNullable(tool.getApiToolInfo())
                .map(
                    info -> {
                      try {
                        return JsonUtils.convertJsonToObject(info, ApiToolDto.class);
                      } catch (Exception e) {
                        log.error("Error converting API tool info: {}", e.getMessage());
                        return null;
                      }
                    })
                .orElse(null))
        .build();
  }
}
