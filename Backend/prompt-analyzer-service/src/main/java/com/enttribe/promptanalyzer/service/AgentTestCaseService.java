/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseResponseDto;
import java.util.List;
import java.util.Map;

/**
 * Service interface for managing Agent Test Case entities.
 *
 * <p>Defines business operations for creating, updating, retrieving, deleting, searching, and
 * counting agent test cases. Handles the relationship between agents, prompts, tools, and knowledge
 * bases for test case management.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface AgentTestCaseService {

  /**
   * Creates a new agent test case.
   *
   * <p>This method creates a new agent test case by fetching agent details from the API service,
   * retrieving associated prompts, tools, and knowledge bases, and then saving the test case.
   *
   * @param dto the agent test case request DTO containing the test case information
   * @return a map containing the result of the creation operation with status and ID
   */
  Map<String, String> create(AgentTestCaseRequestDto dto);

  /**
   * Updates an existing agent test case.
   *
   * <p>This method updates an existing agent test case by fetching the current entity, retrieving
   * updated agent details, and merging the changes with the existing data.
   *
   * @param dto the agent test case request DTO with updated information
   * @param forImport this parameter is used to determine whether to clear previous assertion
   *     mappings of the test-case
   * @return the updated agent test case as a response DTO
   */
  Map<String, String> update(AgentTestCaseRequestDto dto, boolean forImport);

  /**
   * Searches for agent test cases with optional filtering, pagination, and sorting.
   *
   * <p>This method performs a search operation using the custom filter utility to find agent test
   * cases based on the provided criteria.
   *
   * @param filter the filter string (optional) for searching specific criteria
   * @param offset the starting index for pagination (required)
   * @param size the number of results to return (required)
   * @param orderBy the field to order by (optional)
   * @param orderType the order direction (asc/desc, optional)
   * @return a list of agent test case response DTOs matching the search criteria
   */
  List<AgentTestCaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of agent test cases matching the optional filter.
   *
   * <p>This method uses the custom filter utility to count agent test cases that match the
   * specified filter criteria.
   *
   * @param filter the filter string (optional) for counting specific criteria
   * @return the count of agent test cases matching the filter
   */
  Long count(String filter);

  /**
   * Soft deletes an agent test case by its ID.
   *
   * <p>This method performs a soft delete operation by setting the deleted flag to true and
   * updating the modified time, rather than permanently removing the record.
   *
   * @param id the nanoId of the agent test case to soft delete
   * @return a map containing the result of the soft delete operation
   */
  Map<String, String> deleteById(String id);
}
