/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import com.enttribe.promptanalyzer.rest.QueryRest;
import com.enttribe.promptanalyzer.service.QueryService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing query operations. Provides endpoints for creating, searching, and
 * managing queries with support for pagination and filtering.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/query")
@RequiredArgsConstructor
public class QueryRestImpl implements QueryRest {

  private final QueryService service;

  /**
   * Searches for queries with pagination and sorting options.
   *
   * @param filter Optional filter criteria for searching queries
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching queries as QueryResponseDto objects
   */
  @Override
  public List<QueryResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching queries");
    return service.search(filter, offset, size, orderBy, orderType);
  }

  /**
   * Counts the number of queries matching the optional filter.
   *
   * @param filter Optional filter criteria
   * @return Total count of matching queries
   */
  @Override
  public Long count(String filter) {
    log.debug("Counting queries");
    return service.count(filter);
  }
}
