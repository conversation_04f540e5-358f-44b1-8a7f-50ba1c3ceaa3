/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.LlmModelService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link LlmModelService} interface. This class provides the actual business
 * logic for managing LlmModel. for a specific application. It interacts with the data access layer
 * to fetch and modify LlmModel data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class LlmModelServiceImpl implements LlmModelService {

  private final LlmModelDao llmModelDao;

  @Override
  public ProviderModelDto getModelsByProvider(String provider) {
    log.debug("Fetching models for provider: {}", provider);
    List<String> models = llmModelDao.getModelsByProvider(provider, "chat");
    log.debug("Number of models fetched: {}", models.size());
    ProviderModelDto dto = new ProviderModelDto();
    List<ProviderModelDto.ProviderModel> providerModels =
        models.stream().map(model -> new ProviderModelDto.ProviderModel(provider, model)).toList();

    dto.setProvider(provider);
    dto.setModels(providerModels);
    log.debug("Successfully get models for provider: {}", provider);
    return dto;
  }

  @Override
  public List<ProviderModelDto> getAllProvidersWithModels() {
    log.debug("Inside method getAllProvidersWithModels");
    try {
      List<Object[]> results = llmModelDao.getAllProvidersWithModels("chat");

      if (results.isEmpty()) {
        log.warn("No records found in LlmModel for the given query");
        throw new BusinessException("No records found in LlmModel for the given query");
      }
      Map<String, List<ProviderModelDto.ProviderModel>> providerModelMap = new HashMap<>();
      for (Object[] result : results) {
        String provider = (String) result[0];
        String model = (String) result[1];
        ProviderModelDto.ProviderModel providerModel =
            new ProviderModelDto.ProviderModel(provider, model);
        providerModelMap.computeIfAbsent(provider, k -> new ArrayList<>()).add(providerModel);
      }
      List<ProviderModelDto> providerModelDtos = new ArrayList<>();
      providerModelMap.forEach(
          (provider, models) -> {
            ProviderModelDto dto = new ProviderModelDto();
            dto.setProvider(provider);
            dto.setModels(models);
            providerModelDtos.add(dto);
          });
      return providerModelDtos;
    } catch (Exception e) {
      log.error("Error fetching all providers with models: {}", e.getMessage(), e);
      throw new BusinessException("Failed to fetch all providers with models");
    }
  }

  @Override
  public List<LlmModelSdkDto> getUniqueInferencesByType(String type) {
    try {
      log.debug("Fetching unique inferences for type: {}", type);
      return llmModelDao.getUniqueInferencesByType(type);
    } catch (Exception e) {
      log.warn(
          "Inside @method getUniqueInferencesByType @param : {} - Exception: {}",
          type,
          e.getMessage(),
          e);
      throw new BusinessException("Failed to fetch unique inferences by type");
    }
  }

  @Override
  public List<LlmModelSdkDto> getLlmModelsForSDK(String appName, String type) {
    try {
      log.debug("Fetching LLM models for SDK for app: {}, type: {}", appName, type);
      return llmModelDao.getLlmModelsForSDK(appName, type);
    } catch (Exception e) {
      log.warn(
          "Inside @method getLlmModelsForSDK @param : {}, {} - Exception: {}",
          appName,
          type,
          e.getMessage(),
          e);
      throw new BusinessException("Failed to fetch LLM models for SDK");
    }
  }

  @Override
  public List<LlmModelSdkDto> getLlmModelsByTypeForSDK(String type) {
    try {
      log.debug("Fetching LLM models by type for SDK: {}", type);
      return llmModelDao.getLlmModelsByTypeForSDK(type);
    } catch (Exception e) {
      log.warn(
          "Inside @method getLlmModelsByTypeForSDK @param : {} - Exception: {}",
          type,
          e.getMessage(),
          e);
      throw new BusinessException("Failed to fetch LLM models by type for SDK");
    }
  }
}
