/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.constants.FilterConstants;
import com.enttribe.promptanalyzer.model.BaseEntityGlobal;
import jakarta.persistence.EntityManager;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.Session;

@Slf4j
public class SaaSUtils {

  private SaaSUtils() {}

  public static <T> List<T> deduplicateAndGetList(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return new ArrayList<>();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (!map.containsKey(key) || tenantId != 1) {
        map.put(key, item);
      }
    }
    return new ArrayList<>(map.values());
  }

  public static <T> T deduplicateAndGetOne(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return null;
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (!map.containsKey(key) || tenantId != 1) {
        map.put(key, item);
      }
    }
    ArrayList<T> records = new ArrayList<>(map.values());
    if (records.size() > 1) {
      throw new IllegalStateException(
          "Multiple records found for key: " + keyExtractor.apply(records.getFirst()));
    }
    return records.isEmpty() ? null : records.getFirst();
  }

  public static <T> Optional<T> deduplicateAndGetOptional(
      List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return Optional.empty();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (!map.containsKey(key) || tenantId != 1) {
        map.put(key, item);
      }
    }
    ArrayList<T> records = new ArrayList<>(map.values());
    if (records.size() > 1) {
      throw new IllegalStateException(
          "Multiple records found for key: " + keyExtractor.apply(records.getFirst()));
    }
    return records.isEmpty() ? Optional.empty() : Optional.of(records.getFirst());
  }

  public static void disableFilter(EntityManager entityManager) {
    log.info("disabling filter - {}", FilterConstants._CUSTOMER_ID_FILTER);
    entityManager.unwrap(Session.class).disableFilter(FilterConstants._CUSTOMER_ID_FILTER);
  }

  public static void disableFilter(EntityManager entityManager, String filterName) {
    log.info("disabling filter : {}", filterName);
    entityManager.unwrap(Session.class).disableFilter(filterName);
  }

  public static void enableFilter(
      EntityManager entityManager, String filterName, Integer customerId) {
    log.info("enabling filter : {}", filterName);
    entityManager
        .unwrap(Session.class)
        .enableFilter(filterName)
        .setParameter("customerId", customerId);
  }

  public static <T> List<T> getGlobal(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return new ArrayList<>();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (tenantId == 1) {
        map.put(key, item);
      }
    }
    return new ArrayList<>(map.values());
  }

  public static <T> List<T> getLocal(List<T> items, Function<T, String> keyExtractor) {
    if (items == null || items.isEmpty()) {
      return new ArrayList<>();
    }
    Map<String, T> map = new LinkedHashMap<>();
    for (T item : items) {
      Integer tenantId = ((BaseEntityGlobal) item).getCustomerId();
      String key = keyExtractor.apply(item);

      if (tenantId != 1) {
        map.put(key, item);
      }
    }
    return new ArrayList<>(map.values());
  }
}
