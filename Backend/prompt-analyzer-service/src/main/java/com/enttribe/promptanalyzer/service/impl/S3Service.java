/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.io.InputStream;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
public class S3Service {

  private final AmazonS3 amazonS3;

  @Value("${s3BucketName}")
  private String bucketName;

  /**
   * Uploads a list of files to S3 with a unique filter prefix. If a JSON string of existing file
   * names is provided, it deletes those files before uploading new ones.
   *
   * @param files List of files to upload
   * @param uniqueFilter Unique prefix to add to each file name
   * @param s3FileNames JSON string containing the list of existing file names to delete before
   *     upload
   * @return List of URLs of the uploaded files
   */
  public List<String> uploadFileToS3(
      List<MultipartFile> files, String uniqueFilter, String s3FileNames) {
    log.debug(
        "Going to upload {} file to S3 bucket '{}', with fileName: {}",
        files.size(),
        bucketName,
        s3FileNames);

    // Execute case of update Delete existing files if file Path Json is provided
    if (StringUtils.hasText(s3FileNames)) {
      log.info("Deleting existing files from S3 before upload");
      try {
        deleteExistingFilesFromS3(s3FileNames);
      } catch (Exception e) {
        log.error("Failed to delete existing files from S3: {}", e.getMessage(), e);
        throw new BusinessException("Failed to delete existing files from S3: ");
      }
    }

    List<String> uploadedFileUrls = new ArrayList<>();
    for (MultipartFile file : files) {
      String newFileName = uniqueFilter + "_" + file.getOriginalFilename();
      log.debug("Processing file upload: {}", newFileName);
      try (InputStream inputStream = file.getInputStream()) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.getSize());
        metadata.setContentType(file.getContentType());

        PutObjectRequest request =
            new PutObjectRequest(bucketName, newFileName, inputStream, metadata);
        amazonS3.putObject(request);
        log.info("Successfully uploaded file to S3 bucket: {}", newFileName);
        uploadedFileUrls.add(newFileName);
      } catch (Exception e) {
        log.error(
            "Failed to upload file {} to S3 bucket {}: {}",
            newFileName,
            bucketName,
            e.getMessage(),
            e);
        throw new BusinessException(
            "S3 upload failed for bucket and connection of seaweedfs: "
                + bucketName
                + " for file: "
                + newFileName,
            e);
      }
    }
    log.info("Completed file upload to S3. Total files uploaded: {}", uploadedFileUrls.size());
    return uploadedFileUrls;
  }

  /**
   * Deletes existing files from S3 based on the provided JSON string of file names.
   *
   * @param s3FileNames JSON string containing the list of file names to delete
   */
  private void deleteExistingFilesFromS3(String s3FileNames) {
    log.debug("Starting deletion of existing files from S3");
    List<String> fileNames = extractFileName(s3FileNames);
    log.debug("Found {} files to delete from S3", fileNames.size());

    for (String fileName : fileNames) {
      try {
        amazonS3.deleteObject(bucketName, fileName);
        log.info("Successfully deleted file from S3 bucket: {}", fileName);
      } catch (Exception e) {
        log.error(
            "Failed to delete file {} from S3 bucket {}: {}",
            fileName,
            bucketName,
            e.getMessage(),
            e);
        throw new BusinessException("Failed to delete file from S3: " + fileName, e);
      }
    }
    log.info("Completed deletion of {} files from S3", fileNames.size());
  }

  /**
   * Extracts file names from a JSON string.
   *
   * @param s3FileNames JSON string containing the list of file names
   * @return List of file names
   */
  private List<String> extractFileName(String s3FileNames) {
    log.debug("Extracting file names from JSON string");
    try {
      List<String> fileNames = JsonUtils.convertJsonToList(s3FileNames, String.class);
      log.debug("Successfully extracted {} file names from JSON", fileNames.size());
      return fileNames;
    } catch (JsonProcessingException e) {
      log.error("Failed to parse file names JSON: {}", e.getMessage(), e);
      throw new BusinessException("Failed to extract file names from JSON: " + e.getMessage(), e);
    }
  }

  /**
   * Downloads a file from S3 by its file name.
   *
   * @param fileName Name of the file to download
   * @return Byte array of the downloaded file
   */
  public byte[] downloadFileFromS3(String fileName) {
    log.info("Going downloading file from S3Bucket: {} and fileName {}", bucketName, fileName);
    try {
      S3Object s3Object = amazonS3.getObject(bucketName, fileName);
      try (InputStream inputStream = s3Object.getObjectContent()) {
        byte[] fileBytes = inputStream.readAllBytes();
        log.info("Successfully downloaded file from S3: {}", fileName);
        return fileBytes;
      }
    } catch (Exception e) {
      log.error(
          "Failed to download file {} from S3 bucket {}: {}",
          fileName,
          bucketName,
          e.getMessage(),
          e);
      throw new BusinessException(e + e.getMessage());
    }
  }
}
