/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.constants;

/**
 * This class contains constant values used throughout the application for handling prompt-related
 * operations and responses. Author: VisionWaves Version: 1.0
 */
public class PromptConstants {

  public static final String RESULT = "result";
  public static final String SUCCESS = "success";
  public static final String FAILED = "failed";
  public static final String AUTHORIZATION = "Authorization";
  public static final String VERSION = "version";
  public static final String CONDITION = "condition";
  public static final String PACKAGE = "package";
  public static final String REVISION = "revision";
  public static final String COMPONENT = "component";
  public static final String DISCONNECTED_NODE_ACKNOWLEDGED = "&disconnectedNodeAcknowledged=false";
  public static final String CLIENT_ID = "clientId";
  public static final String APPLICATION_JSON = "application/json";
  public static final String COLLECTION_NAME = "collectionName";
  public static final String VECTOR_DATABASE = "vectorDatabase";
  public static final String MILVUS = "milvus";
  public static final String REDIS = "redis";
  public static final String INDEX_NAME = "indexName";
  public static final String PREFIX = "prefix";
  public static final String DATABASE_NAME = "databaseName";
  public static final String EMBEDDING_MODEL = "nomic-embed-text-v1_5-preview1";
  public static final String EMBEDDING_MODEL_KEY = "embeddingModel";
  public static final String CHAT_MODEL_KEY = "chatModel";
  public static final String CHAT_MODEL_VALUE = "llama-3.3-70b-versatile";
  public static final String PROVIDER = "provider";
  public static final String PARTIAL_SUCCESS = "partial-success";
  public static final String AGENT_ID = "Agent ID";
  public static final String PROMPT_ID = "Prompt ID";
  public static final String SCHEMA = "schema";
  public static final String DESCRIPTION = "description";
  public static final String CONNECTOR = "connector";
  public static final String RETURN_DIRECT = "Return Direct";
  public static final String COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL =
      "com.enttribe.promptanalyzer.function.ApiTool";
  public static final String TEMPLATE_NOT_VALID = "The template string is not valid.";
  public static final String CONTENT_TYPE = "Content-Type";
  public static final String COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL =
      "com.enttribe.promptanalyzer.function.ConnectorTool";
  public static final String FAILED_TO_READ_RESOURCE = "Failed to read resource";
  public static final String ENDING = "\";\n";
  public static final String SEMICOLON_NEWLINES = "\";\n\n";
  public static final String STATIC_METHOD_DECLARATION = "private static final String METHOD = \"";
  public static final String STATIC_HOSTNAME_DECLARATION =
      "private static final String HOSTNAME = \"";
  public static final String AUTH_TYPE_DECLARATION =
      "private static final String AUTHORIZATION_TYPE = \"";
  public static final String AUTH_VALUE_DECLARATION =
      "private static final String AUTHORIZATION_VALUE = \"";
  public static final String JANUARY = "january";
  public static final String FEBRUARY = "february";
  public static final String MARCH = "march";
  public static final String APRIL = "april";
  public static final String MAY = "may";
  public static final String JUNE = "june";
  public static final String JULY = "july";
  public static final String AUGUST = "august";
  public static final String SEPTEMBER = "september";
  public static final String OCTOBER = "october";
  public static final String NOVEMBER = "november";
  public static final String DECEMBER = "december";
  public static final String USER_QUERY = "userQuery";
  public static final String TIME_ZONE = "timeZone";
  public static final String LOCALE = "locale";
  public static final String NO_TIME_INFORMATION_FOUND = "No time information found";
  public static final String SECOND = "second";
  public static final String MONTH = "month";
  public static final String GRAIN = "grain";
  public static final String PROCESSING_QUERY_LOG = "Processing query: {}";

  // Vector Store Deletion Batch Sizes
  public static final int VECTOR_STORE_DELETION_BATCH_SIZE = 100;
  public static final int CUSTOM_AGENT_DELETION_BATCH_SIZE = 11;

  // RestTemplate Connection Settings
  public static final int MAX_TOTAL_CONNECTIONS = 100;
  public static final int MAX_CONNECTIONS_PER_ROUTE = 20;
  public static final int IDLE_CONNECTION_EVICTION_TIME_SECONDS = 30;

  // CSV Column Indices
  public static final int EXPECTED_TO_COLUMN_INDEX = 2;

  public static final String REQUEST_BODY_CANNOT_BE_NULL = "The request body can not be null.";
  public static final String STATUS = "status";
  public static final String TESTSUITE_NOT_FOUND = "TestSuite not found";
  public static final String TOTAL_RECORDS_UPDATED_FOR_PROMPT =
      "Total records updated for prompt : {}";
  public static final String PROCESSED_PROMPT_BATCH_OF = "Processed prompt batch of: {}";
  public static final String AGENTTESTCASE_NOT_FOUND = "AgentTestCase not found";
  public static final String ASSERTION_NOT_FOUND = "Assertion not found";
  public static final String PROMPT_NOT_FOUND = "Prompt not found";
  public static final String UNABLE_TO_GET_PROMPT = "Unable to get prompt";
  public static final String TOOL_NOT_FOUND = "Tool not found";
  public static final String TESTEXECUTION_NOT_FOUND = "TestExecution not found";
  public static final String FAILED_TO_GET_PROMPT = "Failed to get prompt";
  public static final String ERROR = "error";

  private PromptConstants() {
    // public constructor to prevent instantiation
  }
}
