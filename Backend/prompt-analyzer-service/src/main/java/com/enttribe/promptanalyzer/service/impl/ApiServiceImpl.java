/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.config.RestTemplateSingleton;
import com.enttribe.promptanalyzer.dto.agent.AgentApiResponse;
import com.enttribe.promptanalyzer.dto.crawl.CrawlResponse;
import com.enttribe.promptanalyzer.dto.microintent.MicroIntentResponseDto;
import com.enttribe.promptanalyzer.exception.ApiException;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class ApiServiceImpl implements ApiService {

  @Value("${crawl.url}")
  private String crawlUrl;

  @Value("${crawl.token}")
  private String crawlToken;

  @Value("${execution.api.url}")
  private String executionApiUrl;

  @Value("${processorList.api.url}")
  private String processorsListApiUrl;

  @Value("${agent.details.url}")
  private String agentDetailsUrl;

  @Value("${micro.intent.api.url}")
  private String microIntentApiUrl;

  private final RestTemplate restTemplate;
  private final HttpServletRequest httpServletRequest;

  public ApiServiceImpl(HttpServletRequest httpServletRequest) {
    this.httpServletRequest = httpServletRequest;
    this.restTemplate = RestTemplateSingleton.getRestTemplate();
  }

  private static final int DEFAULT_PRIORITY = 10;

  @Override
  public String triggerCrawl(String websiteUrl) {
    log.debug("inside @method triggerCrawl. @param websiteUrl : {}", websiteUrl);
    try {
      String url = String.format("%s/crawl", crawlUrl);
      log.debug("Constructed URL for crawl: {}", url);

      // Create headers
      HttpHeaders headers = new HttpHeaders();
      headers.set("Authorization", String.format("Bearer %s", crawlToken));
      headers.setContentType(MediaType.APPLICATION_JSON);
      log.debug("Headers set for request: {}", headers);

      // Create request body
      Map<String, Object> requestBody = new HashMap<>();
      requestBody.put("urls", websiteUrl);
      requestBody.put("priority", DEFAULT_PRIORITY);
      log.debug("Request body created: {}", requestBody);

      // Create HTTP entity with headers and body
      HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
      // Make the request
      ResponseEntity<CrawlResponse> response =
          restTemplate.exchange(url, HttpMethod.POST, requestEntity, CrawlResponse.class);
      log.debug("Received response of Crawl: {}", response);

      // Return response body
      var body = response.getBody();
      if (body != null) {
        log.info("Crawl task created successfully with task ID: {}", body.getTaskId());
      } else {
        log.warn("Received null response body for website URL: {}", websiteUrl);
      }
      return body != null ? body.getTaskId() : null;
    } catch (Exception e) {
      log.error("Error triggering crawl for website URL {}: {}", websiteUrl, e.getMessage(), e);
      throw new ApiException("Failed triggering crawl");
    }
  }

  @Override
  public CrawlResponse getTaskStatus(String taskId) {
    log.debug("inside @method getTaskStatus. @param : taskId -> {}", taskId);
    try {
      // Construct the request URL
      String url = String.format("%s/task/%s", crawlUrl, taskId);
      log.debug("Constructed URL for task status: {}", url);

      // Create headers
      HttpHeaders headers = new HttpHeaders();
      headers.set("Authorization", String.format("Bearer %s", crawlToken));
      headers.setContentType(MediaType.APPLICATION_JSON);
      log.debug("Headers set for request: {}", headers);

      // Create HTTP entity with headers
      HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

      // Make the GET request
      ResponseEntity<CrawlResponse> response =
          restTemplate.exchange(url, HttpMethod.GET, requestEntity, CrawlResponse.class);
      log.debug("Received response of task status: {}", response);

      // Return response body
      CrawlResponse responseBody = response.getBody();
      if (responseBody != null) {
        log.info("Successfully retrieved status for task ID: {}", taskId);
      } else {
        log.warn("Received null response body for task ID: {}", taskId);
      }
      return responseBody;
    } catch (Exception e) {
      log.error("Error retrieving status for task ID {}: {}", taskId, e.getMessage(), e);
      return new CrawlResponse(e.getMessage());
    }
  }

  @Override
  public String getTableSchema(String executionName, String tableName) {
    log.debug(
        "inside @method getTableSchema. @param : executionName -> {}, tableName -> {}",
        executionName,
        tableName);
    try {
      String requestBody =
          String.format(
              "{\"name\":\"%s\", \"operationSpecification\": \"{\\\"query\\\":\\\"SHOW CREATE TABLE %s\\\"}\"}",
              executionName, tableName);
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
      HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
      Map<String, Object> responseMap =
          restTemplate.postForObject(executionApiUrl, request, Map.class);
      log.debug("Received response of table schema: {}", responseMap);
      if (responseMap != null && "SUCCESS".equals(responseMap.get("response"))) {
        String schema = responseMap.get("sqlResponse").toString();
        log.info("Successfully retrieved schema for table: {}", tableName);
        return schema;
      } else {
        log.warn("Failed to retrieve schema for table: {}. Response: {}", tableName, responseMap);
        return null;
      }
    } catch (Exception e) {
      log.error(
          "Error retrieving schema for table {} in execution {}: {}",
          tableName,
          executionName,
          e.getMessage(),
          e);
      return null;
    }
  }

  @Override
  public List<String> getTablesName(String name) {
    log.debug("Inside @method getTablesName @param : {}", name);
    List<String> tableNames = new ArrayList<>();
    try {
      log.debug("RestTemplate initialized successfully");
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.setAccept(List.of(MediaType.APPLICATION_JSON));
      log.debug("HTTP headers set successfully");
      Map<String, Object> requestBody = new HashMap<>();
      requestBody.put("name", name);
      requestBody.put("operationSpecification", "{\"query\":\"Show tables;\"}");
      ObjectMapper objectMapper = new ObjectMapper();
      String jsonString = objectMapper.writeValueAsString(requestBody);
      log.debug("Request body sqlConnector: {}", name);
      HttpEntity<String> entity = new HttpEntity<>(jsonString, headers);
      log.info("Sending API request to: {}", executionApiUrl);
      ResponseEntity<Map<String, Object>> responseEntity =
          restTemplate.exchange(
              executionApiUrl,
              HttpMethod.POST,
              entity,
              new ParameterizedTypeReference<Map<String, Object>>() {});
      Map<String, Object> response = responseEntity.getBody();
      log.info("Received API response: {}", response);
      if (response != null && "SUCCESS".equals(response.get("response"))) {
        Object sqlResponseObj = response.get("sqlResponse");

        if (sqlResponseObj instanceof List<?> sqlResponse) {
          tableNames =
              sqlResponse.stream()
                  .filter(Map.class::isInstance)
                  .map(map -> (Map<?, ?>) map)
                  .map(
                      entry -> {
                        Iterator<?> it = entry.values().iterator();
                        if (it.hasNext()) {
                          Object val = it.next();
                          return val != null ? val.toString() : null;
                        }
                        return null;
                      })
                  .filter(Objects::nonNull)
                  .toList();

          log.debug("Extracted table names: {}", tableNames);
          log.info("Successfully retrieved {} table names", tableNames.size());
        }
      } else {
        log.warn("API response status is not SUCCESS or response is : {}", response);
      }
    } catch (Exception e) {
      log.error("Error retrieving table names for execution", e);
      throw new ApiException("Failed to retrieve table names for execution: " + name, e);
    }
    return tableNames;
  }

  @Override
  public Map<String, String> getProcessors() {
    List<Map<String, Object>> processorApiResponse = getProcessorApiResponse();
    log.debug("size of processors list: {}", processorApiResponse.size());
    Map<String, String> result = processProcessorList(processorApiResponse);
    log.info("Successfully processed {} processors", result.size());
    return result;
  }

  @Override
  public AgentApiResponse getAgentDetails(String agentId, String agentType) {
    log.debug(
        "Inside @method getAgentDetails. @param agentId: {}, agentType: {}", agentId, agentType);
    try {
      String url =
          String.format(
              "%s/voiceAgent/getAgentDetailBySystemCodeAndType?systemCode=%s&type=%s",
              agentDetailsUrl, agentId, agentType);
      log.debug("URL to get agent details : {}", url);
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
      ResponseEntity<AgentApiResponse> response =
          restTemplate.exchange(url, HttpMethod.GET, requestEntity, AgentApiResponse.class);
      AgentApiResponse body = response.getBody();
      if (body != null) {
        log.info(
            "Successfully retrieved agent details for agentId: {} response: {}", agentId, body);
        return body;
      } else {
        log.warn("Received null response body for agentId: {}", agentId);
        throw new ApiException("failed to get agent details");
      }
    } catch (Exception e) {
      log.error("Error retrieving agent details for agentId={}", agentId, e);
      throw new ApiException("Failed to retrieve agent details for agentId=" + agentId, e);
    }
  }

  @Override
  public List<MicroIntentResponseDto> getMicroIntentDetails() {
    log.debug("Inside @method getMicroIntentDetails with microIntentApiUrl: {}", microIntentApiUrl);

    try {
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      String authorizationToken = httpServletRequest.getHeader("access-token");
      headers.set("access-token", authorizationToken);
      HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

      ResponseEntity<List<Map<String, Object>>> response =
          restTemplate.exchange(
              microIntentApiUrl,
              HttpMethod.GET,
              requestEntity,
              new ParameterizedTypeReference<List<Map<String, Object>>>() {});

      List<Map<String, Object>> responseBody = response.getBody();
      if (responseBody == null || responseBody.isEmpty()) {
        log.info("No micro-intent details found.");
        return Collections.emptyList();
      }
      return convertToMicroIntentDtoList(responseBody);
    } catch (Exception e) {
      log.error("Error retrieving micro-intent details", e);
      throw new ApiException("Failed to retrieve micro-intent details", e);
    }
  }

  private List<MicroIntentResponseDto> convertToMicroIntentDtoList(
      List<Map<String, Object>> responseBody) {
    List<MicroIntentResponseDto> result = new ArrayList<>();

    for (Map<String, Object> item : responseBody) {
      String entityName = null;
      String intentName = null;
      String sequenceChain = null;

      if (item.containsKey("entityInfo")) {
        Map<String, Object> entityInfo = (Map<String, Object>) item.get("entityInfo");
        if (entityInfo != null) {
          entityName = (String) entityInfo.get("name");
        }
      }

      if (item.containsKey("intentType")) {
        Map<String, Object> intentType = (Map<String, Object>) item.get("intentType");
        if (intentType != null) {
          intentName = (String) intentType.get("name");
        }
      }
      if (item.containsKey("sequenceChain")) {
        sequenceChain = (String) item.get("sequenceChain");
      }
      MicroIntentResponseDto dto =
          MicroIntentResponseDto.builder()
              .entityName(entityName)
              .intentName(intentName)
              .sequenceChain(sequenceChain)
              .build();

      result.add(dto);
    }

    log.info("Mapped {} micro-intent response(s).", result.size());
    return result;
  }

  private List<Map<String, Object>> getProcessorApiResponse() {
    log.debug("Inside @method getProcessorsListFromApi");

    try {
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);

      HttpEntity<String> entity = new HttpEntity<>(headers);
      ResponseEntity<List<Map<String, Object>>> response =
          restTemplate.exchange(
              processorsListApiUrl,
              HttpMethod.GET,
              entity,
              new ParameterizedTypeReference<List<Map<String, Object>>>() {});

      List<Map<String, Object>> resultMap = response.getBody();
      if (resultMap != null) {
        log.info("Successfully retrieved {} processors", resultMap.size());
      } else {
        log.warn("Received null response for processor list");
      }
      return resultMap != null ? resultMap : Collections.emptyList();
    } catch (Exception e) {
      log.error("Error while fetching processors list: {}", e.getMessage());
      return Collections.emptyList();
    }
  }

  private static Map<String, String> processProcessorList(
      List<Map<String, Object>> processorsList) {
    Map<String, String> result = new HashMap<>();

    for (Map<String, Object> processor : processorsList) {
      String id = String.valueOf(processor.get("id"));
      String name = (String) processor.get("displayName");
      String description = (String) processor.get("description");
      String packageName = (String) processor.get("key");
      String propertiesJson = (String) processor.get("properties");

      Map<String, Object> outputMap = new LinkedHashMap<>();
      outputMap.put("name", name);
      outputMap.put("description", description);
      outputMap.put("package", packageName);
      outputMap.put("properties", propertiesJson);

      String processorJson = JsonUtils.convertToJSON(outputMap);
      result.put(id, processorJson);
      log.debug("Processed processor with ID: {}", id);
    }

    return result;
  }
}
