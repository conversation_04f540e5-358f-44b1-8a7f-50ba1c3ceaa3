/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.rest.LlmApiRest;
import com.enttribe.promptanalyzer.service.LlmApiService;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing LLM (Language Learning Model) API operations. Provides endpoints for
 * chat completions and prompt executions. All endpoints require appropriate security roles for
 * access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/chat")
@RequiredArgsConstructor
@Slf4j
public class LlmApiRestImpl implements LlmApiRest {

  private final LlmApiService llmApiService;

  /**
   * Handles chat completion requests.
   *
   * @param requestDto The request data transfer object containing chat parameters
   * @return The response from the LLM API service
   */
  @Override
  public Object chatCompletion(ChatCompletionRequestDto requestDto) {
    log.info("Inside method Chat completion");
    return llmApiService.chatCompletion(requestDto);
  }

  /**
   * Executes a prompt based on the provided request data.
   *
   * @param requestDto The request data containing prompt ID and variable map
   * @return The result of the prompt execution
   */
  @Override
  public Object executePrompt(Map<String, Object> requestDto) {
    log.debug("Inside @method executePrompt");
    String promptId = (String) requestDto.get("promptId");
    Map<String, Object> variableMap = (Map<String, Object>) requestDto.get("variableMap");
    Boolean format = (Boolean) requestDto.get("format");
    if (format == null) {
      format = false;
    }

    log.info("Executing prompt with ID: {}, format: {}", promptId, format);
    return llmApiService.executePrompt(promptId, variableMap, format);
  }

  /**
   * Executes a version 1 prompt based on the provided request data.
   *
   * @param requestDto The request data containing prompt ID and variable map
   * @return A map containing the result of the prompt execution
   */
  @Override
  public Object executePromptV1(Map<String, Object> requestDto) {
    log.debug("Inside @method executePromptV1");
    String promptId = (String) requestDto.get("promptId");
    Map<String, Object> variableMap = (Map<String, Object>) requestDto.get("variableMap");

    log.info("Executing prompt V1 with ID: {}", promptId);
    String result = llmApiService.executePromptV1(promptId, variableMap);
    return Map.of(PromptConstants.RESULT, result);
  }

  /**
   * Generates a system prompt based on user input and an optional old prompt.
   *
   * @param request The request data containing user input, old prompt, and type
   * @return A map containing the generated system prompt
   */
  @Override
  public Map<String, String> generateSystemPrompt(Map<String, String> request) {
    String userInput = request.get("userInput");
    String oldPrompt = request.get("oldPrompt");
    String type = request.get("type");
    String response;

    log.info("Generating system prompt with type: {}", type);
    if (oldPrompt == null) {
      response = llmApiService.generateFreshSystemPrompt(userInput, type);
    } else {
      response = llmApiService.improveSystemPrompt(userInput, oldPrompt, type);
    }
    return Map.of(PromptConstants.RESULT, response);
  }

  @Override
  public Map<String, Object> generateTestCase(
      String promptId, @RequestBody Map<String, String> request) {
    return llmApiService.generateTestCase(promptId, request);
  }
}
