/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dao.ExceptionAuditDao;
import com.enttribe.promptanalyzer.dao.LlmModelDao;
import com.enttribe.promptanalyzer.dao.PromptAuditDao;
import com.enttribe.promptanalyzer.dao.ToolAuditDao;
import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.ExceptionAudit;
import com.enttribe.promptanalyzer.model.LlmModel;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.ToolAudit;
import com.enttribe.promptanalyzer.service.AuditService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.AuditConvertor;
import com.enttribe.promptanalyzer.util.JsonUtils;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link AuditService} interface. This class provides the business Audit logs
 * of prompt and exception handling. It manages the persistence of both exception and prompt audit
 * records, offering functionality to track system behavior, errors, and prompt interactions.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AuditServiceImpl implements AuditService {

  private final ExceptionAuditDao exceptionDao;
  private final PromptAuditDao promptAuditDao;
  private final LlmModelDao llmModelDao;
  private final CustomFilter customFilter;
  private final ToolAuditDao toolAuditDao;

  @Override
  public String saveExceptionAudit(ExceptionAuditDto exceptionAuditDto) {
    log.debug("Starting to save exception audit for audit ID: {}", exceptionAuditDto.getAuditId());
    try {
      ExceptionAudit exceptionAudit = new ExceptionAudit();
      exceptionAudit.setTimestamp(new Date());
      exceptionAudit.setExceptionMessage(exceptionAuditDto.getExceptionMessage());
      exceptionAudit.setExceptionTrace(exceptionAuditDto.getExceptionTrace());
      exceptionAudit.setMethodName(exceptionAuditDto.getMethodName());
      exceptionAudit.setApplicationName(exceptionAuditDto.getApplicationName());
      exceptionAudit.setPromptId(exceptionAuditDto.getPromptId());
      exceptionAudit.setCategory(exceptionAuditDto.getCategory());
      exceptionAudit.setAuditId(exceptionAuditDto.getAuditId());

      if (exceptionAuditDto.getMethodParameters() != null) {
        exceptionAudit.setMethodParameters(
            JsonUtils.convertToJSON(exceptionAuditDto.getMethodParameters()));
        log.debug(
            "Converted method parameters to JSON for audit ID: {}", exceptionAudit.getAuditId());
      }
      if (exceptionAuditDto.getIdentifier() != null) {
        exceptionAudit.setIdentifier(JsonUtils.convertToJSON(exceptionAuditDto.getIdentifier()));
        log.debug("Converted identifier to JSON for audit ID: {}", exceptionAudit.getAuditId());
      }

      exceptionDao.save(exceptionAudit);
      log.info("Exception audit saved successfully for audit ID: {}", exceptionAudit.getAuditId());
      return "success";
    } catch (Exception e) {
      log.error("Error in @method saveExceptionAudit : {}", e.getMessage(), e);
      return "failed";
    }
  }

  @Override
  public String savePromptAudit(PromptAuditDto promptAuditDto) {
    log.info(
        "prompt audit id : {} startTime : {} endTime : {}",
        promptAuditDto.getAuditId(),
        promptAuditDto.getStartTime(),
        promptAuditDto.getEndTime());
    log.debug("Starting to save prompt audit for audit ID: {}", promptAuditDto.getAuditId());
    try {
      PromptAudit promptAudit = new PromptAudit();
      promptAudit.setModel(promptAuditDto.getModel());
      promptAudit.setResponseText(promptAuditDto.getResponseText());
      promptAudit.setTotalToken(promptAuditDto.getTotalToken());
      promptAudit.setPromptToken(promptAuditDto.getPromptToken());
      promptAudit.setResponseTime(promptAuditDto.getResponseTime());
      promptAudit.setApplicationName(promptAuditDto.getApplicationName());
      promptAudit.setGenerationTokens(promptAuditDto.getGenerationTokens());
      promptAudit.setCategory(promptAuditDto.getCategory());
      promptAudit.setPromptId(promptAuditDto.getPromptId());
      promptAudit.setAuditId(promptAuditDto.getAuditId());
      promptAudit.setMetaTags(JsonUtils.convertToJSON(promptAuditDto.getMetaTags()));
      promptAudit.setChatOptions(JsonUtils.convertToJSON(promptAuditDto.getChatOptions()));
      promptAudit.setFunctionArgs(promptAuditDto.getFunctionArgs());
      promptAudit.setPromptName(promptAuditDto.getPromptName());
      promptAudit.setHttpStatus(promptAuditDto.getHttpStatus());
      promptAudit.setRequestText(promptAuditDto.getRequestText());
      promptAudit.setStatus(promptAuditDto.getStatus());
      promptAudit.setErrorMessage(promptAuditDto.getErrorMessage());
      promptAudit.setStartTime(promptAuditDto.getStartTime());
      promptAudit.setEndTime(promptAuditDto.getEndTime());

      double totalCost = getTotalCost(promptAuditDto);
      promptAudit.setTotalCost(totalCost);
      promptAudit.setProvider(promptAuditDto.getProvider());
      promptAudit.setCreationTime(promptAuditDto.getCreationTime());
      promptAudit.setAgentName(promptAuditDto.getAgentName());
      promptAudit.setToolCallDefinitions(promptAuditDto.getToolCallDefinitions());

      promptAuditDao.save(promptAudit);
      log.info("Prompt audit saved successfully for audit ID: {}", promptAudit.getAuditId());
      return "success";
    } catch (Exception e) {
      log.error("Error in @method savePromptAudit : {}", e.getMessage(), e);
      return "failed";
    }
  }

  private double getTotalCost(PromptAuditDto promptAuditDto) {
    double totalCost;
    try {
      totalCost =
          calculateTotalCost(
              promptAuditDto.getModel(),
              promptAuditDto.getProvider(),
              promptAuditDto.getPromptToken(),
              promptAuditDto.getGenerationTokens());
    } catch (Exception e) {
      log.error(
          "Error calculating cost for model {} and provider {}: {}",
          promptAuditDto.getModel(),
          promptAuditDto.getProvider(),
          e.getMessage());
      totalCost = 0;
    }
    return totalCost;
  }

  private double calculateTotalCost(
      String model, String provider, long promptToken, long generationTokens) {
    log.debug(
        "Calculating total cost for model: {}, provider: {}, promptToken: {}, generationTokens: {}",
        model,
        provider,
        promptToken,
        generationTokens);
    try {
      LlmModel llmModel = llmModelDao.findModelAndProvider(model, provider);
      if (llmModel == null) {
        log.error("Error in @method calculateTotalCost : LlM Model not found");
        throw new ResourceNotFoundException("LlM Model not found.");
      }
      double cost =
          (promptToken * (llmModel.getInputCost() / 1_000_000))
              + (generationTokens * (llmModel.getOutputCost() / 1_000_000));
      log.debug("Calculated total cost: {} for model: {}, provider: {}", cost, model, provider);
      return cost;
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method calculateTotalCost Exception : {}", e.getMessage());
      throw new ResourceNotFoundException(
          "Unable to calculate total cost due to missing model information.", e);
    } catch (Exception e) {
      log.error("Error in @method calculateTotalCost : {}", e.getMessage());
      throw new BusinessException("Unable to calculate total cost due to an internal error.", e);
    }
  }

  @Override
  public List<PromptAudit> getPromptAuditListByAuditId(String auditId) {
    log.debug("Retrieving prompt audit list for audit ID: {}", auditId);
    try {
      List<PromptAudit> audits = promptAuditDao.getPromptAuditListByAuditId(auditId);
      log.debug("Found {} prompt audit records for audit ID: {}", audits.size(), auditId);
      return audits;
    } catch (Exception e) {
      log.error("Error in @method getPromptAuditListByAuditId : {}", e.getMessage(), e);
      throw new BusinessException("Failed to retrieve prompt audit list.", e);
    }
  }

  @Override
  public List<PromptAudit> getPromptAuditListByPromptId(String promptId) {
    log.debug("Retrieving prompt audit list for prompt ID: {}", promptId);
    try {
      List<PromptAudit> audits = promptAuditDao.getPromptAuditListByPromptId(promptId);
      log.debug("Found {} prompt audit records for prompt ID: {}", audits.size(), promptId);
      return audits;
    } catch (Exception e) {
      log.error("Error in @method getPromptAuditListByPromptId : {}", e.getMessage(), e);
      throw new BusinessException("Failed to retrieve prompt audit list.", e);
    }
  }

  @Override
  public Map<String, String> saveToolAudit(ToolAuditDto toolAuditDto) {
    log.debug(
        "Starting to save tool audit for audit ID: {}, startTime: {}, endTime: {}",
        toolAuditDto.getAuditId(),
        toolAuditDto.getStartTime(),
        toolAuditDto.getEndTime());
    try {
      ToolAudit toolAudit = mapToToolAudit(toolAuditDto);
      toolAuditDao.save(toolAudit);
      log.info("Tool audit saved successfully for audit ID: {}", toolAudit.getAuditId());
      return Map.of(APIConstants.RESULT, APIConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error in @method saveToolAudit : {}", e.getMessage(), e);
      return Map.of(APIConstants.RESULT, "failed to save tool Audit");
    }
  }

  @Override
  public List<ToolAudit> getToolAuditListByAuditId(String auditId) {
    log.debug("Retrieving tool audit list for audit ID: {}", auditId);
    try {
      List<ToolAudit> audits = toolAuditDao.getToolAuditListByAuditId(auditId);
      log.debug("Found {} tool audit records for audit ID: {}", audits.size(), auditId);
      return audits;
    } catch (Exception e) {
      log.error("Error in @method getToolAuditListByAuditId : {}", e.getMessage(), e);
      throw new BusinessException("Failed to retrieve tool audit list.", e);
    }
  }

  private ToolAudit mapToToolAudit(ToolAuditDto dto) {
    log.debug("Mapping tool audit DTO to entity for audit ID: {}", dto.getAuditId());
    try {
      ToolAudit toolAudit = new ToolAudit();
      toolAudit.setId(dto.getId());
      toolAudit.setRequestText(dto.getRequestText());
      toolAudit.setChatOptions(dto.getChatOptions());
      toolAudit.setAuditId(dto.getAuditId());
      toolAudit.setToolCallRequest(dto.getToolCallRequest());
      toolAudit.setToolDefinitions(dto.getToolDefinitions());
      toolAudit.setToolResponse(dto.getToolResponse());
      toolAudit.setAgentName(dto.getAgentName());
      toolAudit.setPromptId(dto.getPromptId());
      toolAudit.setPromptName(dto.getPromptName());
      toolAudit.setTotalToken(dto.getTotalToken());
      toolAudit.setPromptToken(dto.getPromptToken());
      toolAudit.setGenerationTokens(dto.getGenerationTokens());
      toolAudit.setResponseTime(dto.getResponseTime());
      toolAudit.setCreationTime(dto.getCreationTime());
      toolAudit.setStartTime(dto.getStartTime());
      toolAudit.setEndTime(dto.getEndTime());
      toolAudit.setModel(dto.getModel());
      toolAudit.setProvider(dto.getProvider());
      double totalCost = getTotalCost(dto);
      toolAudit.setTotalCost(totalCost);
      toolAudit.setApplicationName(dto.getApplicationName());
      toolAudit.setStatus(dto.getStatus());
      log.debug("Successfully mapped tool audit DTO to entity for audit ID: {}", dto.getAuditId());
      return toolAudit;
    } catch (Exception e) {
      log.error("Error in @method mapToToolAudit : {}", e.getMessage(), e);
      throw new BusinessException("Failed to map tool audit data.", e);
    }
  }

  private double getTotalCost(ToolAuditDto dto) {
    double totalCost;
    try {
      totalCost =
          calculateTotalCost(
              dto.getModel(), dto.getProvider(), dto.getPromptToken(), dto.getTotalToken());
    } catch (Exception e) {
      log.error(
          "Error calculating cost for model {} and provider {}: {}",
          dto.getModel(),
          dto.getProvider(),
          e.getMessage(),
          e);
      totalCost = 0;
    }
    return totalCost;
  }

  @Override
  public List<PromptAuditDto> searchPromptAudit(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug(
        "Searching prompt audit records with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
        filter,
        offset,
        size,
        orderBy,
        orderType);
    try {
      List<PromptAudit> promptAuditList =
          customFilter.searchByFilter(PromptAudit.class, filter, orderBy, orderType, offset, size);
      log.debug("Found {} prompt audit records matching search criteria", promptAuditList.size());
      return AuditConvertor.getPromptAuditDtoList(promptAuditList);
    } catch (Exception e) {
      log.error("Error in @method searchPromptAudit : {}", e.getMessage(), e);
      throw new BusinessException("Failed to search prompt audit records.", e);
    }
  }

  @Override
  public Long countPromtAudit(String filter) {
    log.debug("Counting prompt audit records with filter: {}", filter);
    try {
      Long count = customFilter.countByFilter(PromptAudit.class, filter);
      log.debug("Found {} prompt audit records matching count criteria", count);
      return count;
    } catch (Exception e) {
      log.error("Error in @method countPromtAudit : {}", e.getMessage(), e);
      throw new BusinessException("Failed to count prompt audit records.", e);
    }
  }
}
