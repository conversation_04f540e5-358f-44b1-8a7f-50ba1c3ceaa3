/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Configuration package for the Prompt Analyzer application.
 *
 * <p>This package contains configuration classes and beans for setting up application
 * properties, security, and integrations in the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Application and security configuration</li>
 *   <li>Integration with external services</li>
 *   <li>Support for custom configuration properties</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Configuration classes are used by the application to initialize and manage
 * settings, security, and integrations.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.config;
