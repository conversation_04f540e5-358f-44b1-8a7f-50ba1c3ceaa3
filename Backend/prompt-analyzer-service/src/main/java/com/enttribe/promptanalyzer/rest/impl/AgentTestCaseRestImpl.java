/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseResponseDto;
import com.enttribe.promptanalyzer.rest.AgentTestCaseRest;
import com.enttribe.promptanalyzer.service.AgentTestCaseService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/agent-test-case")
@RequiredArgsConstructor
public class AgentTestCaseRestImpl implements AgentTestCaseRest {

  private final AgentTestCaseService agentTestCaseService;

  @Override
  public Map<String, String> update(AgentTestCaseRequestDto dto) {
    return agentTestCaseService.update(dto, false);
  }

  @Override
  public List<AgentTestCaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    return agentTestCaseService.search(filter, offset, size, orderBy, orderType);
  }

  @Override
  public Long count(String filter) {
    return agentTestCaseService.count(filter);
  }

  @Override
  public Map<String, String> deleteById(String id) {
    return agentTestCaseService.deleteById(id);
  }
}
