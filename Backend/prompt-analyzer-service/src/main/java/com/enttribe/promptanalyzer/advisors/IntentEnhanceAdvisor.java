/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.advisors;

import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.AdvisorChain;
import org.springframework.ai.chat.client.advisor.api.BaseAdvisor;

/**
 * Context for the question is retrieved from a Vector Store and added to the prompt's user text.
 *
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * @since 1.0.0
 */
public class IntentEnhanceAdvisor implements BaseAdvisor {

  @Override
  public ChatClientRequest before(ChatClientRequest chatClientRequest, <PERSON><PERSON><PERSON><PERSON> advisor<PERSON>hain) {
    return null;
  }

  @Override
  public ChatClientResponse after(
      ChatClientResponse chatClientResponse, AdvisorChain advisorChain) {
    return null;
  }

  @Override
  public int getOrder() {
    return 0;
  }
}
