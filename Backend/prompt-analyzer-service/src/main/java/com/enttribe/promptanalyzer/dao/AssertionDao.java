/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.model.Assertion;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AssertionDao extends JpaRepository<Assertion, Long> {

  @Query("SELECT a FROM Assertion a WHERE a.nanoId = :nanoId AND a.deleted = false")
  Optional<Assertion> findByNanoId(String nanoId);

  @Query(
      "SELECT a FROM Assertion a WHERE a.assertionType = :assertionType AND a.matchStrategy = :matchStrategy AND a.deleted = false")
  Optional<Assertion> findByAssertionTypeAndMatchStrategy(
      AssertionType assertionType, MatchStrategy matchStrategy);

  @Query("SELECT a FROM Assertion a WHERE a.nanoId IN :nanoIds")
  List<Assertion> findAllByNanoIds(@Param("nanoIds") List<String> nanoIds);
}
