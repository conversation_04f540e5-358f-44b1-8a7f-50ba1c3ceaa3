/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.*;
import com.enttribe.promptanalyzer.dto.agent.AgentApiResponse;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseResponseDto;
import com.enttribe.promptanalyzer.dto.assertion.AssertionRequestDto;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.*;
import com.enttribe.promptanalyzer.service.AgentTestCaseService;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.AgentTestCaseUtils;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgentTestCaseServiceImpl implements AgentTestCaseService {

  private final AgentTestCaseDao agentTestCaseDao;
  private final PromptDao promptDao;
  private final ToolDao toolDao;
  private final KnowledgeBaseDao knowledgeBaseDao;
  private final CustomFilter customFilter;
  private final ApiService apiService;
  private final AssertionDao assertionDao;
  private final AgentTestCaseAssertionDao agentTestCaseAssertionDao;
  private final CustomerInfo customerInfo;

  @Override
  @Transactional
  public Map<String, String> create(AgentTestCaseRequestDto requestDto) {
    log.debug("Inside @method create with name: {}", requestDto.getName());
    Map<String, String> result = new HashMap<>();
    try {
      AgentApiResponse agentDetails =
          apiService.getAgentDetails(requestDto.getAgentId(), requestDto.getAgentType());
      Prompt prompt =
          promptDao
              .findByNanoId(agentDetails.getPromptId())
              .orElseThrow(() -> new ResourceNotFoundException("Prompt not found."));
      Set<Tool> tools =
          agentDetails.getToolIds() != null
              ? new HashSet<>(toolDao.findByNanoIds(agentDetails.getToolIds()))
              : new HashSet<>();
      Set<KnowledgeBase> knowledgeBases =
          agentDetails.getKnowledgeBaseIds() != null
              ? new HashSet<>(knowledgeBaseDao.findAllByNanoIds(agentDetails.getKnowledgeBaseIds()))
              : new HashSet<>();

      AgentTestCase entity = AgentTestCaseUtils.toEntity(requestDto, prompt, tools, knowledgeBases);

      Set<AgentTestCaseAssertion> agentTestCaseAssertions =
          buildAgentTestCaseAssertions(requestDto.getAssertions(), entity);
      entity.setAgentTestCaseAssertions(agentTestCaseAssertions);
      entity.setDeleted(false);
      AgentTestCase saved = agentTestCaseDao.save(entity);
      log.info("Successfully created agent test case with nanoId: {}", saved.getNanoId());
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      result.put("id", saved.getNanoId());
      return result;
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method create : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method create : {}", e.getMessage(), e);
      throw new BusinessException("Failed to create agent test case.", e);
    }
  }

  @Override
  @Transactional
  public Map<String, String> update(AgentTestCaseRequestDto requestDto, boolean forImport) {
    log.debug("Inside @method update with name: {}", requestDto.getName());
    try {
      AgentTestCase existingTestCase =
          agentTestCaseDao
              .findByName(requestDto.getName())
              .orElseThrow(
                  () -> new ResourceNotFoundException(PromptConstants.AGENTTESTCASE_NOT_FOUND));

      if (requestDto.getName() != null) {
        existingTestCase.setName(requestDto.getName());
      }
      if (requestDto.getDescription() != null) {
        existingTestCase.setDescription(requestDto.getDescription());
      }
      if (requestDto.getHistory() != null) {
        existingTestCase.setHistory(requestDto.getHistory());
      }
      if (requestDto.getAdvisors() != null) {
        existingTestCase.setAdvisors(requestDto.getAdvisors());
      }
      if (requestDto.getDeleted() != null) {
        existingTestCase.setDeleted(requestDto.getDeleted());
      }

      if (requestDto.getUserQuestion() != null) {
        existingTestCase.setUserQuestion(requestDto.getUserQuestion());
      }
      if (requestDto.getAgentName() != null) {
        existingTestCase.setAgentName(requestDto.getAgentName());
      }
      if (requestDto.getAgentId() != null) {
        existingTestCase.setAgentId(requestDto.getAgentId());
      }
      if (requestDto.getAgentType() != null) {
        existingTestCase.setAgentType(requestDto.getAgentType());
      }
      if (requestDto.getVariableMap() != null) {
        existingTestCase.setVariableMap(requestDto.getVariableMap());
      }

      Set<AgentTestCaseAssertion> agentTestCaseAssertions =
          buildAgentTestCaseAssertions(requestDto.getAssertions(), existingTestCase);
      if (forImport) {
        // here we do not clear previous assertion mappings, instead add new in the existing.
        existingTestCase.getAgentTestCaseAssertions().addAll(agentTestCaseAssertions);
      } else {
        // here we clear previous assertion mappings, which allows request coming from UI to remove
        // assertion from a test-case.
        existingTestCase.getAgentTestCaseAssertions().clear();
        existingTestCase.getAgentTestCaseAssertions().addAll(agentTestCaseAssertions);
      }

      existingTestCase.setModifiedTime(new Date());
      AgentTestCase saved = agentTestCaseDao.save(existingTestCase);
      log.info("Successfully updated agent test case with nanoId: {}", saved.getNanoId());
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS, "id", saved.getNanoId());
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method update : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method update : {}", e.getMessage(), e);
      throw new BusinessException("Failed to update agent test case.", e);
    }
  }

  private Set<AgentTestCaseAssertion> buildAgentTestCaseAssertions(
      List<AssertionRequestDto> assertionRequestDtos, AgentTestCase agentTestCase) {
    Set<AgentTestCaseAssertion> agentTestCaseAssertions = new HashSet<>();
    for (AssertionRequestDto assertionRequestDto : assertionRequestDtos) {
      log.debug(
          "assertion type : {} match strategy : {}",
          assertionRequestDto.getAssertionType(),
          assertionRequestDto.getMatchStrategy());
      Assertion assertion =
          assertionDao
              .findByAssertionTypeAndMatchStrategy(
                  assertionRequestDto.getAssertionType(), assertionRequestDto.getMatchStrategy())
              .orElseThrow(() -> new ResourceNotFoundException("assertion not found"));

      String parameterName =
          assertionRequestDto.getJsonPointer() == null ? "" : assertionRequestDto.getJsonPointer();
      AgentTestCaseAssertion agentTestCaseAssertion =
          agentTestCaseAssertionDao
              .findByTestCaseNameAndStrategyAndType(
                  agentTestCase.getName(),
                  assertionRequestDto.getMatchStrategy(),
                  assertionRequestDto.getAssertionType(),
                  parameterName)
              .orElse(new AgentTestCaseAssertion());
      agentTestCaseAssertion.setAgentTestCase(agentTestCase);
      agentTestCaseAssertion.setAssertion(assertion);
      String expectedValue = assertionRequestDto.getExpectedValue();
      String jsonPointer = assertionRequestDto.getJsonPointer();
      if (jsonPointer == null) {
        agentTestCaseAssertion.setExpectedValue(expectedValue);
      } else {
        agentTestCaseAssertion.setParameterName(jsonPointer);
        agentTestCaseAssertion.setExpectedValue(expectedValue);
      }
      agentTestCaseAssertion.setToolName(assertionRequestDto.getToolName());
      agentTestCaseAssertion.setCreatedTime(new Date());
      agentTestCaseAssertion.setModifiedTime(new Date());
      agentTestCaseAssertions.add(agentTestCaseAssertion);
    }
    return agentTestCaseAssertions;
  }

  @Override
  public List<AgentTestCaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug(
        "Inside @method search with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
        filter,
        offset,
        size,
        orderBy,
        orderType);
    try {
      List<AgentTestCase> agentTestCases =
          customFilter.searchByFilter(
              AgentTestCase.class, filter, orderBy, orderType, offset, size);
      List<AgentTestCaseResponseDto> result =
          agentTestCases.stream().map(AgentTestCaseUtils::toResponseDto).toList();
      log.info("Successfully searched agent test cases. Result count: {}", result.size());
      return result;
    } catch (Exception e) {
      log.error("Error in @method search : {}", e.getMessage(), e);
      throw new BusinessException("Failed to search agent test cases.", e);
    }
  }

  @Override
  public Long count(String filter) {
    log.debug("Inside @method count with filter: {}", filter);
    try {
      Long count = customFilter.countByFilter(AgentTestCase.class, filter);
      log.info("Successfully counted agent test cases. Count: {}", count);
      return count;
    } catch (Exception e) {
      log.error("Error in @method count : {}", e.getMessage(), e);
      throw new BusinessException("Failed to count agent test cases.", e);
    }
  }

  @Override
  public Map<String, String> deleteById(String id) {
    log.debug("Inside @method deleteById with @id: {}", id);
    Map<String, String> result = new HashMap<>();

    AgentTestCase agentTestCase =
        agentTestCaseDao
            .findByNanoId(id)
            .orElseThrow(
                () -> new ResourceNotFoundException(PromptConstants.AGENTTESTCASE_NOT_FOUND));
    try {
      // Ensure the current user is the creator of the agent test case
      customFilter.verifyCreator(customerInfo.getUserId(), agentTestCase.getCreator());

      agentTestCase.setDeleted(true);
      agentTestCaseDao.save(agentTestCase);
      log.info("Successfully soft deleted agent test case with ID: {}", id);
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (ResourceNotFoundException e) {
      log.error("Error in @method deleteById : {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Error in @method deleteById : {}", e.getMessage(), e);
      throw new BusinessException("Failed to delete agent test case", e);
    }
    return result;
  }
}
