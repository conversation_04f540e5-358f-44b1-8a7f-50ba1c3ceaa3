/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.rest.ProcessorRest;
import com.enttribe.promptanalyzer.service.ProcessorService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller implementation for processor-related operations. Handles HTTP requests for
 * saving, updating, searching, and counting processors.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/processor")
@RequiredArgsConstructor
public class ProcessorRestImpl implements ProcessorRest {

  private final ProcessorService processorService;

  /**
   * Saves a new processor.
   *
   * @param requestDto The processor data to be saved
   * @return A map containing the result of the save operation
   */
  @Override
  public Map<String, Object> save(ProcessorRequestDto requestDto) {
    log.info("Inside @method Saving processor");
    return processorService.save(requestDto);
  }

  /**
   * Updates an existing processor by ID.
   *
   * @param nanoId The ID of the processor to update
   * @param requestDto The updated processor data
   * @return A map containing the result of the update operation
   */
  @Override
  public Map<String, Object> update(String nanoId, ProcessorRequestDto requestDto) {
    log.info("Updating processor with ID: {}", nanoId);
    return processorService.update(nanoId, requestDto);
  }

  /**
   * Searches for processors based on specified criteria.
   *
   * @param filter Optional search filter
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (e.g., "ASC" or "DESC")
   * @return List of processors matching the search criteria
   */
  @Override
  public List<ProcessorResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching processors");
    return processorService.search(filter, offset, size, orderBy, orderType);
  }

  /**
   * Counts the total number of processors matching the filter criteria.
   *
   * @param filter Optional search filter
   * @return The total count of matching processors
   */
  @Override
  public Long count(String filter) {
    log.info("Counting processors");
    return processorService.count(filter);
  }
}
