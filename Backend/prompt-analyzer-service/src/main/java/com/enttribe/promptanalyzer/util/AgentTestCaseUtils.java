/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseAssertionReponseDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseResponseDto;
import com.enttribe.promptanalyzer.model.*;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AgentTestCaseUtils {

  private AgentTestCaseUtils() {}

  public static AgentTestCase toEntity(
      AgentTestCaseRequestDto dto,
      Prompt prompt,
      Set<Tool> tools,
      Set<KnowledgeBase> knowledgeBases) {
    AgentTestCase entity = new AgentTestCase();
    entity.setNanoId(NanoIdUtils.randomNanoId());
    entity.setName(dto.getName());
    entity.setAgentName(dto.getAgentName());
    entity.setAgentId(dto.getAgentId());
    entity.setAgentType(dto.getAgentType());
    entity.setDescription(dto.getDescription());
    entity.setHistory(dto.getHistory());
    entity.setAdvisors(dto.getAdvisors());
    entity.setDeleted(Boolean.TRUE.equals(dto.getDeleted()));
    entity.setPrompt(prompt);
    entity.setTools(tools != null ? tools : new HashSet<>());
    entity.setKnowledgeBases(knowledgeBases != null ? knowledgeBases : new HashSet<>());
    entity.setUserQuestion(dto.getUserQuestion());
    entity.setCreatedTime(new Date());
    entity.setModifiedTime(new Date());
    entity.setVariableMap(dto.getVariableMap());
    return entity;
  }

  public static AgentTestCaseResponseDto toResponseDto(AgentTestCase agentTestCase) {
    return AgentTestCaseResponseDto.builder()
        .id(agentTestCase.getNanoId())
        .agentId(agentTestCase.getAgentId())
        .agentName(agentTestCase.getAgentName())
        .agentType(agentTestCase.getAgentType())
        .name(agentTestCase.getName())
        .description(agentTestCase.getDescription())
        .history(agentTestCase.getHistory())
        .advisors(agentTestCase.getAdvisors())
        .deleted(agentTestCase.isDeleted())
        .createdTime(agentTestCase.getCreatedTime())
        .modifiedTime(agentTestCase.getModifiedTime())
        .userQuestion(agentTestCase.getUserQuestion())
        .variableMap(agentTestCase.getVariableMap())
        .assertions(
            Optional.ofNullable(agentTestCase.getAgentTestCaseAssertions())
                .orElse(Collections.emptySet())
                .stream()
                .map(AgentTestCaseUtils::toAssertionResponseDto) // calling your new method
                .toList())
        .build();
  }

  private static AgentTestCaseAssertionReponseDto toAssertionResponseDto(
      AgentTestCaseAssertion assertion) {
    String expectedValue = assertion.getExpectedValue();
    String jsonPointer = assertion.getParameterName();

    AgentTestCaseAssertionReponseDto dto = new AgentTestCaseAssertionReponseDto();
    dto.setId(assertion.getAssertion().getNanoId());
    dto.setAssertionType(
        assertion.getAssertion() != null
            ? String.valueOf(assertion.getAssertion().getAssertionType())
            : null);
    dto.setExpectedValue(expectedValue);
    dto.setJsonPointer(jsonPointer);
    dto.setToolName(assertion.getToolName());
    dto.setMatchStrategy(assertion.getAssertion().getMatchStrategy().toString());
    return dto;
  }
}
