/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteResponseDto;
import com.enttribe.promptanalyzer.rest.TestSuiteRest;
import com.enttribe.promptanalyzer.service.TestSuiteService;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/testSuite")
@RequiredArgsConstructor
public class TestSuiteRestImpl implements TestSuiteRest {

  private final TestSuiteService testSuiteService;

  @Override
  public Map<String, String> create(TestSuiteRequestDto requestDto) {
    log.info("Inside @class TestSuiteRestImpl create TestSuite");
    return testSuiteService.create(requestDto);
  }

  @Override
  public Map<String, String> update(TestSuiteRequestDto requestDto) {
    log.info("Inside @class TestSuiteRestImpl Updating TestSuite");
    return testSuiteService.update(requestDto);
  }

  @Override
  public List<TestSuiteResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Inside @class TestSuiteRestImpl Searching TestSuites");
    return testSuiteService.search(filter, offset, size, orderBy, orderType);
  }

  @Override
  public Long count(String filter) {
    log.info("Inside @class TestSuiteRestImpl Counting TestSuites");
    return testSuiteService.count(filter);
  }

  @Override
  public Map<String, String> deleteById(String id) {
    log.info("Inside @class TestSuiteRestImpl  Deleting TestSuite by Id: {}", id);
    return testSuiteService.deleteById(id);
  }

  @Override
  public Map<String, String> addAgentTestCasesToSuite(TestSuiteRequestDto requestDto) {
    log.info(
        "Inside @class TestSuiteRestImpl @method addAgentTestCasesToSuite for TestSuite: {}",
        requestDto.getId());
    return testSuiteService.addAgentTestCasesToSuite(requestDto);
  }

  @Override
  public ResponseEntity<Resource> exportTestCasesBySuiteId(String suiteId) {
    return testSuiteService.exportTestCasesBySuiteId(suiteId);
  }

  @Override
  public ResponseEntity<Resource> importTestCasesToSuite(String suiteId, MultipartFile file)
      throws IOException {
    return testSuiteService.importTestCasesToSuite(suiteId, file);
  }
}
