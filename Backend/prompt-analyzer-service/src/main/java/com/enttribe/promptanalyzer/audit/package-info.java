/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Audit package for the Prompt Analyzer application.
 *
 * <p>This package contains classes and utilities for auditing application events,
 * tool usage, and exception tracking in the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Audit event tracking</li>
 *   <li>Tool usage and exception auditing</li>
 *   <li>Support for compliance and monitoring</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Audit classes are used by services and controllers to monitor and record
 * application events and tool usage for compliance and analysis.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.audit;
