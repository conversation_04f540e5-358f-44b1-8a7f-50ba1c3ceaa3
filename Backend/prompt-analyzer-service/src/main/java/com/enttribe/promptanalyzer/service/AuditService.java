/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.ToolAudit;
import java.util.List;
import java.util.Map;

/**
 * Manages audit logging operations for prompts and exceptions. This service provides functionality
 * to save and retrieve audit records, helping track system activities and exceptions for monitoring
 * purposes.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface AuditService {

  /**
   * Saves an exception audit record.
   *
   * @param exceptionAuditDto the exception audit data transfer object
   * @return a string indicating the result of the save operation
   */
  String saveExceptionAudit(ExceptionAuditDto exceptionAuditDto);

  /**
   * Saves a prompt audit record.
   *
   * @param promptAuditDto the prompt audit data transfer object
   * @return a string indicating the result of the save operation
   */
  String savePromptAudit(PromptAuditDto promptAuditDto);

  /**
   * Retrieves a list of prompt audit records by audit ID.
   *
   * @param auditId the audit ID
   * @return a list of prompt audit records
   */
  List<PromptAudit> getPromptAuditListByAuditId(String auditId);

  /**
   * Searches for prompt audit records based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of prompt audit DTOs
   */
  List<PromptAuditDto> searchPromptAudit(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of prompt audit records matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching prompt audit records
   */
  Long countPromtAudit(String filter);

  /**
   * Retrieves a list of prompt audit records by prompt ID.
   *
   * @param promptId the prompt ID
   * @return a list of prompt audit records
   */
  List<PromptAudit> getPromptAuditListByPromptId(String promptId);

  /**
   * Saves a tool audit record.
   *
   * @param toolAuditDto the tool audit data transfer object
   * @return a map containing the result of the save operation
   */
  Map<String, String> saveToolAudit(ToolAuditDto toolAuditDto);

  /**
   * Retrieves a list of tool audit records by audit ID.
   *
   * @param auditId the audit ID
   * @return a list of tool audit records
   */
  List<ToolAudit> getToolAuditListByAuditId(String auditId);
}
