/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.config.redis;

import com.enttribe.promptanalyzer.vectorstore.Redis8VectorStore;
import com.enttribe.promptanalyzer.vectorstore.Redis8VectorStoreConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.DefaultJedisClientConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisClientConfig;
import java.time.Duration;
import javax.net.ssl.SSLSocketFactory;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.KeyManagementException;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;

/**
 * Configuration for Redis 8 Vector Store using native vector sets.
 * 
 * This configuration provides Redis 8 vector store beans that use the new
 * VADD, VSIM, VDEL commands instead of Redis Stack's FT. commands.
 * 
 * Enable this configuration by setting:
 * vector.store.type=redis8
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@ConditionalOnProperty(name = "vector.store.type", havingValue = "redis8")
public class Redis8VectorConfig {

    private static final Logger log = LoggerFactory.getLogger(Redis8VectorConfig.class);

    @Value("${spring.ai.vectorstore.redis.index-name:vector_store_knowledge_base}")
    private String indexName;

    @Value("${spring.ai.vectorstore.redis.prefix:prompt_smith_}")
    private String redisPrefix;

    @Value("${hint.collection.name:hint_collection}")
    private String hintCollectionName;

    @Value("${vector.store.redis8.quantization:8bit}")
    private String quantizationType;

    @Value("${vector.store.redis8.dimensionality-reduction.enabled:true}")
    private boolean dimensionalityReductionEnabled;

    @Value("${vector.store.redis8.dimensionality-reduction.target-dimensions:512}")
    private int targetDimensions;

    @Value("${vector.store.redis8.initialize-schema:true}")
    private boolean initializeSchema;

    // Redis connection properties
    @Value("${spring.data.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.data.redis.port:6379}")
    private int redisPort;

    @Value("${spring.data.redis.username:}")
    private String redisUsername;

    @Value("${spring.data.redis.password:}")
    private String redisPassword;

    @Value("${spring.data.redis.ssl.enable:false}")
    private boolean sslEnable;

    @Value("${vector.redis.trustStorePath:}")
    private String trustStorePath;

    @Value("${vector.redis.trustStorePassword:}")
    private String trustStorePassword;

    @Value("${spring.data.redis.timeout.connection:10000}")
    private int connectionTimeout;

    @Value("${spring.data.redis.timeout.socket:10000}")
    private int socketTimeout;

    @Value("${spring.data.redis.pool.max-total:50}")
    private int maxTotal;

    @Value("${spring.data.redis.pool.max-idle:20}")
    private int maxIdle;

    @Value("${spring.data.redis.pool.min-idle:5}")
    private int minIdle;

    /**
     * JedisPool bean for Redis 8 vector operations
     */
    @Bean
    @ConditionalOnProperty(name = "vector.store.type", havingValue = "redis8")
    public JedisPool jedisPool() throws Exception {
        log.info("Creating JedisPool for Redis 8 with host: {}, port: {}, ssl: {}",
                redisHost, redisPort, sslEnable);

        // Configure pool settings
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(30000));
        poolConfig.setMinEvictableIdleDuration(Duration.ofMillis(60000));
        poolConfig.setNumTestsPerEvictionRun(-1);

        // Build client configuration
        DefaultJedisClientConfig.Builder configBuilder = DefaultJedisClientConfig.builder()
                .socketTimeoutMillis(socketTimeout)
                .connectionTimeoutMillis(connectionTimeout);

        // Add authentication if provided
        if (redisUsername != null && !redisUsername.isEmpty()) {
            configBuilder.user(redisUsername);
        }
        if (redisPassword != null && !redisPassword.isEmpty()) {
            configBuilder.password(redisPassword);
        }

        // Configure SSL if enabled
        if (sslEnable) {
            configBuilder.ssl(true);

            if (trustStorePath != null && !trustStorePath.isEmpty()) {
                SSLSocketFactory sslSocketFactory = createSSLSocketFactory();
                configBuilder.sslSocketFactory(sslSocketFactory);
            }
        }

        JedisClientConfig clientConfig = configBuilder.build();

        return new JedisPool(poolConfig, new HostAndPort(redisHost, redisPort), clientConfig);
    }

    /**
     * Create SSL socket factory for Redis connection
     */
    private SSLSocketFactory createSSLSocketFactory() throws Exception {
        if (trustStorePath == null || trustStorePath.isEmpty()) {
            return (SSLSocketFactory) SSLSocketFactory.getDefault();
        }

        try {
            // Load certificate from file
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            X509Certificate cert;
            try (FileInputStream fis = new FileInputStream(trustStorePath)) {
                cert = (X509Certificate) cf.generateCertificate(fis);
            }

            // Create KeyStore and add certificate
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, null);
            keyStore.setCertificateEntry("redis-ca", cert);

            // Create TrustManager
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init(keyStore);

            // Create SSLContext
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, tmf.getTrustManagers(), null);

            return sslContext.getSocketFactory();
        } catch (Exception e) {
            log.error("Failed to create SSL socket factory", e);
            throw e;
        }
    }

    /**
     * Primary vector store bean using Redis 8 vector sets
     */
    @Bean(name = "redis8VectorStore")
    @Primary
    public VectorStore redis8VectorStore(JedisPool jedisPool,
                                       EmbeddingModel embeddingModel,
                                       BatchingStrategy batchingStrategy) {
        log.info("Creating Redis 8 vector store with vector set: {}, prefix: {}", indexName, redisPrefix);

        Redis8VectorStoreConfig config = Redis8VectorStoreConfig.builder()
            .vectorSetName(indexName)
            .keyPrefix(redisPrefix)
            .quantizationType(parseQuantizationType(quantizationType))
            .initializeSchema(initializeSchema)
            .targetDimensions(dimensionalityReductionEnabled ? targetDimensions : -1)
            .build();

        Redis8VectorStore vectorStore = Redis8VectorStore.builder(jedisPool, embeddingModel)
            .batchingStrategy(batchingStrategy)
            .config(config)
            .build();

        log.info("Redis 8 vector store created successfully with config: {}", config);
        return vectorStore;
    }

    /**
     * Hint vector store bean using Redis 8 vector sets
     */
    @Bean(name = "vectorStoreHint")
    public VectorStore redis8VectorStoreHint(JedisPool jedisPool,
                                           EmbeddingModel embeddingModel,
                                           BatchingStrategy batchingStrategy) {
        log.info("Creating Redis 8 hint vector store with vector set: {}", hintCollectionName);

        Redis8VectorStoreConfig config = Redis8VectorStoreConfig.builder()
            .vectorSetName(hintCollectionName)
            .keyPrefix("emp_base_")
            .quantizationType(parseQuantizationType(quantizationType))
            .initializeSchema(initializeSchema)
            .targetDimensions(dimensionalityReductionEnabled ? targetDimensions : -1)
            .build();

        Redis8VectorStore vectorStore = Redis8VectorStore.builder(jedisPool, embeddingModel)
            .batchingStrategy(batchingStrategy)
            .config(config)
            .build();

        log.info("Redis 8 hint vector store created successfully");
        return vectorStore;
    }

//    /**
//     * Custom batching strategy for Redis 8 vector operations
//     */
//    @Bean
//    @ConditionalOnProperty(name = "vector.store.redis8.batching.enabled", havingValue = "true", matchIfMissing = true)
//    public BatchingStrategy redis8BatchingStrategy(
//            @Value("${vector.store.redis8.batching.max-tokens:8000}") int maxTokens,
//            @Value("${vector.store.redis8.batching.reserve-percentage:0.1}") double reservePercentage) {
//
//        log.info("Creating Redis 8 batching strategy with maxTokens: {}, reservePercentage: {}",
//                maxTokens, reservePercentage);
//
//        return new TokenCountBatchingStrategy(
//            org.springframework.ai.tokenizer.JTokkitTokenCountEstimator.EncodingType.CL100K_BASE,
//            maxTokens,
//            reservePercentage
//        );
//    }

    /**
     * Parse quantization type from string configuration
     * Supports both Redis native values (Q8, BIN, NOQUANT) and friendly names
     */
    private Redis8VectorStoreConfig.QuantizationType parseQuantizationType(String type) {
        if (type == null) {
            return Redis8VectorStoreConfig.QuantizationType.QUANTIZE_8BIT;
        }

        return switch (type.toUpperCase()) {
            // Redis native values
            case "Q8", "8BIT", "QUANTIZE_8BIT" -> Redis8VectorStoreConfig.QuantizationType.QUANTIZE_8BIT;
            case "BIN", "BINARY" -> Redis8VectorStoreConfig.QuantizationType.BINARY;
            case "NOQUANT", "NONE", "FULL" -> Redis8VectorStoreConfig.QuantizationType.NONE;
            default -> {
                log.warn("Unknown quantization type: {}, defaulting to Q8 (8-bit)", type);
                yield Redis8VectorStoreConfig.QuantizationType.QUANTIZE_8BIT;
            }
        };
    }
}
