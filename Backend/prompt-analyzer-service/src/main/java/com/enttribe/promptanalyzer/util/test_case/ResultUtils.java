/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.dto.result.AssertionResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestCaseResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestResultResponseDto;
import com.enttribe.promptanalyzer.model.AssertionResult;
import com.enttribe.promptanalyzer.model.TestCaseResult;
import com.enttribe.promptanalyzer.model.TestResult;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ResultUtils {

  private ResultUtils() {
    throw new IllegalStateException("utility claas can not be instantiated");
  }

  public static List<AssertionResultResponseDto> toAssertionResultResponseDtos(
      Set<AssertionResult> assertionResults) {
    return assertionResults.stream().map(ResultUtils::toAssertionResultResponseDto).toList();
  }

  public static AssertionResultResponseDto toAssertionResultResponseDto(AssertionResult entity) {
    if (entity == null) {
      return null;
    }

    String expected = entity.getExpected();
    String jsonPointer = entity.getParameterName();

    return AssertionResultResponseDto.builder()
        .id(entity.getNanoId())
        .actual(entity.getActual())
        .expected(expected)
        .parameterName(jsonPointer)
        .isPassed(entity.isPassed())
        .userQuestion(entity.getUserQuestion())
        .assertionType(entity.getAssertionType())
        .matchStrategy(entity.getMatchStrategy())
        .comment(entity.getComment())
        .accuracyScore(entity.getAccuracyScore())
        .createdTime(entity.getCreatedTime())
        .build();
  }

  public static TestCaseResultResponseDto toTestCaseResultResponseDto(TestCaseResult entity) {
    if (entity == null) {
      return null;
    }

    return TestCaseResultResponseDto.builder()
        .id(entity.getNanoId())
        .accuracy(entity.getAccuracy())
        .latency(entity.getLatency())
        .isError(entity.isError())
        .runNumber(entity.getRunNumber())
        .promptToken(entity.getPromptToken())
        .generationTokens(entity.getGenerationTokens())
        .failureReason(entity.getFailureReason())
        .userQuestion(
            entity.getAgentTestCase() != null ? entity.getAgentTestCase().getUserQuestion() : null)
        .advisors(
            entity.getAgentTestCase() != null ? entity.getAgentTestCase().getAdvisors() : null)
        .name(entity.getAgentTestCase() != null ? entity.getAgentTestCase().getName() : null)
        .description(
            entity.getAgentTestCase() != null ? entity.getAgentTestCase().getDescription() : null)
        .isPassed(entity.isPassed())
        .assertionsPassed(entity.getAssertionsPassed())
        .assertionsFailed(entity.getAssertionsFailed())
        .createdTime(entity.getCreatedTime())
        .assertionResultsCount(
            entity.getAssertionResults() != null ? entity.getAssertionResults().size() : 0)
        .assertionResults(
            entity.getAssertionResults() != null && !entity.getAssertionResults().isEmpty()
                ? toAssertionResultResponseDtos(entity.getAssertionResults())
                : new ArrayList<>())
        .build();
  }

  public static TestResultResponseDto toTestResultResponseDto(TestResult entity) {
    if (entity == null) {
      return null;
    }

    return TestResultResponseDto.builder()
        .id(entity.getNanoId())
        .testSuiteName(entity.getTestSuite() != null ? entity.getTestSuite().getName() : null)
        .agentName(
            (entity.getTestSuite() != null && entity.getTestSuite().getAgentName() != null)
                ? entity.getTestSuite().getAgentName()
                : null)
        .agentType(
            (entity.getTestSuite() != null && entity.getTestSuite().getAgentType() != null)
                ? entity.getTestSuite().getAgentType()
                : null)
        .executionId(entity.getExecutionId())
        .accuracy(entity.getAccuracy())
        .latency(entity.getLatency())
        .consistency(entity.getConsistency())
        .numberOfRuns(entity.getNumberOfRuns())
        .testCasesPassed(entity.getTestCasesPassed())
        .testCasesFailed(entity.getTestCasesFailed())
        .promptToken(entity.getPromptToken())
        .generationTokens(entity.getGenerationTokens())
        .model(entity.getModel())
        .description(entity.getDescription())
        .status(entity.getStatus())
        .deleted(entity.getDeleted())
        .createdTime(entity.getCreatedTime())
        .modifiedTime(entity.getModifiedTime())
        .testCaseResultsCount(
            entity.getTestCaseResults() != null ? entity.getTestCaseResults().size() : 0)
        .build();
  }
}
