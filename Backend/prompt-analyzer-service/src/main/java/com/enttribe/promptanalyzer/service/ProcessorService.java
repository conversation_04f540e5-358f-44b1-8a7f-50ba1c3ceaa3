/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import java.util.List;
import java.util.Map;

/**
 * Service interface for managing Processor operations. This interface defines the contract for
 * handling processor-related business operations such as creating, updating, and searching
 * processors within the application.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ProcessorService {

  /**
   * Saves a new processor.
   *
   * @param requestDto the processor request DTO
   * @return a map containing the result of the save operation
   */
  Map<String, Object> save(ProcessorRequestDto requestDto);

  /**
   * Updates an existing processor by nano ID.
   *
   * @param nanoId the nano ID of the processor
   * @param requestDto the processor request DTO
   * @return a map containing the result of the update operation
   */
  Map<String, Object> update(String nanoId, ProcessorRequestDto requestDto);

  /**
   * Searches for processors based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of processor response DTOs
   */
  List<ProcessorResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of processors matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching processors
   */
  Long count(String filter);
}
