/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.AgentHistory;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * This interface provides data access methods for AgentHistory entities, allowing for CRUD
 * operations and custom queries. Author: VisionWaves Version: 1.0
 */
@Repository
public interface AgentHistoryDao extends JpaRepository<AgentHistory, Integer> {

  /**
   * Retrieves the agent history for a given agent name.
   *
   * @param agentName the name of the agent whose history is to be retrieved
   * @return an Optional containing the AgentHistory if found, otherwise empty
   */
  @Query("SELECT a FROM AgentHistory a WHERE a.agentName = :agentName")
  Optional<AgentHistory> getAgentHistory(String agentName);
}
