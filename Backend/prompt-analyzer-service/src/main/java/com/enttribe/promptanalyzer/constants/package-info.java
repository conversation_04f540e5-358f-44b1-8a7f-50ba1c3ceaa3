/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Constants package for the Prompt Analyzer application.
 *
 * <p>This package contains constant definitions used throughout the Prompt Analyzer
 * application. Constants provide centralized, reusable values for configuration,
 * API keys, and other fixed data.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Centralized constant definitions</li>
 *   <li>Support for configuration and API keys</li>
 *   <li>Improved maintainability and consistency</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Constants are used in all layers of the application to avoid magic numbers
 * and hard-coded values, promoting maintainable and readable code.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.constants;
