/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.config;

import com.enttribe.promptanalyzer.dao.ToolDao;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class McpToolService {

  private static final Logger log = LoggerFactory.getLogger(McpToolService.class);
  private final ToolDao toolDao;

  @Tool(description = "get list of tool name")
  public List<String> getListOfToolName() {
    log.info("inside @method getListOfToolName");
    return toolDao.getListOfToolName();
  }

  @Tool(description = "get list of tool name given application name")
  public List<String> getListOfToolNameByAppName(
      @ToolParam(description = "application name") String appName) {
    log.info("inside @method getListOfToolNameByAppName. application name : {}", appName);
    return toolDao.getListOfToolNameByAppName(appName);
  }

  @Tool(description = "Count tool by application name")
  public Long countToolByAppName(@ToolParam(description = "application name") String appName) {
    log.info("inside @method countToolByAppName. application name : {}", appName);
    return toolDao.countToolByAppName(appName);
  }

  @Tool(description = "send email")
  public String sendEmail(@ToolParam(description = "email id of recipient") String email) {
    log.info("inside @method sendEmail. email id of recipient : {}", email);

    return "email sent successfully";
  }
}
