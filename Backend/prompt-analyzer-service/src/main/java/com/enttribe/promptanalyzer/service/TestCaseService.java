/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.testcase.TestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseResponseDto;
import java.util.List;
import java.util.Map;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Manages test case operations and validation scenarios. This service handles the creation,
 * updating, and management of test cases, providing functionality for test case tracking and
 * execution with search capabilities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TestCaseService {

  /**
   * Creates a new test case.
   *
   * @param requestDto the test case request DTO
   * @return a map containing the result of the create operation
   */
  Map<String, String> create(TestCaseRequestDto requestDto);

  /**
   * Updates an existing test case.
   *
   * @param updatedTestcase the updated test case
   * @return a map containing the result of the update operation
   */
  Map<String, String> update(TestCaseRequestDto updatedTestcase);

  /**
   * Deletes a test case by its integer ID.
   *
   * @param id the test case ID
   * @return a map containing the result of the delete operation
   */
  Map<String, String> deleteTestcase(String id);

  /**
   * Searches for test cases based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of test cases
   */
  List<TestCaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of test cases matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching test cases
   */
  Long count(String filter);

  /**
   * Creates test cases in batch.
   *
   * @param requestDtos the list of test case request DTOs
   * @return a map containing the result of the batch creation
   */
  Map<String, String> createBatch(List<TestCaseRequestDto> requestDtos);

  /**
   * Imports test cases from a CSV file.
   *
   * @param file the CSV file containing test case data
   * @return a ResponseEntity containing the result of the import operation
   */
  ResponseEntity<Resource> importTestCase(MultipartFile file);

  /**
   * Exports test cases by their integer IDs.
   *
   * @param testCaseIds the list of test case IDs
   * @return a ResponseEntity containing the exported test cases as a CSV file
   */
  ResponseEntity<Resource> exportTestCasesByIds(List<String> testCaseIds);
}
