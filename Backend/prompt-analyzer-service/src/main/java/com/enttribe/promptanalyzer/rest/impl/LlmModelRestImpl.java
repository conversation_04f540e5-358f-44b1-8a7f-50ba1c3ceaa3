/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import com.enttribe.promptanalyzer.rest.LlmModelRest;
import com.enttribe.promptanalyzer.service.LlmModelService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing LLM (Language Learning Model) operations. Provides endpoints for
 * creating and retrieving LLM models, with security requirements for each operation. Supports SDK
 * integration and provider-specific queries.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/llm-model")
@RequiredArgsConstructor
@Slf4j
public class LlmModelRestImpl implements LlmModelRest {

  private final LlmModelService llmService;

  /**
   * Retrieves models for a specific provider.
   *
   * @param provider the name of the provider
   * @return a ProviderModelDto containing the models for the provider
   */
  @Override
  public ProviderModelDto getModelsByProvider(String provider) {
    log.info("Retrieving models for provider: {}", provider);
    return llmService.getModelsByProvider(provider);
  }

  /**
   * Retrieves all providers along with their models.
   *
   * @return a list of ProviderModelDto containing all providers with their models
   */
  @Override
  public List<ProviderModelDto> getAllProvidersWithModels() {
    log.info("Retrieving all providers with models");
    return llmService.getAllProvidersWithModels();
  }

  /**
   * Retrieves LLM models for a specific SDK application.
   *
   * @param appName the name of the application
   * @return a list of LlmModelSdkDto for the specified SDK application
   */
  @Override
  public List<LlmModelSdkDto> getLlmModelsForSDK(String appName) {
    log.info("Retrieving LLM models for SDK with appName: {}", appName);
    return llmService.getLlmModelsForSDK(appName, "chat");
  }

  /**
   * Retrieves LLM models by type for a specific SDK.
   *
   * @param type the type of LLM models to retrieve
   * @return a list of LlmModelSdkDto for the specified type
   */
  @Override
  public List<LlmModelSdkDto> getLlmModelsByTypeForSDK(String type) {
    log.info("Retrieving LLM models by type for SDK: {}", type);
    return llmService.getLlmModelsByTypeForSDK(type);
  }
}
