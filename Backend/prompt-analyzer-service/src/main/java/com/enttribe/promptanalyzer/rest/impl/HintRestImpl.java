/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.rest.HintRest;
import com.enttribe.promptanalyzer.service.HintService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/hint")
@RequiredArgsConstructor
public class HintRestImpl implements HintRest {

  private final HintService hintService;

  @Override
  public List<Map<String, Object>> searchPlanBatchV2(Map<String, Object> request) {
    List<String> queries = (List<String>) request.get("queries");
    String type = (String) request.get("type");
    String entityType = (String) request.get("entity");
    log.info(
        "Processing batch search request with {} queries type : {} entityType : {}",
        queries.size(),
        type,
        entityType);
    return hintService.searchPlanBatch(queries, type, entityType);
  }
}
