/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A tool for interacting with a knowledge base through vector-based search and retrieval. This
 * class provides functionality to search and retrieve answers from a document store based on vector
 * similarity and metadata filtering.
 *
 * <p>The tool uses a vector service to perform semantic searches and retrieve relevant information
 * based on input questions. It can be configured with specific metadata and filter expressions to
 * narrow down the search scope.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class KnowledgeBaseTool
    implements Function<KnowledgeBaseTool.Request, KnowledgeBaseTool.Response> {

  private static final Logger log = LoggerFactory.getLogger(KnowledgeBaseTool.class);
  private KnowledgeBaseService knowledgeBaseService;
  private String knowledgeBaseName;

  /**
   * Constructs a new KnowledgeBaseTool with the specified parameters.
   *
   * @param knowledgeBaseService The knowledge base service store implementation for performing
   *     searches
   * @param knowledgeBase Knowledge Base object containing necessary information
   * @param filterExpression Expression used to filter search results
   */
  public KnowledgeBaseTool(
      KnowledgeBaseService knowledgeBaseService,
      KnowledgeBase knowledgeBase,
      String filterExpression) {
    String metaData = knowledgeBase.getVectorMetaData();
    log.info("initializing KnowledgeBase tool. metaData : {}", metaData);
    this.knowledgeBaseService = knowledgeBaseService;
    this.knowledgeBaseName = knowledgeBase.getName();
    log.info("KnowledgeBase tool initialized with filterExpression : {}", filterExpression);
  }

  /** Instantiates a new Knowledge base tool. */
  public KnowledgeBaseTool() {}

  /**
   * Processes a question and returns a response based on the knowledge base content. This method
   * performs a vector search using the provided question and returns the most relevant answer found
   * in the document store.
   *
   * @param request The request containing the question to be answered
   * @return A Response object containing the answer retrieved from the knowledge base
   */
  @Override
  public Response apply(Request request) {
    log.info("inside function KnowledgeBase. request : {}", request.question());
    if (knowledgeBaseName == null) {
      log.warn("knowledge base tool is not properly initialized");
      return new Response("could not find answer");
    }
    Map<String, String> answer =
        knowledgeBaseService.getAnswer(knowledgeBaseName, request.question());
    return new Response(answer.get("answer"));
  }

  /**
   * Record representing a question request to the knowledge base.
   *
   * @param question The question to be answered
   */
  public record Request(@JsonProperty("question") String question) {}

  /**
   * Record representing the response from the knowledge base.
   *
   * @param result The answer retrieved from the knowledge base
   */
  public record Response(@JsonProperty("result") String result) {}
}
