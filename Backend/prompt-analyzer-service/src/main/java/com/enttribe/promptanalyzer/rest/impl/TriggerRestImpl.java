/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.rest.TriggerRest;
import com.enttribe.promptanalyzer.service.TriggerService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller for managing trigger operations. Provides endpoints for creating, updating,
 * searching, and counting triggers. All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/trigger")
@RequiredArgsConstructor
@Slf4j
public class TriggerRestImpl implements TriggerRest {

  private final TriggerService service;

  /**
   * Searches for triggers based on the provided filter and pagination options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the number of results to return
   * @param orderBy the field to order the results by
   * @param orderType the type of ordering (ascending or descending)
   * @return a list of trigger response data transfer objects matching the search criteria
   */
  @Override
  public List<TriggerResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Received request to search triggers");
    return service.search(filter, offset, size, orderBy, orderType);
  }
}
