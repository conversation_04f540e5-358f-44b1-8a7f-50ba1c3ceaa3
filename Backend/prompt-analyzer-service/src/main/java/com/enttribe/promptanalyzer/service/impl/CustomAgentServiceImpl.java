/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.audit.AuditAdvisor;
import com.enttribe.promptanalyzer.dao.AgentHistoryDao;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.model.AgentMessage;
import com.enttribe.promptanalyzer.service.CustomAgentService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.MessageUtils;
import com.enttribe.promptanalyzer.util.TemplateUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link CustomAgentService} interface. This class provides functionality for
 * managing custom agents in the system, including creating, updating, and retrieving agent
 * configurations. It handles the business logic for agent operations and manages the interaction
 * between custom agents and their associated configurations through the DAO layer.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomAgentServiceImpl implements CustomAgentService {

  private static final String SUCCESS = "success";
  private static final String STATUS = "status";
  private static final String CONDITION = "condition";
  private static final String MESSAGE = "message";
  private static final String FORMAT = "format";
  private static final String CONVERSATION_ID = "conversationId";

  private static final double TEMPERATURE = 0.2;


  private final AgentHistoryDao agentHistoryDao;
  private final NifiFlowService nifiFlowService;
  private final CustomAgentHelperService customAgentHelperService;
  private final InferenceManager inferenceManager;
  private final AuditAdvisor auditAdvisor;


  private final ObjectMapper objectMapper = JsonUtils.getObjectMapper();

  @Value("classpath:template/agent/orchestrator.st")
  private Resource orchestratorPrompt;

  @Value("classpath:template/agent/identify_processors.st")
  private Resource identifyProcessorsPrompt;


  private Map<String, String> processorMap;

  @PostConstruct
  public void init() {
    log.debug("Initializing CustomAgentServiceImpl");
    try {
      processorMap = loadJsonToMap("processors.json");
      log.info("Successfully loaded processor map with {} entries", processorMap.size());
    } catch (Exception e) {
      log.error("Error initializing processor map: {}", e.getMessage(), e);
      throw new BusinessException("Error initializing processor map: " + e.getMessage(), e);
    }
  }

  private static Map<String, String> loadJsonToMap(String resourcePath) {
    ObjectMapper mapper = new ObjectMapper();
    try (InputStream is = new ClassPathResource(resourcePath).getInputStream()) {
      return mapper.readValue(is, new TypeReference<Map<String, String>>() {});
    } catch (IOException e) {
      log.error("Error loading JSON resource: {}", e.getMessage(), e);
      return new HashMap<>();
    }
  }

  @Override
  public Map<String, String> getPlanforUserQuery(CustomAgentDto agent) {
    log.debug("Inside @method getPlanforUserQuery");
    boolean isFirstMessage = agent.getConversationId() == null;
    String conversationId = getConversationId(agent);
    agent.setConversationId(conversationId);
    try {
      log.info("Processing user query for conversation ID: {}", agent.getConversationId());
      String userQuery = agent.getUserQuery();
      String userQuery1 = userQuery;
      log.debug("Original user query: {}", userQuery);

      ChatClient chatClient = createChatClient();
      AgentHistory agentHistory =
          agentHistoryDao.getAgentHistory(agent.getProcessGroupId()).orElse(new AgentHistory());
      String contextForOrchestrator = getConversationContextForOrchestrator(agentHistory);

      boolean isValidating = false;
      if (agent.getUserQuery().contains("I accept the flow object")) {
        List<String> validationErrors =
            nifiFlowService.getValidationErrorsProcessGroup(agent.getProcessGroupId());
        userQuery =
            userQuery
                + "\n\n Validation message (ask user to rectify this if any) :\n"
                + validationErrors;
        log.info("Updated user query with validation messages: {}", userQuery);
        isValidating = true;
        userQuery1 = "Accept all steps";
      }

      String identifiedProcessorsJson;
      boolean askingForAllProcessors = false;
      OrchestratorResponse orchestratorResponse =
          getOrchestratorResponse(userQuery, chatClient, contextForOrchestrator);
      if (isValidating
          || (orchestratorResponse.fillProperties() && !orchestratorResponse.changeProcessor())) {
        identifiedProcessorsJson = agentHistory.getCurrentPlan();
        identifiedProcessorsJson =
            mergePlans(getPlan(agentHistory.getAgentMessages()), identifiedProcessorsJson);
        log.debug("Using existing plan for validation or property filling");
      } else {
        ProcessorMatchResponse processorMatchResponse =
            identifyProcessors(userQuery, chatClient, conversationId, contextForOrchestrator);
        askingForAllProcessors = processorMatchResponse.isAskingForProcessorsList();
        List<String> identifiedProcessors =
            processorMatchResponse.matchedProcessors().stream()
                .map(p -> processorMap.get(p.id()))
                .filter(Objects::nonNull)
                .toList();
        identifiedProcessorsJson = JsonUtils.convertToJSON(identifiedProcessors);
        log.debug("Identified {} processors for the query", identifiedProcessors.size());
      }

      PopulatedProcessors populatedProcessors;
      if (askingForAllProcessors) {
        log.info("User is asking for all processors list");
        populatedProcessors =
            new PopulatedProcessors(
                List.of(),
                "Sorry, I can't provide processors list. Please specify a specific processor.");
      } else {
        populatedProcessors =
            customAgentHelperService.populateProcessors(
                userQuery, chatClient, identifiedProcessorsJson, agentHistory.getLastFlow());
      }

      String message = populatedProcessors.message();
      String plan = JsonUtils.convertToJSON(populatedProcessors.processors());

      if (isValidating) {
        plan = mergePlans(plan, getPlan(agentHistory.getAgentMessages()));
        agentHistory.setCurrentPlan(plan);
        log.debug("Merged plans for validation");
      }

      log.debug("Saving agent history for conversation ID: {}", conversationId);
      agentHistory.setAgentName(agent.getProcessGroupId());
      AgentMessage agentMessage = new AgentMessage();
      agentMessage.setTimeStamp(agent.getTimeStamp());
      agentMessage.setUserMessage(userQuery1);

      JSONObject assistantMessageJson = new JSONObject();
      assistantMessageJson.put(MESSAGE, message);
      assistantMessageJson.put("plan", plan);
      assistantMessageJson.put(CONVERSATION_ID, conversationId);
      assistantMessageJson.put(STATUS, SUCCESS);
      String assistantMessage = assistantMessageJson.toString();
      agentMessage.setAssistantMessage(assistantMessage);

      agentMessage.setAgentHistory(agentHistory);
      agentHistory.getAgentMessages().add(agentMessage);
      agentHistory.setLastFlow(JsonUtils.convertToJSON(populatedProcessors.processors()));
      agentHistoryDao.save(agentHistory);
      log.info("Successfully saved agent history for conversation ID: {}", conversationId);

      if (isValidating && !isFirstMessage) {
        return Map.of(
            STATUS, SUCCESS, MESSAGE, message, CONVERSATION_ID, conversationId, "plan", "");
      }
      if (plan.equals("[]")) {
        plan = "";
      }
      return Map.of(
          STATUS, SUCCESS, MESSAGE, message, CONVERSATION_ID, conversationId, "plan", plan);
    } catch (Exception e) {
      log.error(
          "Error generating plan for conversation ID {}: {}", conversationId, e.getMessage(), e);
      return Map.of(
          STATUS,
          SUCCESS,
          MESSAGE,
          MessageUtils.getErrorMessage(),
          CONVERSATION_ID,
          conversationId);
    }
  }

  private String mergePlans(String currentPlan, String previousPlan) {
    log.debug("Merging plans - current: {}, previous: {}", currentPlan, previousPlan);
    if (previousPlan == null || previousPlan.isEmpty()) {
      return currentPlan;
    }
    try {
      List<Map<String, Object>> selectedList =
          objectMapper.readValue(currentPlan, new TypeReference<>() {});
      List<Map<String, Object>> latestList =
          objectMapper.readValue(previousPlan.replace("\\\"", "\""), new TypeReference<>() {});

      Map<String, Map<String, Object>> latestMap =
          latestList.stream().collect(Collectors.toMap(p -> p.get("name").toString(), p -> p));

      return margePlansExtract(selectedList, latestMap);
    } catch (Exception e) {
      log.error("Error merging plans: {}", e.getMessage(), e);
      return currentPlan;
    }
  }

  private String margePlansExtract(
      List<Map<String, Object>> selectedList, Map<String, Map<String, Object>> latestMap)
      throws JsonProcessingException {
    try {
      log.debug("Extracting and merging plans for {} processors", selectedList.size());
      for (Map<String, Object> processor : selectedList) {
        String name = processor.get("name").toString();
        if (latestMap.containsKey(name)) {
          mergeConditions(processor, latestMap.get(name));
        }
      }
      log.debug("Successfully merged plans for {} processors", selectedList.size());
      return objectMapper.writeValueAsString(selectedList);
    } catch (Exception e) {
      log.error("Error extracting merged plans: {}", e.getMessage(), e);
      throw new BusinessException("Error merging plans: " + e.getMessage(), e);
    }
  }

  private void mergeConditions(Map<String, Object> processor, Map<String, Object> latestProcessor) {
    log.debug("Merging conditions for processor: {}", processor.get("name"));
    if (latestProcessor.containsKey(CONDITION)) {
      Map<String, Object> latestCondition = (Map<String, Object>) latestProcessor.get(CONDITION);
      Map<String, Object> currentCondition = (Map<String, Object>) processor.get(CONDITION);
      for (Map.Entry<String, Object> entry : latestCondition.entrySet()) {
        if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
          currentCondition.put(entry.getKey(), entry.getValue());
        }
      }
    }
  }

  private String getConversationContextForOrchestrator(AgentHistory agentHistory) {
    log.debug("Getting conversation context for orchestrator");
    List<AgentMessage> agentMessages = agentHistory.getAgentMessages();
    StringBuilder conversation = new StringBuilder();
    for (AgentMessage agentMessage : agentMessages) {
      conversation.append("user : ").append(agentMessage.getUserMessage()).append("\n");
      String assistantMessage = agentMessage.getAssistantMessage();
      JSONObject jsonObject = new JSONObject(assistantMessage);
      if (jsonObject.has(MESSAGE)) {
        assistantMessage = jsonObject.getString(MESSAGE);
      } else {
        assistantMessage = "";
      }
      conversation.append("LLM : ").append(assistantMessage).append("\n");
    }
    String plan = agentHistory.getCurrentPlan();
    conversation.append("\nlatest plan is : ").append(plan);
    return conversation.toString();
  }

  private String getPlan(List<AgentMessage> agentMessages) {
    log.debug("Getting plan from {} agent messages", agentMessages.size());
    String plan = "";
    if (!agentMessages.isEmpty()) {
      AgentMessage lastMessage = agentMessages.getLast();
      String assistantMessage = lastMessage.getAssistantMessage();
      JSONObject jsonObject = new JSONObject(assistantMessage);
      if (jsonObject.has("plan")) {
        plan = jsonObject.getString("plan");
      }
    }
    return plan;
  }

  private String getConversationId(CustomAgentDto agent) {
    String conversationId =
        agent.getConversationId() == null
            ? UUID.randomUUID().toString()
            : agent.getConversationId();
    log.debug("Generated/Retrieved conversation ID: {}", conversationId);
    return conversationId;
  }

  public ChatClient createChatClient() {
    log.debug("Creating chat client with Llama model");
    try {
      OpenAiChatOptions options = new OpenAiChatOptions();
      options.setModel("llama-3.3-70b-versatile");
      options.setTemperature(TEMPERATURE);
      options.setResponseFormat(
          ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build());

      ChatModel model = inferenceManager.getChatModelByProvider("groq");

      return ChatClient.builder(model)
          .defaultAdvisors(auditAdvisor)
          .defaultToolContext(Map.of("auditId", UUID.randomUUID().toString()))
          .defaultOptions(options)
          .build();
    } catch (Exception e) {
      log.error("Error creating chat client: {}", e.getMessage(), e);
      throw new BusinessException("Error creating chat client: " + e.getMessage(), e);
    }
  }

  public OrchestratorResponse getOrchestratorResponse(
      String userQuery, ChatClient chatClient, String conversationHistory) {
    try {
      log.debug("Getting orchestrator response for query: {}", userQuery);
      BeanOutputConverter<OrchestratorResponse> converter =
          new BeanOutputConverter<>(OrchestratorResponse.class);
      String resolvedPrompt =
          TemplateUtils.getResolvedPrompt(
              orchestratorPrompt,
              Map.of("conversationHistory", conversationHistory, FORMAT, converter.getFormat()));

      String plan = chatClient.prompt().system(resolvedPrompt).user(userQuery).call().content();

      OrchestratorResponse orchestratorResponse =
          JsonUtils.convertJsonToObject(plan, OrchestratorResponse.class);
      log.debug("Successfully got orchestrator response: {}", plan);
      return orchestratorResponse;
    } catch (Exception e) {
      log.error("Error getting orchestrator response: {}", e.getMessage(), e);
      return null;
    }
  }

  public ProcessorMatchResponse identifyProcessors(
          String userQuery, ChatClient chatClient, String conversationId, String conversationHistory) {
    try {
      log.debug(
              "Identifying processors for query: {} and conversation ID: {}",
              userQuery,
              conversationId);
      BeanOutputConverter<ProcessorMatchResponse> converter =
              new BeanOutputConverter<>(ProcessorMatchResponse.class);

      List<String> documents = customAgentHelperService.getRelevantContext(userQuery, chatClient);

      Map<String, Object> variableMap =
              Map.of(
                      "context",
                      documents,
                      FORMAT,
                      converter.getFormat(),
                      "conversationHistory",
                      conversationHistory,
                      "currentMessage",
                      userQuery);

      String resolvedPrompt =
              TemplateUtils.getResolvedPrompt(identifyProcessorsPrompt, variableMap);

      String plan = chatClient.prompt().system(resolvedPrompt).user(userQuery).call().content();

      ProcessorMatchResponse orchestratorResponse =
              JsonUtils.convertJsonToObject(plan, ProcessorMatchResponse.class);
      log.debug("Successfully identified processors: {}", plan);
      return orchestratorResponse;
    } catch (Exception e) {
      log.error("Error identifying processors: {}", e.getMessage(), e);
      return null;
    }
  }

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record OrchestratorResponse(
      @JsonPropertyDescription("True if a processor is getting added or removed, else false")
          Boolean changeProcessor,
      @JsonPropertyDescription("True if properties of processor is provided, else false")
          Boolean fillProperties,
      @JsonPropertyDescription("reasoning for this decision") String reasoning) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record ProcessorMatchResponse(
      List<MatchedProcessor> matchedProcessors,
      @JsonPropertyDescription("True if user is asking for the list of processors, else false")
          Boolean isAskingForProcessorsList,
      @JsonPropertyDescription("reasoning for this decision") String reasoning) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record MatchedProcessor(
      @JsonPropertyDescription("name of the processor") String name,
      @JsonPropertyDescription("Id of the processor") String id) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record PopulatedProcessors(
      @JsonPropertyDescription("list of all processors") List<Processor> processors,
      @JsonPropertyDescription("message to be shown to the user") String message) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Processor(
      String name,
      @JsonProperty("package") String _package,
      String description,
      @JsonPropertyDescription("type can either be TRIGGER or ACTION") String type,
      @JsonPropertyDescription("fields from parameters of the processor")
          Map<String, String> condition) {}

  @Override
  public Map<String, String> createTriggerNameDescription(String userQuery) {
    try {
      log.debug("Creating trigger name and description for query: {}", userQuery);
      ChatClient chatClient = createChatClient();
      ChatClient.ChatClientRequestSpec triggerNameRequest =
          chatClient
              .prompt()
              .system(
                  """
                              You are a naming assistant responsible for creating meaningful and logical business names and descriptions for NiFi data flows. Your task is to take the user's input query, analyze the business context, and generate a clear, concise, and descriptive name and description that reflects the purpose and functionality of the NiFi flow.

                            Guidelines for Output:

                            Provide the result as a JSON object with the following structure:

                            {
                              "name": "Business Name in User friendly title case",
                              "description": "Detailed description of the flow's functionality and purpose."
                            }

                            Guidelines for Naming:

                            - Use space-separated Title Case (e.g., "Customer Payment Kafka to Postgres").
                            - Include essential business functions (e.g., Data Ingestion, Data Transformation, or Data Enrichment).
                            - Indicate key data sources, destinations, or domains (e.g., Customer Data, Payment Processing).
                            - Maintain clarity and brevity (avoid overly long names).
                             Guidelines for Description:

                            Provide a brief yet comprehensive explanation of the flow's purpose.
                            Include the key operations performed and data sources or destinations involved.
                            Examples:

                            Query: "Process customer payment data from Kafka and send to PostgreSQL"
                            Output:

                            {
                              "name": "Customer Payment Kafka to Postgres",
                              "description": "This flow processes customer payment data received from Kafka and stores it in a PostgreSQL database."
                            }


                            The output should be a parsable json with no additional information
                            """)
              .user(userQuery);

      String triggerName =
          triggerNameRequest.call().chatResponse().getResult().getOutput().getText();
      log.info("Successfully created trigger name and description");
      return Map.of(STATUS, SUCCESS, "data", triggerName);
    } catch (Exception e) {
      log.error("Error creating trigger name and description: {}", e.getMessage(), e);
      return Map.of(STATUS, "failed");
    }
  }
}
