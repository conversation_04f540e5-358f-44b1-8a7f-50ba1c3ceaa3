/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCase;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for converting TestCase entities to TestCaseRequestDto objects and vice versa.
 * Provides methods to convert individual entities and lists of entities.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public final class TestCaseUtils {

  // Private constructor to prevent instantiation
  private TestCaseUtils() {}

  /**
   * Converts a TestCase entity to a TestCaseRequestDto object.
   *
   * @param testcase the TestCase entity to convert
   * @return the converted TestCaseRequestDto object
   */
  public static TestCaseRequestDto getTestCaseDto(TestCase testcase) {
    return TestCaseRequestDto.builder()
        .testcaseId(testcase.getTestcaseId())
        .assertions(testcase.getAssertions())
        .inputJson(testcase.getInputJson())
        .remark(testcase.getRemark())
        .prompt(PromptConvertor.getPromptDto(testcase.getPrompt()))
        .build();
  }

  public static TestCaseResponseDto getTestCaseResponseDto(TestCase testcase) {
    return TestCaseResponseDto.builder()
        .id(testcase.getNanoId())
        .testcaseId(testcase.getTestcaseId())
        .assertions(testcase.getAssertions())
        .inputJson(testcase.getInputJson())
        .remark(testcase.getRemark())
        .prompt(PromptConvertor.getPromptDto(testcase.getPrompt()))
        .build();
  }

  /**
   * Converts a TestCaseRequestDto object to a TestCase entity.
   *
   * @param dto the TestCaseRequestDto object to convert
   * @return the converted TestCase entity
   */
  public static TestCase getTestCase(TestCaseRequestDto dto, PromptDao promptDao) {
    PromptConvertorDto promptDto = dto.getPrompt();
    Prompt prompt;
    if (promptDto != null && promptDto.getId() != null) {
      prompt =
          promptDao
              .findByNanoId(promptDto.getId())
              .orElseThrow(() -> new ResourceNotFoundException("prompt not found"));
    } else {
      throw new BusinessException("prompt id is not provided");
    }
    return TestCase.builder()
        .assertions(dto.getAssertions())
        .inputJson(dto.getInputJson())
        .remark(dto.getRemark())
        .nanoId(NanoIdUtils.randomNanoId())
        .prompt(prompt)
        .modifiedTime(new Date())
        .build();
  }

  /**
   * Converts a list of TestCase entities to a list of TestCaseRequestDto objects.
   *
   * @param testcases the list of TestCase entities to convert
   * @return a list of converted TestCaseRequestDto objects
   */
  public static List<TestCaseRequestDto> getTestCaseDtoList(List<TestCase> testcases) {
    return testcases.stream().map(TestCaseUtils::getTestCaseDto).toList();
  }

  public static List<TestCaseResponseDto> getTestCaseResponseDtoList(List<TestCase> testcases) {
    return testcases.stream().map(TestCaseUtils::getTestCaseResponseDto).toList();
  }
}
