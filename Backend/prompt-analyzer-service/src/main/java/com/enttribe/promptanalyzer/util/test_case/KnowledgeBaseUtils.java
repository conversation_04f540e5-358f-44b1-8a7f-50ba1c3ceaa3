/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.enttribe.promptanalyzer.service.LlmApiService;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;

/**
 * Utility class for managing and interacting with Knowledge Base functionality. This class provides
 * methods for creating knowledge base tools and handling knowledge base variables. It supports both
 * static tool creation and dynamic code execution for knowledge bases.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
public class KnowledgeBaseUtils {

  private final VectorStore vectorStore;
  private final LlmApiService llmApiService;
  private final KnowledgeBaseService knowledgeBaseService;

  /**
   * Creates a FunctionCallback for a given knowledge base that can be used as a tool. This method
   * wraps a KnowledgeBase into a functional tool that can be used for retrieving and processing
   * knowledge base information.
   *
   * @param knowledgeBase the knowledge base entity to create a tool for
   * @return FunctionCallback configured with the knowledge base tool
   */
  public ToolCallback getKnowledgeBaseToolV1(KnowledgeBase knowledgeBase) {

    String filterExpression =
        knowledgeBase.getFilter() != null
            ? String.format("doc_id == '%s'", knowledgeBase.getFilter())
            : null;

    boolean returnDirect =
        knowledgeBase.getReturnDirect() != null && knowledgeBase.getReturnDirect();

    if (knowledgeBase.getType().equals("SQL")) {
      SchemaProviderTool schemaProviderTool = new SchemaProviderTool(vectorStore, knowledgeBase);
      return FunctionToolCallback.builder(knowledgeBase.getName(), schemaProviderTool)
          .inputType(SchemaProviderTool.Request.class)
          .description(knowledgeBase.getDescription())
          .build();
    } else {
      if (returnDirect) {
        KnowledgeBaseReturnDirect knowledgeBaseTool =
            new KnowledgeBaseReturnDirect(
                vectorStore, knowledgeBase, llmApiService, filterExpression);
        return FunctionToolCallback.builder(knowledgeBase.getName(), knowledgeBaseTool)
            .inputType(KnowledgeBaseReturnDirect.Request.class)
            .description(knowledgeBase.getDescription())
            .toolMetadata(ToolMetadata.builder().returnDirect(true).build())
            .build();
      }
      KnowledgeBaseTool knowledgeBaseTool =
          new KnowledgeBaseTool(knowledgeBaseService, knowledgeBase, filterExpression);
      return FunctionToolCallback.builder(knowledgeBase.getName(), knowledgeBaseTool)
          .inputType(KnowledgeBaseTool.Request.class)
          .description(knowledgeBase.getDescription())
          .build();
    }
  }

  /**
   * Creates a FunctionCallback for a given knowledge base that can be used as a tool. This method
   * wraps a KnowledgeBase into a functional tool that can be used for retrieving and processing
   * knowledge base information.
   *
   * @param knowledgeBase the knowledge base entity to create a tool for
   * @return FunctionCallback configured with the knowledge base tool
   */
  public static ToolCallback getKnowledgeBaseTool(KnowledgeBase knowledgeBase) {
    KnowledgeBaseTool knowledgeBaseTool = new KnowledgeBaseTool();
    return FunctionToolCallback.builder(knowledgeBase.getName(), knowledgeBaseTool)
        .inputType(KnowledgeBaseTool.Request.class)
        .description(knowledgeBase.getDescription())
        .build();
  }

  public static List<ToolCallback> getKnowledgeBaseTools(Set<KnowledgeBase> knowledgeBases) {
    return knowledgeBases.stream().map(KnowledgeBaseUtils::getKnowledgeBaseTool).toList();
  }

  public List<ToolCallback> getKnowledgeBaseToolsV1(Set<KnowledgeBase> knowledgeBases) {
    return knowledgeBases.stream().map(this::getKnowledgeBaseToolV1).toList();
  }
}
