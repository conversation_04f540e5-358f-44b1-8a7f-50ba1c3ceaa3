/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.TestCase;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for TestCase entity operations. Provides methods to interact with
 * the TestCase table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TestCaseDao extends JpaRepository<TestCase, Integer> {

  /**
   * Finds a test case by its testcaseId.
   *
   * @param testcaseId The testcaseId to search for
   * @return Optional containing the test case if found, empty otherwise
   */
  Optional<TestCase> findByTestcaseId(String testcaseId);

  @Query("SELECT  t FROM TestCase t WHERE t.nanoId = :nanoId")
  Optional<TestCase> findByNanoId(@Param("nanoId") String nanoId);

  @Query("SELECT t FROM TestCase t WHERE t.nanoId IS NULL")
  List<TestCase> findFirstBatchWithNullNanoId(Pageable pageable);

  @Query("SELECT t FROM TestCase t WHERE t.nanoId IN :testCaseNanoIds AND t.deleted = false")
  List<TestCase> findAllByNanoIds(List<String> testCaseNanoIds);
}
