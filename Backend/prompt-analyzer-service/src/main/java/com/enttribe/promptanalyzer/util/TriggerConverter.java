/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.model.Trigger;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for converting Trigger entities to TriggerResponseDto objects. Provides methods to
 * convert individual entities and lists of entities.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public final class TriggerConverter {

  // Private constructor to prevent instantiation
  private TriggerConverter() {}

  /**
   * Converts a list of Trigger entities to a list of TriggerResponseDto objects.
   *
   * @param triggers the list of Trigger entities to convert
   * @return a list of converted TriggerResponseDto objects
   */
  public static List<TriggerResponseDto> getTriggerDtoList(List<Trigger> triggers) {
    log.debug("Converting list of Trigger entities to TriggerResponseDto objects");
    return triggers.stream().map(TriggerConverter::convertToTriggerDto).toList();
  }

  /**
   * Converts a single Trigger entity to a TriggerResponseDto object.
   *
   * @param trigger the Trigger entity to convert
   * @return the converted TriggerResponseDto object
   */
  public static TriggerResponseDto convertToTriggerDto(Trigger trigger) {
    return TriggerResponseDto.builder()
        .id(trigger.getNanoId())
        .name(trigger.getName())
        .displayName(trigger.getDisplayName())
        .description(trigger.getDescription())
        .metadata(trigger.getMetadata())
        .application(trigger.getApplication())
        .entity(trigger.getEntity())
        .type(trigger.getType())
        .build();
  }
}
