/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.assertion.AssertionResponseDto;
import java.util.List;

/**
 * Service interface for managing Assertion entities.
 *
 * <p>Defines business operations for creating, updating, retrieving, deleting, searching, and
 * counting assertions.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface AssertionService {

  /**
   * Searches for assertions with optional filtering, pagination, and sorting.
   *
   * @param filter the filter string (optional)
   * @param offset the starting index for pagination (required)
   * @param size the number of results to return (required)
   * @param orderBy the field to order by (optional)
   * @param orderType the order direction (asc/desc, optional)
   * @return a list of response DTOs containing assertions and pagination information
   */
  List<AssertionResponseDto> searchAssertion(
      String filter, Integer offset, Integer size, String orderBy, String orderType);
}
