/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.McpServerDao;
import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.service.McpServerService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.SdkUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link McpServerService} interface. This class provides the business logic
 * for managing MCP servers. It handles server creation, updating, searching, and data mapping
 * operations while interacting with the data access layer.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class McpServerServiceImpl implements McpServerService {

  private final McpServerDao mcpServerDao;
  private final CustomFilter customFilter;
  private final CustomerInfo customerInfo;

  @Override
  public Map<String, String> create(McpServerDto serverDto) {
    log.debug("@class McpServerServiceImpl @method create going to create MCP Server");
    Map<String, String> result = new HashMap<>();
    try {
      McpServer mcpServer = new McpServer();
      mapDtoToEntity(serverDto, mcpServer);
      // Set creation and modification times
      Date now = new Date();
      mcpServer.setCreatedTime(now);
      mcpServer.setModifiedTime(now);
      mcpServer.setNanoId(NanoIdUtils.randomNanoId());
      mcpServerDao.save(mcpServer);
      log.debug("Successfully saved MCP Server");
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      return result;
    } catch (Exception e) {
      log.error("Exception occurred while saving MCP Server: {}", e.getMessage(), e);
      throw new BusinessException("Failed to save MCP Server");
    }
  }

  @Override
  public Map<String, String> update(McpServerDto serverDto) {
    log.debug(
        "@class McpServerServiceImpl @method update going to update MCP Server with ID: {}",
        serverDto.getId());
    Map<String, String> result = new HashMap<>();
    try {
      McpServer existingServer =
          mcpServerDao
              .findByNanoId(serverDto.getId())
              .orElseThrow(() -> new ResourceNotFoundException("MCP Server not found"));

      mapDtoToEntity(serverDto, existingServer);
      // Update modification time
      existingServer.setModifiedTime(new Date());
      mcpServerDao.save(existingServer);

      log.debug("Successfully updated MCP Server with ID: {}", serverDto.getId());
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      return result;
    } catch (ResourceNotFoundException e) {
      log.warn("MCP Server not found with Id: {}", serverDto.getId());
      throw e;
    } catch (Exception e) {
      log.error("Exception occurred while updating MCP Server: {}", e.getMessage(), e);
      throw new BusinessException("Unable to update MCP Server");
    }
  }

  @Override
  public List<McpServerDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    try {
      log.debug("Searching for MCP Servers with filter: {}", filter);
      List<McpServer> mcpServers =
          customFilter.searchByFilter(McpServer.class, filter, orderBy, orderType, offset, size);
      return SdkUtils.getMcpServersDtoListSdk(mcpServers);
    } catch (Exception e) {
      log.error("Error while searching for MCP Servers: {}", e.getMessage(), e);
      throw new BusinessException("Failed to search MCP Servers");
    }
  }

  @Override
  public Long count(String filter) {
    try {
      log.debug("Counting MCP Servers");
      return customFilter.countByFilter(McpServer.class, filter);
    } catch (Exception e) {
      log.error("Error while counting MCP Servers: {}", e.getMessage(), e);
      throw new BusinessException("Failed to count mcp servers");
    }
  }

  @Override
  public Map<String, String> softDelete(String id) {
    log.debug(
        "@class McpServerServiceImpl @method delete going to soft delete MCP Server with ID: {}",
        id);
    Map<String, String> result = new HashMap<>();
    try {
      Optional<McpServer> serverOptional = mcpServerDao.findByNanoId(id);
      if (serverOptional.isEmpty()) {
        result.put(PromptConstants.RESULT, PromptConstants.FAILED);
        result.put("message", "MCP Server not found with ID: " + id);
        return result;
      }
      McpServer server = serverOptional.get();
      customFilter.verifyCreator(customerInfo.getUserId(), server.getCreator());
      server.setDeleted(true);
      mcpServerDao.save(server);
      log.debug("Successfully soft deleted MCP Server with ID: {}", id);
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      return result;
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (Exception e) {
      log.error("Exception occurred while soft deleting MCP Server: {}", e.getMessage(), e);
      throw new BusinessException("Unable to delete MCP Server");
    }
  }

  @Override
  public List<McpServerDto> getMcpServerByIds(List<String> ids) {
    log.debug("Inside @method getMcpServerByIds @param IDs: {}", ids);
    try {
      List<McpServer> mcpServers = mcpServerDao.findByNanoIds(ids);
      return SdkUtils.getMcpServersDtoListSdk(mcpServers);
    } catch (Exception e) {
      log.warn("Inside @method getMcpServerByIds @exception : {}", e.getMessage(), e);
      throw new BusinessException("Failed to get MCP servers by IDs");
    }
  }

  private McpServer mapDtoToEntity(McpServerDto dto, McpServer server) {
    log.debug("Mapping McpServerDto to McpServer entity: {}", dto.getName());
    if (server.getNanoId() == null) {
      server.setName(NanoIdUtils.randomNanoId());
    }
    if (dto.getName() != null) {
      server.setName(dto.getName());
    }
    if (dto.getDescription() != null) {
      server.setDescription(dto.getDescription());
    }
    if (dto.getType() != null) {
      server.setType(dto.getType());
    }
    if (dto.getIcon() != null) {
      server.setIcon(dto.getIcon());
    }
    if (dto.getUrl() != null) {
      server.setUrl(dto.getUrl());
    }
    if (dto.getIsCustomServer() != null) {
      server.setIsCustomServer(dto.getIsCustomServer());
    }
    if (dto.getCommandToRun() != null) {
      server.setCommandToRun(dto.getCommandToRun());
    }

    server.setModifiedTime(new Date());

    if (server.getCreatedTime() == null) {
      server.setCreatedTime(new Date());
    }

    log.debug("Updated McpServer entity with new values: {}", dto.getName());
    return server;
  }
}
