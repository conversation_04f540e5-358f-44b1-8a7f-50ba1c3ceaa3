/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.model.Tool;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.ai.tool.metadata.ToolMetadata;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;

/**
 * A registrar for dynamically managing Spring beans at runtime. This class provides functionality
 * to register, re-register, and unregister beans in the Spring application context dynamically
 * using bytecode.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DynamicBeanRegistrar {

  private static final Logger log = LoggerFactory.getLogger(DynamicBeanRegistrar.class);
  private final ApplicationContext applicationContext;

  /**
   * Constructs a new DynamicBeanRegistrar with the specified ApplicationContext.
   *
   * @param applicationContext the Spring ApplicationContext to use for bean registration
   */
  public DynamicBeanRegistrar(ApplicationContext applicationContext) {
    this.applicationContext = applicationContext;
  }

  public List<ToolCallback> registerToolCallbacks(List<Tool> tools) {
    List<ToolCallback> functionToolCallbacks = new ArrayList<>();
    for (Tool tool : tools) {
      try {
        String beanName = tool.getToolName();
        boolean containsBean = applicationContext.containsBean(beanName);
        if (containsBean) {
          log.info("Bean already exists with name: {}. Unregistering existing bean...", beanName);
          unregisterBean(beanName);
        }
        // Load the class dynamically
        DynamicClassLoader classLoader = new DynamicClassLoader(tool.getByteCodeMap());
        String className = tool.getClassName();
        Class<?> clazz = classLoader.loadClass(className);
        Class<?> req = classLoader.loadClass(className + "$Request");

        // Register the bean
        DefaultListableBeanFactory beanFactory =
            (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
        BeanDefinitionBuilder beanDefinitionBuilder =
            BeanDefinitionBuilder.genericBeanDefinition(clazz);
        beanFactory.registerBeanDefinition(beanName, beanDefinitionBuilder.getBeanDefinition());
        log.info("successfully registered bean with name : {}", beanName);

        Object object = applicationContext.getBean(beanName);
        beanFactory.autowireBean(object);
        boolean returnDirect = tool.getReturnDirect() != null && tool.getReturnDirect();

        if (object instanceof Function<?, ?> function) {
          log.info("bean : {} is a function", beanName);
          ToolCallback toolCallback =
              FunctionToolCallback.builder(tool.getToolName(), function)
                  .toolMetadata(ToolMetadata.builder().returnDirect(returnDirect).build())
                  .description(tool.getDescription())
                  .inputType(req)
                  .build();
          functionToolCallbacks.add(toolCallback);
        } else if (object instanceof BiFunction<?, ?, ?>) {
          log.info("bean : {} is a bi-function", beanName);
          @SuppressWarnings("unchecked")
          BiFunction<?, ToolContext, ?> biFunction = (BiFunction<?, ToolContext, ?>) object;
          ToolCallback toolCallback =
              FunctionToolCallback.builder(tool.getToolName(), biFunction)
                  .toolMetadata(ToolMetadata.builder().returnDirect(returnDirect).build())
                  .description(tool.getDescription())
                  .inputType(req)
                  .build();
          functionToolCallbacks.add(toolCallback);
        }
      } catch (Exception e) {
        log.error("error registering bean for tool : {}", tool.getToolName(), e);
      }
    }
    return functionToolCallbacks;
  }

  /**
   * Unregisters a bean from the Spring context.
   *
   * @param beanName the name of the bean to unregister
   */
  public void unregisterBean(String beanName) {
    DefaultListableBeanFactory beanFactory =
        (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
    if (beanFactory.containsBeanDefinition(beanName)) {
      beanFactory.removeBeanDefinition(beanName);
      log.debug("Successfully unregistered bean: {}", beanName);
    } else {
      log.warn("Cannot unregister bean: {}. Bean definition not found.", beanName);
    }
  }
}
