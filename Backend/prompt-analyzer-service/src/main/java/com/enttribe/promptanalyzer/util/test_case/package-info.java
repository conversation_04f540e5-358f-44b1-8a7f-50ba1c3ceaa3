/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Test Case Utility package for the Prompt Analyzer application.
 *
 * <p>This package contains utility classes for test case management, dynamic class loading,
 * and related operations in the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Dynamic bean registration and class loading</li>
 *   <li>Test case data management utilities</li>
 *   <li>Support for test case automation and evaluation</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Test case utility classes are used by services and test suites to manage and automate
 * test case operations.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.util.test_case;
