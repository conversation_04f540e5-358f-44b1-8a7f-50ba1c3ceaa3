/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import static org.hibernate.validator.internal.util.Contracts.assertTrue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.model.AssertionResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ToolCallAssertions {

  private static final ObjectMapper objectMapper = new ObjectMapper();
  private static final Logger log = LoggerFactory.getLogger(ToolCallAssertions.class);
  private static final String AND_EXPECTED_VALUE = " and expected value : '";
  private static final String ASSERTION_PASSED = "assertion passed";
  private static final String ARGUMENTS = "arguments";
  private static final double PERCENTAGE_MULTIPLIER = 100.0;

  private ToolCallAssertions() {
    throw new IllegalStateException("utility claas can not be instantiated");
  }

  /**
   * Asserts that the tool name matches the expected name.
   *
   * @param jsonString the JSON string
   * @param expectedName the expected tool name
   */
  public static AssertionResult assertToolName(
      String jsonString, String expectedName, String userQuestion) {
    String actualName = "";
    try {
      actualName = getToolName(jsonString);
      assertEquals("Tool name does not match", expectedName, actualName);
      return AssertionResult.builder()
          .actual(actualName)
          .expected(expectedName)
          .isPassed(true)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_CALLED.toString())
          .matchStrategy(MatchStrategy.EXACT.toString())
          .comment(ASSERTION_PASSED)
          .accuracyScore(PERCENTAGE_MULTIPLIER)
          .createdTime(new Date())
          .build();
    } catch (AssertionError | Exception e) {
      log.error("error in assertToolName : {}", e.getMessage());
      return AssertionResult.builder()
          .actual(actualName)
          .expected(expectedName)
          .isPassed(false)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_CALLED.toString())
          .matchStrategy(MatchStrategy.EXACT.toString())
          .comment(e.getMessage())
          .accuracyScore(0.0)
          .createdTime(new Date())
          .build();
    }
  }

  public static String getToolName(String jsonString) {
    try {
      JsonNode root = objectMapper.readTree(jsonString);
      if (jsonString.startsWith("{")) {
        return root.get("name").asText();
      }
      return root.get(0).get("name").asText();
    } catch (Exception e) {
      log.error("error in getToolName for json : {}: {}", jsonString, e.getMessage());
      return "";
    }
  }

  /**
   * Asserts a specific value inside a nested JSON string using JSON Pointer and match
   * matchStrategy.
   *
   * @param jsonString the JSON string
   * @param jsonPointer the JSON Pointer path (e.g., "/filters/0/conditions/0/parameterValue")
   * @param expectedValue the expected value or pattern
   * @param matchStrategy the match matchStrategy (EXACT, CONTAINS, REGEX, SUBSET)
   */
  public static AssertionResult assertJsonArgument(
      String jsonString,
      String jsonPointer,
      String expectedValue,
      MatchStrategy matchStrategy,
      String userQuestion) {
    jsonPointer = "/" + jsonPointer;
    String rawActualValue = "";
    try {
      JsonNode jsonNode = objectMapper.readTree(jsonString);
      String arguments = jsonNode.get(0).get(ARGUMENTS).asText();
      JsonNode root = objectMapper.readTree(arguments);
      JsonNode node = root.at(jsonPointer);

      assertFalse("Path not found: " + jsonPointer, node.isMissingNode());
      String actualValue = node.toString();
      rawActualValue = node.isTextual() ? node.asText() : actualValue;

      switch (matchStrategy) {
        case EXACT -> assertEquals(
            "EXACT match failed at " + jsonPointer + "\n", expectedValue, rawActualValue);
        case CONTAINS -> assertTrue(
            rawActualValue.contains(expectedValue),
            "CONTAINS match failed at "
                + jsonPointer
                + ". Actual value : '"
                + rawActualValue
                + AND_EXPECTED_VALUE
                + expectedValue
                + "'\n");
        case REGEX -> assertTrue(
            rawActualValue.matches(expectedValue),
            "REGEX match failed at "
                + jsonPointer
                + ". Actual value is : '"
                + rawActualValue
                + AND_EXPECTED_VALUE
                + expectedValue
                + "'\n");
        default -> throw new UnsupportedOperationException(
            "Unsupported match matchStrategy: " + matchStrategy);
      }
      return AssertionResult.builder()
          .actual(rawActualValue)
          .expected(expectedValue)
          .parameterName(jsonPointer.substring(1))
          .isPassed(true)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_PARAMETERS.toString())
          .matchStrategy(matchStrategy.toString())
          .comment(ASSERTION_PASSED)
          .accuracyScore(PERCENTAGE_MULTIPLIER)
          .createdTime(new Date())
          .build();
    } catch (AssertionError | Exception e) {
      log.error("error in assertJsonArgument : {}", e.getMessage());
      return AssertionResult.builder()
          .actual(rawActualValue)
          .expected(expectedValue)
          .parameterName(jsonPointer.substring(1))
          .isPassed(false)
          .userQuestion(userQuestion)
          .assertionType(AssertionType.TOOL_PARAMETERS.toString())
          .matchStrategy(matchStrategy.toString())
          .comment(e.getMessage())
          .accuracyScore(0.0)
          .createdTime(new Date())
          .build();
    }
  }

  public static boolean isToolInvoked(String responseJson) {
    try {
      JsonNode root = objectMapper.readTree(responseJson);
      if (root.isArray() && root.size() > 0) {
        for (JsonNode node : root) {
          boolean isFunction = "function".equalsIgnoreCase(node.path("type").asText());
          boolean hasName = node.hasNonNull("name") && !node.get("name").asText().isEmpty();
          boolean hasArguments =
              node.hasNonNull(ARGUMENTS) && !node.get(ARGUMENTS).asText().isEmpty();
          if (isFunction && hasName && hasArguments) {
            return true;
          }
        }
      }
    } catch (Exception e) {
      log.error("error in isToolInvoked. response : {}", responseJson);
    }
    return false;
  }
}
