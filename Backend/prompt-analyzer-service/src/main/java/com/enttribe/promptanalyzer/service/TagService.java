/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import java.util.List;
import java.util.Map;

/**
 * Service interface for managing Tag operations. This interface defines the contract for handling
 * tag-related business operations such as searching, creating, updating, and deleting tags within
 * the application.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TagService {

  /**
   * Searches for tags based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of tag response DTOs
   */
  List<TagResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of tags matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching tags
   */
  Long count(String filter);

  /**
   * Saves a new tag.
   *
   * @param dto the tag request DTO
   * @return a map containing the result of the save operation
   */
  Map<String, String> save(TagRequestDto dto);

  /**
   * Soft deletes a tag by its integer ID.
   *
   * @param id the tag ID
   * @return a map containing the result of the delete operation
   */
  Map<String, String> softDelete(String id);

  /**
   * Updates an existing tag.
   *
   * @param dto the tag request DTO
   * @return a map containing the result of the update operation
   */
  Map<String, String> update(TagRequestDto dto);
}
