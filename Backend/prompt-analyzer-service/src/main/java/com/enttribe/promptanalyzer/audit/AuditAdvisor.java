/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.audit;

import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.AuditService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.CallAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAdvisorChain;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.metadata.Usage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AuditAdvisor implements CallAdvisor {

  public static final int ORDER = 999;

  private static final String APPLICATION_NAME = "PROMPT_ANALYZER_APP_NAME";
  private static final String STATUS_SUCCESS = "success";
  private static final String STATUS_FAILED = "failed";

  @Value("${prompt.audit.enable:false}")
  private boolean promptAuditEnabled;

  private final AuditService auditService;

  public AuditAdvisor(AuditService auditService) {
    this.auditService = auditService;
  }

  @Override
  public ChatClientResponse adviseCall(
      ChatClientRequest chatClientRequest, CallAdvisorChain callAdvisorChain) {
    List<Message> messages = new ArrayList<>(chatClientRequest.prompt().getInstructions());
    if (messages.isEmpty()) {
      log.info("adding system message for audit");
      String systemText = chatClientRequest.prompt().getSystemMessage().getText();
      if (!systemText.isEmpty()) {
        messages.add(new SystemMessage(systemText));
      }
    }
    if (!chatClientRequest.prompt().getUserMessage().getText().isEmpty()) {
      UserMessage userMessage =
          new UserMessage(chatClientRequest.prompt().getUserMessage().getText());
      messages.add(userMessage);
    }

    String auditId = UUID.randomUUID().toString();
    OpenAiChatOptions options = (OpenAiChatOptions) chatClientRequest.prompt().getOptions();
    if (options != null && options.getToolContext() != null) {
      auditId = (String) options.getToolContext().get("auditId");
    }
    ChatClientResponse advisedResponse = null;
    try {
      Instant startTime = Instant.now();
      advisedResponse = callAdvisorChain.nextCall(chatClientRequest);
      Instant endTime = Instant.now();
      Long timeTakenMillis = Duration.between(startTime, endTime).toMillis();
      ChatResponse chatResponse = advisedResponse.chatResponse();
      doSuccessPromptAudit(chatClientRequest, timeTakenMillis, chatResponse, messages, auditId);
      return advisedResponse;
    } catch (Exception e) {
      log.error("error while executing prompt", e);
      doFailedPromptAudit(chatClientRequest, messages, e.getMessage(), auditId);
      throw new BusinessException(
          "error while executing prompt : " + chatClientRequest + e.getMessage());
    }
  }

  @Override
  public String getName() {
    return "AuditAdvisor";
  }

  @Override
  public int getOrder() {
    return ORDER;
  }

  private void doSuccessPromptAudit(
      ChatClientRequest advisedRequest,
      Long timeTakenMillis,
      ChatResponse chatResponse,
      List<Message> messages,
      String auditId) {
    if (promptAuditEnabled) {
      // Audit related code
      Usage usage = chatResponse.getMetadata().getUsage();

      PromptAuditDto promptAudit =
          PromptAuditDto.builder()
              .applicationName(APPLICATION_NAME)
              .chatOptions(
                  advisedRequest.prompt().getOptions() != null
                      ? Map.of("chatOptions", advisedRequest.prompt().getOptions())
                      : Map.of())
              .auditId(AuditUtils.generateAuditId(auditId))
              .model("llama-3.3-70b-versatile")
              .responseText(chatResponse.getResult().getOutput().getText())
              .totalToken(usage.getTotalTokens().longValue())
              .promptToken(usage.getPromptTokens().longValue())
              .generationTokens(usage.getCompletionTokens().longValue())
              .responseTime(timeTakenMillis.doubleValue())
              .creationTime(new Date())
              .requestText(JsonUtils.convertToJSON(messages))
              .status(STATUS_SUCCESS)
              .build();

      auditService.savePromptAudit(promptAudit);
    }
  }

  private void doFailedPromptAudit(
      ChatClientRequest advisedRequest,
      List<Message> messages,
      String errorMessage,
      String auditId) {
    if (promptAuditEnabled) {
      // Audit related code

      ChatOptions chatOptions = advisedRequest.prompt().getOptions();

      PromptAuditDto promptAudit =
          PromptAuditDto.builder()
              .applicationName(APPLICATION_NAME)
              .chatOptions(
                  chatOptions != null ? Map.of("chatOptions", chatOptions) : Collections.emptyMap())
              .auditId(AuditUtils.generateAuditId(auditId))
              .model(chatOptions != null ? chatOptions.getModel() : null)
              .creationTime(new Date())
              .requestText(JsonUtils.convertToJSON(messages))
              .httpStatus(extractStatusCode(errorMessage))
              .status(STATUS_FAILED)
              .errorMessage(errorMessage)
              .build();

      auditService.savePromptAudit(promptAudit);
    }
  }

  private static Integer extractStatusCode(String errorMessage) {
    try {
      Pattern pattern = Pattern.compile("^(\\d{3})");
      Matcher matcher = pattern.matcher(errorMessage);

      if (matcher.find()) {
        return Integer.parseInt(matcher.group(1)); // Extracts the first 3-digit number
      }
    } catch (Exception e) {
      log.error("error in extractStatusCode : {}", e.getMessage());
    }
    return null; // Return null if no status code is found
  }
}
