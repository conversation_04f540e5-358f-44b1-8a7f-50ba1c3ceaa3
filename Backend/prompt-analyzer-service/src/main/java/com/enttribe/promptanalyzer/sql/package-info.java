/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * SQL package for the Prompt Analyzer application.
 *
 * <p>This package contains classes and utilities for custom SQL filtering and related
 * database operations in the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Custom SQL filter implementations</li>
 *   <li>Support for advanced database queries</li>
 *   <li>Facilitation of data access and filtering</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>SQL-related classes are used by DAOs and services to perform advanced filtering
 * and database operations.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.sql;
