/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Data Access Object interface for KnowledgeBase entity operations. Extends JpaRepository to
 * inherit standard database operations.
 *
 * <AUTHOR>
 * @version 1.0
 * @see KnowledgeBase
 * @see JpaRepository
 */
public interface KnowledgeBaseDao extends JpaRepository<KnowledgeBase, Integer> {

  /**
   * Retrieves a list of KnowledgeBase entities of type 'WEBSITE' that are currently in the
   * 'PROCESSING' status.
   *
   * @return a list of unprocessed KnowledgeBase entities of type 'WEBSITE'
   */
  @Query(
      "SELECT kb FROM KnowledgeBase kb WHERE kb.type = 'WEBSITE' AND kb.websiteTaskStatus = 'PROCESSING'")
  List<KnowledgeBase> getWebsiteTypeUnprocessedKB();

  /**
   * Checks if a KnowledgeBase entity with the specified website URL exists and is not marked as
   * deleted.
   *
   * @param webSiteUrl the website URL to check
   * @return a list of KnowledgeBase entities matching the specified URL
   */
  @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.webSiteUrl = :webSiteUrl AND kb.deleted = false")
  List<KnowledgeBase> existsWebsiteUrl(String webSiteUrl);

  @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.name = :knowledgeBaseName")
  KnowledgeBase findByName(String knowledgeBaseName);

  @Query("SELECT k FROM KnowledgeBase k WHERE k.nanoId = :nanoId AND k.deleted = false")
  KnowledgeBase findKnowledgeBaseByNanoId(@Param("nanoId") String nanoId);

  @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.nanoId IS NULL")
  List<KnowledgeBase> findFirstBatchWithNullNanoId(Pageable pageable);

  @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.nanoId  =:nanoId AND kb.deleted = false")
  Optional<KnowledgeBase> findByNanoId(String nanoId);

  @Query("SELECT kb FROM KnowledgeBase kb WHERE kb.nanoId IN :nanoIds AND kb.deleted = false")
  List<KnowledgeBase> findAllByNanoIds(List<String> nanoIds);
}
