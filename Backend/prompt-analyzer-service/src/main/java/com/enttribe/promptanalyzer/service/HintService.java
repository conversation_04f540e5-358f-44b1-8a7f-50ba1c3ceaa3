/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import java.util.List;
import java.util.Map;

public interface HintService {

  /**
   * Searches for plans in batch for a list of queries, a type, and an entity type.
   *
   * @param queries the list of queries
   * @param type the type of plan
   * @param entityType the entity type
   * @return a list of maps containing search results for each query
   */
  List<Map<String, Object>> searchPlanBatch(List<String> queries, String type, String entityType);
}
