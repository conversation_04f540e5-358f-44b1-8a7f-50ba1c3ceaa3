/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.rest.TagRest;
import com.enttribe.promptanalyzer.service.TagService;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST controller implementation for tag-related operations. Handles HTTP requests for searching,
 * counting, saving, updating, and deleting tags.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/tag")
@RequiredArgsConstructor
public class TagRestImpl implements TagRest {

  private final TagService tagService;

  /**
   * Searches for tags based on specified criteria.
   *
   * @param filter Optional search filter
   * @param offset Pagination offset
   * @param size Number of items per page
   * @param orderBy Field to order results by
   * @param orderType Order direction (e.g., "ASC" or "DESC")
   * @return List of tags matching the search criteria
   */
  @Override
  public List<TagResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching tags");
    return tagService.search(filter, offset, size, orderBy, orderType);
  }

  /**
   * Counts the total number of tags matching the filter criteria.
   *
   * @param filter Optional search filter
   * @return The total count of matching tags
   */
  @Override
  public Long count(String filter) {
    return tagService.count(filter);
  }

  /**
   * Saves a new tag.
   *
   * @param dto The tag data to be saved
   * @return A map containing the result of the save operation
   */
  @Override
  public Map<String, String> save(TagRequestDto dto) {
    log.info("Saving tag");
    return tagService.save(dto);
  }

  /**
   * Performs a soft delete of a tag by its ID.
   *
   * @param id The ID of the tag to delete
   * @return A map containing the result of the delete operation
   */
  @Override
  public Map<String, String> deleteById(String id) {
    log.info("Performing soft delete for tag with ID: {}", id);
    return tagService.softDelete(id);
  }

  /**
   * Updates an existing tag.
   *
   * @param dto The updated tag data
   * @return A map containing the result of the update operation
   */
  @Override
  public Map<String, String> update(TagRequestDto dto) {
    log.info("Updating tag with ID:{}", dto.getId());
    return tagService.update(dto);
  }
}
