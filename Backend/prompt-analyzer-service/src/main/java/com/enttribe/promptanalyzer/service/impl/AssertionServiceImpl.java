/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dto.assertion.AssertionResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Assertion;
import com.enttribe.promptanalyzer.service.AssertionService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.AssertionUtil;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service implementation for managing Assertion entities.
 *
 * <p>Provides business logic for creating, updating, retrieving, deleting, searching, and counting
 * assertions. Handles conversion between Assertion and AssertionDto, and applies exception handling
 * and logging.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AssertionServiceImpl implements AssertionService {

  private final CustomFilter customFilter;

  /**
   * Searches for assertions with optional filtering, pagination, and sorting.
   *
   * @param filter the filter string (optional)
   * @param offset the starting index for pagination (required)
   * @param size the number of results to return (required)
   * @param orderBy the field to order by (optional)
   * @param orderType the order direction (asc/desc, optional)
   * @return a list of response DTOs containing assertions and pagination information
   */
  @Override
  public List<AssertionResponseDto> searchAssertion(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug(
        "Inside @method searchAssertion with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
        filter,
        offset,
        size,
        orderBy,
        orderType);
    try {
      List<Assertion> assertionEntities =
          customFilter.searchByFilter(Assertion.class, filter, orderBy, orderType, offset, size);

      List<AssertionResponseDto> assertionResponseDtos =
          assertionEntities.stream().map(AssertionUtil::convertToAssertionDto).toList();
      log.debug("Successfully retrieved {} assertions", assertionResponseDtos.size());
      return assertionResponseDtos;
    } catch (Exception e) {
      log.error("Error in @method searchAssertion : {}", e.getMessage(), e);
      throw new BusinessException("Failed to search assertions", e);
    }
  }
}
