/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import java.net.URI;
import javax.tools.SimpleJavaFileObject;

/**
 * A Java file object that holds source code in memory. This class extends SimpleJavaFileObject to
 * provide a way to compile Java source code from a string.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class InMemoryJavaFile extends SimpleJavaFileObject {
  private final String sourceCode;

  /**
   * Constructs an InMemoryJavaFile with the specified class name and source code.
   *
   * @param className the name of the class
   * @param sourceCode the source code of the class
   */
  public InMemoryJavaFile(String className, String sourceCode) {
    super(
        URI.create("string:///" + className.replace('.', '/') + Kind.SOURCE.extension),
        Kind.SOURCE);
    this.sourceCode = sourceCode;
  }

  /**
   * Returns the source code content of this file object.
   *
   * @param ignoreEncodingErrors whether to ignore encoding errors
   * @return the source code as a CharSequence
   */
  @Override
  public CharSequence getCharContent(boolean ignoreEncodingErrors) {
    return sourceCode;
  }
}
