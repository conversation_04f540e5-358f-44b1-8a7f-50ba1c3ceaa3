package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.TemplateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.Resource;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class CustomAgentHelperService {

    private static final double SIMILARITY_THRESHOLD = 0.4;
    private static final int DOCUMENT_COUNT_TO_RETRIEVE = 3;
    private static final double TEMPERATURE = 0.2;
    private static final String FORMAT = "format";
    private static final String CUSTOM_AGENT_FILTER = KnowledgeBaseServiceImpl.CUSTOM_AGENT_FILTER;

    @Value("classpath:template/agent/improve_query_prompt.st")
    private Resource improveQueryPrompt;

    @Value("classpath:template/agent/populate_processor.st")
    private Resource populateProcessorsPrompt;

    private final VectorStore vectorStore;

    @Retryable(
            retryFor = Exception.class,
            maxAttempts = 2,
            backoff = @Backoff(delay = 200, multiplier = 1.2)
    )
    public CustomAgentServiceImpl.PopulatedProcessors populateProcessors(
            String userQuery, ChatClient chatClient, String identifiedProcessors, String currentPlan) {
        try {
            log.debug("Populating processors for query: {}", userQuery);
            if (identifiedProcessors.isEmpty() || identifiedProcessors.equals("[]")) {
                log.info("No processors identified for query");
                return new CustomAgentServiceImpl.PopulatedProcessors(
                        List.of(), "Unfortunately, we couldn't find a processor that matches your request.");
            }
            Map<String, Object> variableMap = getVariableMap(identifiedProcessors, currentPlan);
            String resolvedPrompt =
                    TemplateUtils.getResolvedPrompt(populateProcessorsPrompt, variableMap);

            String plan = chatClient.prompt().system(resolvedPrompt).user(userQuery).call().content();

            CustomAgentServiceImpl.PopulatedProcessors populatedProcessors =
                    JsonUtils.convertJsonToObject(plan, CustomAgentServiceImpl.PopulatedProcessors.class);
            log.debug("Successfully populated processors: {}", plan);
            return populatedProcessors;
        } catch (Exception e) {
            log.error("Error populating processors: {}", e.getMessage(), e);
            return null;
        }
    }

    @NotNull
    private static Map<String, Object> getVariableMap(String identifiedProcessors, String currentPlan) {
        BeanOutputConverter<CustomAgentServiceImpl.PopulatedProcessors> converter =
                new BeanOutputConverter<>(CustomAgentServiceImpl.PopulatedProcessors.class);
        return Map.of(
                "processors",
                identifiedProcessors,
                FORMAT,
                converter.getFormat(),
                "lastFlow",
                currentPlan);
    }

    @Retryable(
            retryFor = Exception.class,
            maxAttempts = 5,
            backoff = @Backoff(delay = 200, multiplier = 1.2)
    )
    public List<String> getRelevantContext(String userQuery, ChatClient chatClient) {
        log.debug("Getting relevant context for query: {}", userQuery);
        List<Document> documents = new ArrayList<>();
        List<String> queries = improveQuery(userQuery, chatClient);

        for (String query : queries) {
            SearchRequest searchRequest =
                    SearchRequest.builder()
                            .similarityThreshold(SIMILARITY_THRESHOLD)
                            .filterExpression(String.format("custom_agent_id == '%s'", CUSTOM_AGENT_FILTER))
                            .topK(DOCUMENT_COUNT_TO_RETRIEVE)
                            .query(query)
                            .build();

            List<Document> documentList = vectorStore.similaritySearch(searchRequest);
            documents.addAll(documentList);
        }
        log.debug("Found {} relevant documents", documents.size());
        return documents.stream().map(Document::getText).toList();
    }

    @Retryable(
            retryFor = Exception.class,
            maxAttempts = 2,
            backoff = @Backoff(delay = 200, multiplier = 1.2)
    )
    public List<String> improveQuery(String userQuery, ChatClient chatClient) {
        try {
            log.debug("Improving query: {}", userQuery);
            String systemPrompt =
                    TemplateUtils.getResolvedPrompt(improveQueryPrompt, Map.of("query", userQuery));

            OpenAiChatOptions options = new OpenAiChatOptions();
            options.setModel("llama-3.3-70b-versatile");
            options.setTemperature(TEMPERATURE);

            List<String> queries =
                    chatClient
                            .prompt()
                            .system(systemPrompt)
                            .options(options)
                            .user(userQuery)
                            .call()
                            .entity(new ParameterizedTypeReference<List<String>>() {});

            log.debug("Successfully improved query, generated {} variations", queries.size());
            return queries;
        } catch (Exception e) {
            log.error("Error improving query: {}", e.getMessage(), e);
            return List.of();
        }
    }

}
