/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dto.result.AssertionResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestCaseResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestResultResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.AssertionResult;
import com.enttribe.promptanalyzer.model.TestCaseResult;
import com.enttribe.promptanalyzer.model.TestResult;
import com.enttribe.promptanalyzer.service.ResultService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.test_case.ResultUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ResultServiceImpl implements ResultService {

  private final CustomFilter customFilter;

  // AssertionResult
  @Override
  public List<AssertionResultResponseDto> searchAssertionResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Inside @method searchAssertionResults");
    try {
      List<AssertionResult> results =
          customFilter.searchByFilter(
              AssertionResult.class, filter, orderBy, orderType, offset, size);
      return results.stream().map(ResultUtils::toAssertionResultResponseDto).toList();
    } catch (Exception e) {
      log.error("Failed to search assertion results {} ", e.getMessage(), e);
      throw new BusinessException("Failed to search assertion results", e);
    }
  }

  @Override
  public Long countAssertionResults(String filter) {
    log.debug("Inside @method countAssertionResults");
    try {
      return customFilter.countByFilter(AssertionResult.class, filter);
    } catch (Exception e) {
      log.error("Failed to count assertion results {} ", e.getMessage(), e);
      throw new BusinessException("Failed to count assertion results", e);
    }
  }

  // TestCaseResult
  @Override
  public List<TestCaseResultResponseDto> searchTestCaseResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Inside @method searchTestCaseResults");
    try {
      List<TestCaseResult> results =
          customFilter.searchByFilter(
              TestCaseResult.class, filter, orderBy, orderType, offset, size);
      return results.stream().map(ResultUtils::toTestCaseResultResponseDto).toList();
    } catch (Exception e) {
      log.error("Failed to search test case results {} ", e.getMessage(), e);
      throw new BusinessException("Failed to search test case results", e);
    }
  }

  @Override
  public Long countTestCaseResults(String filter) {
    log.debug("Inside @method countTestCaseResults");
    try {
      return customFilter.countByFilter(TestCaseResult.class, filter);
    } catch (Exception e) {
      log.error("Failed to count test case results {} ", e.getMessage(), e);
      throw new BusinessException("Failed to count test case results", e);
    }
  }

  // TestResult
  @Override
  public List<TestResultResponseDto> searchTestResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Inside @method searchTestResults");
    try {
      List<TestResult> results =
          customFilter.searchByFilter(TestResult.class, filter, orderBy, orderType, offset, size);
      return results.stream().map(ResultUtils::toTestResultResponseDto).toList();
    } catch (Exception e) {
      log.error("Failed to search test results {} ", e.getMessage(), e);
      throw new BusinessException("Failed to search test results", e);
    }
  }

  @Override
  public Long countTestResults(String filter) {
    log.debug("Inside @method countTestResults");
    try {
      return customFilter.countByFilter(TestResult.class, filter);
    } catch (Exception e) {
      log.error("Failed to count test results {} ", e.getMessage(), e);
      throw new BusinessException("Failed to count test results", e);
    }
  }
}
