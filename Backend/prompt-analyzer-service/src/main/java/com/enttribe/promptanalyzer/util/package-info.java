/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Utility package for the Prompt Analyzer application.
 *
 * <p>This package contains utility and helper classes that provide common functionality
 * and reusable code for the Prompt Analyzer application. Utilities support various
 * operations such as data conversion, formatting, and other cross-cutting concerns.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Reusable utility methods and classes</li>
 *   <li>Support for data conversion and formatting</li>
 *   <li>Common helpers for application-wide use</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Utility classes are used throughout the application to avoid code duplication and
 * to provide standardized solutions for common programming tasks.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.util;
