/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import java.util.List;

/**
 * Manages query operations and search functionality. This service handles the creation, searching,
 * and management of queries, providing a centralized interface for query-related operations with
 * pagination and filtering support.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface QueryService {

  /**
   * Searches for queries based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of query response DTOs
   */
  List<QueryResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of queries matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching queries
   */
  Long count(String filter);
}
