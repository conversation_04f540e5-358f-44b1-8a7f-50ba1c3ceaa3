/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.rest.DataMigrationRest;
import com.enttribe.promptanalyzer.service.NanoIdMigrationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/nano")
public class DataMigrationRestImpl implements DataMigrationRest {

  private final NanoIdMigrationService nanoIdMigrationService;

  @GetMapping("/prompt")
  @Override
  public ResponseEntity<String> migrateNanoIdsForPrompt() {
    nanoIdMigrationService.migrateAllPrompt();
    return ResponseEntity.ok("Migration completed for prompt.");
  }

  @GetMapping("/knowledgeBase")
  @Override
  public ResponseEntity<String> migrateNanoIdsForKnowledgeBase() {
    nanoIdMigrationService.migrateAllKnowledgeBase();
    return ResponseEntity.ok("Migration completed for knowledge base.");
  }

  @GetMapping("/mcp-server")
  @Override
  public ResponseEntity<String> migrateNanoIdsForMcpServer() {
    nanoIdMigrationService.migrateAllMcpServer();
    return ResponseEntity.ok("Migration completed for MCP server.");
  }

  @GetMapping("/tool")
  @Override
  public ResponseEntity<String> migrateNanoIdsForTool() {
    nanoIdMigrationService.migrateAllTool();
    return ResponseEntity.ok("Migration completed for tool.");
  }

  @GetMapping("/testcase")
  @Override
  public ResponseEntity<String> migrateNanoIdsForTestCase() {
    nanoIdMigrationService.migrateAllTestCase();
    return ResponseEntity.ok("Migration completed for test case.");
  }

  @GetMapping("/tag")
  @Override
  public ResponseEntity<String> migrateNanoIdsForTag() {
    nanoIdMigrationService.migrateAllTag();
    return ResponseEntity.ok("Migration completed for tag.");
  }
}
