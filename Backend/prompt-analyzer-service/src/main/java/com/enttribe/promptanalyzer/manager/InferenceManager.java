/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.manager;

import com.enttribe.commons.encoder.AESUtils;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.service.LlmModelService;
import io.micrometer.observation.ObservationRegistry;
import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.observation.ChatModelObservationConvention;
import org.springframework.ai.model.SimpleApiKey;
import org.springframework.ai.model.openai.autoconfigure.OpenAIAutoConfigurationUtil;
import org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties;
import org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties;
import org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties;
import org.springframework.ai.model.openai.autoconfigure.OpenAiParentProperties;
import org.springframework.ai.model.tool.DefaultToolExecutionEligibilityPredicate;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.model.tool.ToolExecutionEligibilityPredicate;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * Manager class responsible for handling inference operations. Initializes and manages OpenAI API
 * instances for different providers.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class InferenceManager {

  private static final Logger log = LoggerFactory.getLogger(InferenceManager.class);
  private static final Map<String, OpenAiApi> openAiApiMap = new HashMap<>();
  private final LlmModelService llmModelService;

  /** Thread-safe map storing ChatModel instances keyed by provider name */
  private static final Map<String, ChatModel> chatModelMap = new HashMap<>();

  private final ToolCallingManager toolCallingManager;
  private final RetryTemplate retryTemplate;
  private final ResponseErrorHandler responseErrorHandler;
  private final OpenAiChatProperties chatProperties;
  private final OpenAiConnectionProperties commonProperties;
  private final ObjectProvider<RestClient.Builder> restClientBuilderProvider;
  private final ObjectProvider<WebClient.Builder> webClientBuilderProvider;
  private final ObjectProvider<ObservationRegistry> observationRegistry;
  private final ObjectProvider<ChatModelObservationConvention> chatModelObservationConventions;
  private final ObjectProvider<ToolExecutionEligibilityPredicate>
      openAiToolExecutionEligibilityPredicate;

  /**
   * Constructs a new InferenceManager with all required dependencies.
   *
   * @param toolCallingManager the tool calling manager
   * @param retryTemplate Template for retry operations
   * @param responseErrorHandler Handler for API response errors
   * @param chatProperties OpenAI chat-specific properties
   * @param commonProperties Common OpenAI connection properties
   * @param restClientBuilderProvider Provider for RestClient builder
   * @param webClientBuilderProvider Provider for WebClient builder
   * @param observationRegistry Registry for observations/metrics
   * @param chatModelObservationConventions Conventions for chat model observations
   */
  public InferenceManager(
      LlmModelService llmModelService,
      ToolCallingManager toolCallingManager,
      RetryTemplate retryTemplate,
      ResponseErrorHandler responseErrorHandler,
      OpenAiChatProperties chatProperties,
      OpenAiConnectionProperties commonProperties,
      ObjectProvider<RestClient.Builder> restClientBuilderProvider,
      ObjectProvider<WebClient.Builder> webClientBuilderProvider,
      ObjectProvider<ObservationRegistry> observationRegistry,
      ObjectProvider<ChatModelObservationConvention> chatModelObservationConventions,
      ObjectProvider<ToolExecutionEligibilityPredicate> openAiToolExecutionEligibilityPredicate) {
    this.llmModelService = llmModelService;
    this.toolCallingManager = toolCallingManager;
    this.retryTemplate = retryTemplate;
    this.responseErrorHandler = responseErrorHandler;
    this.chatProperties = chatProperties;
    this.commonProperties = commonProperties;
    this.restClientBuilderProvider = restClientBuilderProvider;
    this.webClientBuilderProvider = webClientBuilderProvider;
    this.observationRegistry = observationRegistry;
    this.chatModelObservationConventions = chatModelObservationConventions;
    this.openAiToolExecutionEligibilityPredicate = openAiToolExecutionEligibilityPredicate;
  }

  /**
   * Retrieves the OpenAiApi instance for a given provider.
   *
   * @param provider the provider key
   * @return the OpenAiApi instance associated with the provider
   */
  public OpenAiApi getOpenAiApiByProvider(String provider) {
    log.debug("open api map keys -> {} and provider is : {}", openAiApiMap.keySet(), provider);
    OpenAiApi openAiApi = openAiApiMap.get(provider);
    Assert.notNull(openAiApi, "chat model is not found for the provider " + provider);
    return openAiApi;
  }

  public ChatModel getChatModelByProvider(String provider) {
    log.debug("chat model map keys : {} provider : {}", chatModelMap.keySet(), provider);
    return chatModelMap.get(provider);
  }

  /**
   * Initializes all chat and embedding models during application startup. This method is
   * automatically called after dependency injection is complete. It fetches model configurations
   * from the prompt API and creates corresponding model instances. Thread-safe initialization is
   * ensured through the use of volatile maps.
   */
  @PostConstruct
  public void initializeChatModels() throws Exception {

    log.info("chat models are (before) : {}", chatModelMap.keySet());
    // Fetch LLM models for the application
    List<LlmModelSdkDto> inferenceDetails = llmModelService.getUniqueInferencesByType("chat");
    log.info("total {} chat models are fetched", inferenceDetails.size());

    // Iterate through each inference
    for (LlmModelSdkDto inferenceDetail : inferenceDetails) {
      log.info("registering chat model for provider : {}", inferenceDetail.getProvider());
      String decryptedApiKey = AESUtils.decrypt(inferenceDetail.getApiKey());
      inferenceDetail.setApiKey(decryptedApiKey);
      try {
        // Create OpenAiChatModel instance
        OpenAiChatModel openAiChatModel =
            openAiChatModel(
                inferenceDetail,
                commonProperties,
                chatProperties,
                restClientBuilderProvider,
                webClientBuilderProvider,
                toolCallingManager,
                retryTemplate,
                responseErrorHandler,
                observationRegistry,
                chatModelObservationConventions,
                openAiToolExecutionEligibilityPredicate);

        // Populate the map
        String providerKey = inferenceDetail.getProvider();
        log.info("adding chat model for key {}", providerKey);
        chatModelMap.put(providerKey, openAiChatModel);
        log.info("chat model initialized for : {}", providerKey);
      } catch (Exception e) {
        log.error(
            "failed to initialize chat model for provider : {}", inferenceDetail.getProvider(), e);
      }
    }

    log.info("chat models are (after) : {}", chatModelMap.keySet());
  }

  public OpenAiChatModel openAiChatModel(
      LlmModelSdkDto inferenceDetail,
      OpenAiConnectionProperties commonProperties,
      OpenAiChatProperties chatProperties,
      ObjectProvider<RestClient.Builder> restClientBuilderProvider,
      ObjectProvider<WebClient.Builder> webClientBuilderProvider,
      ToolCallingManager toolCallingManager,
      RetryTemplate retryTemplate,
      ResponseErrorHandler responseErrorHandler,
      ObjectProvider<ObservationRegistry> observationRegistry,
      ObjectProvider<ChatModelObservationConvention> observationConvention,
      ObjectProvider<ToolExecutionEligibilityPredicate> openAiToolExecutionEligibilityPredicate) {

    chatProperties.setApiKey(inferenceDetail.getApiKey());
    chatProperties.setBaseUrl(inferenceDetail.getBaseUrl());

    var openAiApi =
        openAiApi(
            inferenceDetail,
            chatProperties,
            commonProperties,
            restClientBuilderProvider.getIfAvailable(RestClient::builder),
            webClientBuilderProvider.getIfAvailable(WebClient::builder),
            responseErrorHandler,
            "chat");

    openAiApiMap.put(inferenceDetail.getProvider(), openAiApi);

    var chatModel =
        OpenAiChatModel.builder()
            .openAiApi(openAiApi)
            .defaultOptions(chatProperties.getOptions())
            .toolCallingManager(toolCallingManager)
            .toolExecutionEligibilityPredicate(
                openAiToolExecutionEligibilityPredicate.getIfUnique(
                    DefaultToolExecutionEligibilityPredicate::new))
            .retryTemplate(retryTemplate)
            .observationRegistry(observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP))
            .build();

    observationConvention.ifAvailable(chatModel::setObservationConvention);

    return chatModel;
  }

  private OpenAiApi openAiApi(
      LlmModelSdkDto inferenceDetail,
      OpenAiChatProperties chatProperties,
      OpenAiConnectionProperties commonProperties,
      RestClient.Builder restClientBuilder,
      WebClient.Builder webClientBuilder,
      ResponseErrorHandler responseErrorHandler,
      String modelType) {

    OpenAIAutoConfigurationUtil.ResolvedConnectionProperties resolved =
        resolveConnectionProperties(commonProperties, chatProperties, modelType);

    if (inferenceDetail.getProvider().equals("azure")) {
      return OpenAiApi.builder()
          .baseUrl(inferenceDetail.getBaseUrl())
          .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
          .headers(resolved.headers())
          .completionsPath("")
          .embeddingsPath(OpenAiEmbeddingProperties.DEFAULT_EMBEDDINGS_PATH)
          .restClientBuilder(restClientBuilder)
          .webClientBuilder(webClientBuilder)
          .responseErrorHandler(responseErrorHandler)
          .build();
    } else if (inferenceDetail.getProvider().equals("google")) {
      return OpenAiApi.builder()
          .baseUrl(inferenceDetail.getBaseUrl())
          .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
          .headers(resolved.headers())
          .completionsPath("/v1beta/openai/chat/completions")
          .embeddingsPath(OpenAiEmbeddingProperties.DEFAULT_EMBEDDINGS_PATH)
          .restClientBuilder(restClientBuilder)
          .webClientBuilder(webClientBuilder)
          .responseErrorHandler(responseErrorHandler)
          .build();
    } else {
      return OpenAiApi.builder()
          .baseUrl(inferenceDetail.getBaseUrl())
          .apiKey(new SimpleApiKey(inferenceDetail.getApiKey()))
          .headers(resolved.headers())
          .completionsPath(chatProperties.getCompletionsPath())
          .embeddingsPath(OpenAiEmbeddingProperties.DEFAULT_EMBEDDINGS_PATH)
          .restClientBuilder(restClientBuilder)
          .webClientBuilder(webClientBuilder)
          .responseErrorHandler(responseErrorHandler)
          .build();
    }
  }

  protected static OpenAIAutoConfigurationUtil.ResolvedConnectionProperties
      resolveConnectionProperties(
          OpenAiParentProperties commonProperties,
          OpenAiParentProperties modelProperties,
          String modelType) {

    String baseUrl =
        StringUtils.hasText(modelProperties.getBaseUrl())
            ? modelProperties.getBaseUrl()
            : commonProperties.getBaseUrl();
    String apiKey =
        StringUtils.hasText(modelProperties.getApiKey())
            ? modelProperties.getApiKey()
            : commonProperties.getApiKey();
    String projectId =
        StringUtils.hasText(modelProperties.getProjectId())
            ? modelProperties.getProjectId()
            : commonProperties.getProjectId();
    String organizationId =
        StringUtils.hasText(modelProperties.getOrganizationId())
            ? modelProperties.getOrganizationId()
            : commonProperties.getOrganizationId();

    Map<String, List<String>> connectionHeaders = new HashMap<>();
    if (StringUtils.hasText(projectId)) {
      connectionHeaders.put("OpenAI-Project", List.of(projectId));
    }
    if (StringUtils.hasText(organizationId)) {
      connectionHeaders.put("OpenAI-Organization", List.of(organizationId));
    }

    Assert.hasText(
        baseUrl,
        "OpenAI base URL must be set.  Use the connection property: spring.ai.openai.base-url or spring.ai.openai."
            + modelType
            + ".base-url property.");
    Assert.hasText(
        apiKey,
        "OpenAI API key must be set. Use the connection property: spring.ai.openai.api-key or spring.ai.openai."
            + modelType
            + ".api-key property.");

    return new OpenAIAutoConfigurationUtil.ResolvedConnectionProperties(
        baseUrl, apiKey, CollectionUtils.toMultiValueMap(connectionHeaders));
  }

  public record ResolvedConnectionProperties(
      String baseUrl, String apiKey, MultiValueMap<String, String> headers) {}
}
