/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Tag;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Tag entity operations. Extends JpaRepository to provide standard
 * CRUD operations and pagination support for Tag entities.
 *
 * @see Tag
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TagDao extends JpaRepository<Tag, Integer> {

  @Query("SELECT m FROM Tag m WHERE m.nanoId = :nanoId")
  Optional<Tag> findByNanoId(@Param("nanoId") String nanoId);

  @Query("SELECT m FROM Tag m WHERE m.nanoId IS NULL")
  List<Tag> findFirstBatchWithNullNanoId(Pageable pageable);

  @Query("SELECT t FROM Tag t WHERE t.nanoId = :nanoId AND t.deleted = false")
  Optional<Tag> getByNanoId(String nanoId);
}
