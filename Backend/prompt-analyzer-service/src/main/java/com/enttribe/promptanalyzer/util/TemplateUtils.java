/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import java.util.Map;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.core.io.Resource;
import org.springframework.util.Assert;

/**
 * Utility class for resolving prompt templates with provided model data. Provides methods to render
 * templates by replacing placeholders with actual values.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class TemplateUtils {

  // Private constructor to prevent instantiation
  private TemplateUtils() {}

  /**
   * Resolves a prompt template by replacing placeholders with values from the provided model map.
   *
   * @param prompt the prompt template containing placeholders
   * @param model the map containing values to replace placeholders in the prompt
   * @return the resolved prompt with placeholders replaced by actual values
   * @throws IllegalArgumentException if the prompt is null or empty, or if the model map is null or
   *     empty
   */
  public static String getResolvedPrompt(String prompt, Map<String, Object> model) {
    Assert.notNull(model, "model map must be non-null and non-empty");
    PromptTemplate promptTemplate =
        PromptTemplate.builder().template(prompt).variables(model).build();
    return promptTemplate.render();
  }

  /**
   * Resolves a prompt template by replacing placeholders with values from the provided model map.
   *
   * @param prompt the prompt template containing placeholders
   * @param model the map containing values to replace placeholders in the prompt
   * @return the resolved prompt with placeholders replaced by actual values
   * @throws IllegalArgumentException if the prompt is null or empty, or if the model map is null or
   *     empty
   */
  public static String getResolvedPrompt(Resource prompt, Map<String, Object> model) {
    Assert.notNull(model, "model map must be non-null and non-empty");
    PromptTemplate promptTemplate =
        PromptTemplate.builder().resource(prompt).variables(model).build();
    return promptTemplate.render();
  }
}
