/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.vectorstore;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionTextParser;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.commands.ProtocolCommand;
import redis.clients.jedis.util.SafeEncoder;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;

/**
 * Redis 8 Vector Store implementation using native vector sets (VADD, VSIM commands)
 * instead of Redis Stack FT. commands.
 * 
 * This implementation leverages Redis 8's new vector sets data type which provides:
 * - Native vector operations with VADD, VSIM, VREM commands
 * - Built-in quantization (8-bit by default, configurable)
 * - Dimensionality reduction via random projection
 * - JSON-based metadata filtering via VSETATTR
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Redis8VectorStore implements VectorStore, InitializingBean {

    private static final Logger log = LoggerFactory.getLogger(Redis8VectorStore.class);

    /**
     * Redis 8 Vector Set Commands
     */
    public enum VectorCommand implements ProtocolCommand {
        VADD("VADD"),
        VSIM("VSIM"),
        VREM("VREM"),
        VGETATTR("VGETATTR"),
        VSETATTR("VSETATTR"),
        VCARD("VCARD"),
        VDIM("VDIM"),
        VINFO("VINFO");

        private final byte[] raw;

        VectorCommand(String alt) {
            raw = SafeEncoder.encode(alt);
        }

        @Override
        public byte[] getRaw() {
            return raw;
        }
    }
    
    private final JedisPool jedisPool;
    private final EmbeddingModel embeddingModel;
    private final BatchingStrategy batchingStrategy;
    private final ObjectMapper objectMapper;
    
    // Configuration
    private final String vectorSetName;
    private final String keyPrefix;
    private final Redis8VectorStoreConfig config;
    
    // Filter expression parser
    private final FilterExpressionTextParser filterExpressionTextParser;

    /**
     * Constructor for Redis8VectorStore
     */
    public Redis8VectorStore(JedisPool jedisPool, 
                           EmbeddingModel embeddingModel,
                           BatchingStrategy batchingStrategy,
                           Redis8VectorStoreConfig config) {
        Assert.notNull(jedisPool, "JedisPool must not be null");
        Assert.notNull(embeddingModel, "EmbeddingModel must not be null");
        Assert.notNull(batchingStrategy, "BatchingStrategy must not be null");
        Assert.notNull(config, "Redis8VectorStoreConfig must not be null");
        
        this.jedisPool = jedisPool;
        this.embeddingModel = embeddingModel;
        this.batchingStrategy = batchingStrategy;
        this.config = config;
        this.vectorSetName = config.getVectorSetName();
        this.keyPrefix = config.getKeyPrefix();
        this.objectMapper = new ObjectMapper();
        this.filterExpressionTextParser = new FilterExpressionTextParser();
        
        log.info("Initialized Redis8VectorStore with vector set: {}, prefix: {}", 
                vectorSetName, keyPrefix);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (config.isInitializeSchema()) {
            initializeVectorSet();
        }
    }

    @Override
    public String getName() {
        return "Redis8VectorStore";
    }

    @Override
    public void add(List<Document> documents) {
        Assert.notNull(documents, "Documents must not be null");
        
        if (documents.isEmpty()) {
            log.debug("No documents to add");
            return;
        }

        log.debug("Adding {} documents to Redis 8 vector set: {}", documents.size(), vectorSetName);
        
        try {
            // Batch documents for embedding
            List<List<Document>> batches = batchingStrategy.batch(documents);
            
            for (List<Document> batch : batches) {
                processBatch(batch);
            }
            
            log.debug("Successfully added {} documents to vector set", documents.size());
            
        } catch (Exception e) {
            log.error("Error adding documents to Redis 8 vector set", e);
            throw new RuntimeException("Failed to add documents to vector store", e);
        }
    }

    @Override
    public void delete(List<String> idList) {
        Assert.notNull(idList, "ID list must not be null");
        
        if (idList.isEmpty()) {
            log.debug("No document IDs to delete");
        }

        log.debug("Deleting {} documents from Redis 8 vector set: {}", idList.size(), vectorSetName);
        
        try (Jedis jedis = jedisPool.getResource()) {
            for (String id : idList) {
                String elementName = keyPrefix + id;
                
                // Use VREM command to remove from vector set
                Object result = jedis.sendCommand(VectorCommand.VREM, vectorSetName, elementName);
                
                if (result == null || "0".equals(result.toString())) {
                    log.warn("Document with ID {} not found in vector set", id);
                }
            }
            
            log.debug("Successfully deleted {} documents from vector set", idList.size());

        } catch (Exception e) {
            log.error("Error deleting documents from Redis 8 vector set", e);
        }
    }

    @Override
    public void delete(Filter.Expression filterExpression) {
        Assert.notNull(filterExpression, "Filter expression must not be null");
        
        log.debug("Deleting documents by filter expression from Redis 8 vector set: {}", vectorSetName);
        
        try {
            // First, find documents matching the filter
            SearchRequest searchRequest = SearchRequest.builder()
                .query("*") // Match all for deletion
                .topK(10000) // Large number to get all matches
                .filterExpression(filterExpression)
                .build();
                
            List<Document> documentsToDelete = similaritySearch(searchRequest);
            
            if (documentsToDelete.isEmpty()) {
                log.debug("No documents found matching filter expression");
            }
            
            List<String> idsToDelete = documentsToDelete.stream()
                .map(Document::getId)
                .collect(Collectors.toList());
                
             delete(idsToDelete);
            
        } catch (Exception e) {
            log.error("Error deleting documents by filter from Redis 8 vector set", e);
        }
    }

    @Override
    public List<Document> similaritySearch(String query) {
        return similaritySearch(SearchRequest.builder().query(query).build());
    }

    @Override
    public List<Document> similaritySearch(SearchRequest request) {
        Assert.notNull(request, "SearchRequest must not be null");
        Assert.hasText(request.getQuery(), "Query must not be empty");

        log.debug("Performing Redis 8 vector similarity search - vectorSet: {}, query: {}, topK: {}, threshold: {}",
                vectorSetName, request.getQuery(), request.getTopK(), request.getSimilarityThreshold());

        try {
            // Generate embedding for the query using Spring AI's EmbeddingModel
            EmbeddingRequest embeddingRequest = new EmbeddingRequest(
                List.of(request.getQuery()), null);
            EmbeddingResponse embeddingResponse = embeddingModel.call(embeddingRequest);

            if (embeddingResponse.getResults().isEmpty()) {
                log.warn("No embedding generated for query: {}", request.getQuery());
                return new ArrayList<>();
            }

            float[] queryVector = embeddingResponse.getResults().get(0).getOutput();

            // Perform Redis 8 vector similarity search with full SearchRequest context
            return performRedis8VectorSearch(queryVector, request);

        } catch (Exception e) {
            log.error("Error performing Redis 8 vector similarity search", e);
            throw new RuntimeException("Failed to perform similarity search", e);
        }
    }

    /**
     * Enhanced vector search method that fully utilizes Spring AI's SearchRequest
     */
    private List<Document> performRedis8VectorSearch(float[] queryVector, SearchRequest request) throws Exception {
        try (Jedis jedis = jedisPool.getResource()) {
            // Build VSIM command with Spring AI SearchRequest parameters
            List<Object> vsimArgs = buildVsimCommand(queryVector, request);

            // Execute Redis 8 VSIM command
            log.debug("Executing VSIM command with {} arguments (FP32 format)", vsimArgs.size());

            // Convert to byte arrays for Jedis sendCommand
            byte[][] vsimArgsBytes = convertArgsToByteArrays(vsimArgs);
            Object rawResult = jedis.sendCommand(VectorCommand.VSIM, vsimArgsBytes);
            log.debug("VSIM raw result type: {}, value: {}",
                    rawResult != null ? rawResult.getClass().getSimpleName() : "null", rawResult);

            if (rawResult == null) {
                log.warn("VSIM command returned null result");
                return new ArrayList<>();
            }

            List<Object> results;
            if (rawResult instanceof List) {
                results = (List<Object>) rawResult;
                log.debug("VSIM returned {} results", results.size());
            } else {
                log.error("VSIM returned unexpected type: {}, value: {}",
                        rawResult.getClass().getSimpleName(), rawResult);
                return new ArrayList<>();
            }

            // Parse results and apply Spring AI SearchRequest filters
            return parseAndFilterResults(results, request);
        }
    }

    /**
     * Build VSIM command arguments from Spring AI SearchRequest according to Redis documentation
     * Using FP32 format for better performance (10-20x faster than VALUES)
     */
    private List<Object> buildVsimCommand(float[] queryVector, SearchRequest request) {
        List<Object> vsimArgs = new ArrayList<>();
        vsimArgs.add(vectorSetName);

        // Add vector data using FP32 format (must come first after vectorset name)
        vsimArgs.add("FP32");
        byte[] vectorBytes = floatArrayToByteArray(queryVector);
        vsimArgs.add(vectorBytes);

        // Add WITHSCORES to get similarity scores
        vsimArgs.add("WITHSCORES");

        // Add COUNT limit from SearchRequest
        if (request.getTopK() > 0) {
            vsimArgs.add("COUNT");
            vsimArgs.add(String.valueOf(request.getTopK()));
        }

        // Add Redis 8 filter from Spring AI SearchRequest (must come last)
        String redis8Filter = buildRedis8Filter(request);
        if (redis8Filter != null && !redis8Filter.trim().isEmpty()) {
            vsimArgs.add("FILTER");
            vsimArgs.add(redis8Filter);
        }
        
        return vsimArgs;
    }

    @Override
    public Optional<Object> getNativeClient() {
        return Optional.of(jedisPool);
    }

    /**
     * Initialize the vector set with proper configuration
     */
    private void initializeVectorSet() {
        try (Jedis jedis = jedisPool.getResource()) {
            log.info("Initializing Redis 8 vector set: {}", vectorSetName);
            
            // Check if vector set already exists
            boolean exists = jedis.exists(vectorSetName);
            
            if (!exists) {
                // Vector sets are created automatically on first VADD
                log.info("Vector set {} will be created on first document addition", vectorSetName);
            } else {
                log.info("Vector set {} already exists", vectorSetName);
            }
            
        } catch (Exception e) {
            log.error("Error initializing Redis 8 vector set", e);
            throw new RuntimeException("Failed to initialize vector set", e);
        }
    }

    /**
     * Process a batch of documents for embedding and storage
     */
    private void processBatch(List<Document> batch) throws Exception {
        // Generate embeddings for the batch
        List<String> texts = batch.stream()
            .map(Document::getText)
            .collect(Collectors.toList());

        EmbeddingRequest embeddingRequest = new EmbeddingRequest(texts, null);
        EmbeddingResponse embeddingResponse = embeddingModel.call(embeddingRequest);

        if (embeddingResponse.getResults().size() != batch.size()) {
            throw new RuntimeException("Embedding count mismatch: expected " + batch.size() +
                ", got " + embeddingResponse.getResults().size());
        }

        // Add each document with its embedding to the vector set
        for (int i = 0; i < batch.size(); i++) {
            Document document = batch.get(i);
            float[] embedding = embeddingResponse.getResults().get(i).getOutput();

            addDocumentToVectorSet(document, embedding);
        }
    }

    /**
     * Add a single document with its embedding to the vector set
     */
    private void addDocumentToVectorSet(Document document, float[] embedding) throws Exception {
        try (Jedis jedis = jedisPool.getResource()) {
            String elementName = keyPrefix + document.getId();

            // Build VADD command arguments according to Redis documentation
            // Syntax: VADD key [REDUCE dim] (FP32 | VALUES num) vector element [CAS] [NOQUANT | Q8 | BIN]
            List<Object> vaddArgs = new ArrayList<>();
            vaddArgs.add(vectorSetName);

            // Add dimensionality reduction if configured (must come before FP32)
            if (config.isEnableDimensionalityReduction()) {
                vaddArgs.add("REDUCE");
                vaddArgs.add(String.valueOf(config.getTargetDimensions()));
            }

            // Use FP32 format for better performance (10-20x faster than VALUES)
            vaddArgs.add("FP32");

            // Convert float array to byte array for FP32 format
            byte[] vectorBytes = floatArrayToByteArray(embedding);
            vaddArgs.add(vectorBytes);

            // Add element name
            vaddArgs.add(elementName);

            // Add quantization option if configured (after element name)
            switch (config.getQuantizationType()) {
                case BINARY:
                    vaddArgs.add("BIN");
                    break;
                case NONE:
                    vaddArgs.add("NOQUANT");
                    break;
                case QUANTIZE_8BIT:
                    vaddArgs.add("Q8");
                    break;
            }

            // Execute VADD command with mixed argument types
            // Convert to byte arrays for Jedis sendCommand
            byte[][] vaddArgsBytes = convertArgsToByteArrays(vaddArgs);
            Object result = jedis.sendCommand(VectorCommand.VADD, vaddArgsBytes);

            if (result == null || "0".equals(result.toString())) {
                log.warn("Failed to add document {} to vector set", document.getId());
            } else {
                // Set attributes separately using VSETATTR
                setDocumentAttributes(jedis, elementName, document);

                log.trace("Added document {} to vector set with {} dimensions",
                        document.getId(), embedding.length);
            }
        }
    }

    /**
     * Set document attributes using VSETATTR command
     */
    private void setDocumentAttributes(Jedis jedis, String elementName, Document document) throws Exception {
        // Prepare attributes JSON
        Map<String, Object> attributes = new HashMap<>(document.getMetadata());
        attributes.put("content", document.getText());
        attributes.put("id", document.getId());
        String attributesJson = objectMapper.writeValueAsString(attributes);

        // Execute VSETATTR command
        Object result = jedis.sendCommand(VectorCommand.VSETATTR, vectorSetName, elementName, attributesJson);

        if (result == null || "0".equals(result.toString())) {
            log.warn("Failed to set attributes for document {} in vector set", document.getId());
        }
    }

    /**
     * Parse and filter search results using Spring AI SearchRequest context
     */
    @SuppressWarnings("unchecked")
    private List<Document> parseAndFilterResults(List<Object> results, SearchRequest request) throws Exception {
        List<Document> documents = new ArrayList<>();

        if (results == null || results.isEmpty()) {
            log.debug("No results returned from Redis 8 VSIM command");
            return documents;
        }

        try (Jedis jedis = jedisPool.getResource()) {
            log.debug("Processing {} VSIM results", results.size());

            // VSIM with WITHSCORES returns pairs of [element, score]
            for (int i = 0; i < results.size(); i += 2) {
                if (i + 1 >= results.size()) {
                    log.warn("Odd number of results from VSIM WITHSCORES - missing score for element at index {}", i);
                    break;
                }

                Object elementObj = results.get(i);
                Object scoreObj = results.get(i + 1);

                if (elementObj == null || scoreObj == null) {
                    log.warn("Null element or score at index {}: element={}, score={}", i, elementObj, scoreObj);
                    continue;
                }

                // Decode element name from byte array if needed
                String elementName;
                if (elementObj instanceof byte[]) {
                    elementName = new String((byte[]) elementObj, java.nio.charset.StandardCharsets.UTF_8);
                } else {
                    elementName = elementObj.toString();
                }

                // Decode score from byte array if needed
                double score;
                try {
                    switch (scoreObj) {
                        case byte[] bytes -> {
                            String scoreStr = new String(bytes, StandardCharsets.UTF_8);
                            score = Double.parseDouble(scoreStr.trim());
                        }
                        case Number number -> score = number.doubleValue();
                        case String string -> {
                            String scoreStr = scoreObj.toString().trim();
                            if (scoreStr.isEmpty() || "null".equalsIgnoreCase(scoreStr)) {
                                log.warn("Empty or null score string for element {}, skipping", elementName);
                                continue;
                            }
                            score = Double.parseDouble(scoreStr);
                        }
                        default -> {
                            log.warn("Unexpected score type {} for element {}: {}",
                                    scoreObj.getClass().getSimpleName(), elementName, scoreObj);
                            continue;
                        }
                    }

                    log.trace("Parsed element: {}, score: {}", elementName, score);

                } catch (NumberFormatException e) {
                    log.error("Failed to parse score '{}' for element {}: {}", scoreObj, elementName, e.getMessage());
                    continue;
                }

                // Validate score range (Redis similarity scores should be between 0 and 1)
                if (score < 0.0 || score > 1.0) {
                    log.warn("Score {} for element {} is outside expected range [0.0, 1.0]", score, elementName);
                }

                // Apply Spring AI SearchRequest similarity threshold
                if (request.getSimilarityThreshold() > 0.0 && score < request.getSimilarityThreshold()) {
                    log.trace("Skipping document {} with score {} below threshold {}",
                            elementName, score, request.getSimilarityThreshold());
                    continue;
                }

                // Retrieve document attributes using Redis 8 VGETATTR
                Document document = retrieveDocumentFromRedis8(jedis, elementName, score);
                if (document != null) {
                    documents.add(document);
                }
            }
        }

        log.debug("Parsed {} documents from Redis 8 search results (applied Spring AI filters)", documents.size());
        return documents;
    }

    /**
     * Retrieve a document from Redis 8 vector set with attributes
     */
    private Document retrieveDocumentFromRedis8(Jedis jedis, String elementName, double score) throws Exception {
        try {
            log.trace("Retrieving attributes for element: {}", elementName);

            // Get attributes using Redis 8 VGETATTR command
            Object attrResult = jedis.sendCommand(VectorCommand.VGETATTR, vectorSetName, elementName);

            log.trace("VGETATTR result for {}: type={}, value={}",
                    elementName,
                    attrResult != null ? attrResult.getClass().getSimpleName() : "null",
                    attrResult);

            if (attrResult == null) {
                log.warn("No attributes found for element: {}", elementName);
                return null;
            }

            // Decode attributes JSON from byte array if needed
            String attributesJson;
            if (attrResult instanceof byte[]) {
                attributesJson = new String((byte[]) attrResult, StandardCharsets.UTF_8);
            } else {
                attributesJson = attrResult.toString();
            }

            if (attributesJson.trim().isEmpty() || "null".equalsIgnoreCase(attributesJson.trim())) {
                log.warn("Empty or null attributes JSON for element: {}", elementName);
                return null;
            }

            log.trace("Parsing attributes JSON for {}: {}", elementName, attributesJson);

            Map<String, Object> attributes;
            try {
                attributes = objectMapper.readValue(attributesJson, Map.class);
            } catch (Exception e) {
                log.error("Failed to parse attributes JSON for element {}: {}", elementName, e.getMessage());
                log.debug("Invalid JSON: {}", attributesJson);
                return null;
            }

            // Extract content and metadata
            String content = (String) attributes.remove("content");
            String id = (String) attributes.remove("id");

            if (content == null || id == null) {
                log.warn("Missing content or id for element {}: content={}, id={}", elementName, content != null, id != null);
                log.debug("Available attributes: {}", attributes.keySet());
                return null;
            }

            // Add Spring AI compatible metadata
            attributes.put("similarity_score", score);
            attributes.put("distance", 1.0 - score); // Spring AI often uses distance

            Document document = new Document(id, content, attributes);
            log.trace("Successfully retrieved document {} with similarity score {}", id, score);

            return document;

        } catch (Exception e) {
            log.error("Error retrieving document {} from Redis 8: {}", elementName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Build Redis 8 filter from SearchRequest using Spring AI's filter capabilities
     * Redis 8 uses JavaScript-like filter expressions with dot notation
     */
    private String buildRedis8Filter(SearchRequest request) {
        if (request.getFilterExpression() == null) {
            return null;
        }

        // Convert Spring AI filter expression to Redis 8 JavaScript-like syntax
        return convertSpringAIFilterToRedis8Expression(request.getFilterExpression());
    }

    /**
     * Convert Spring AI Filter.Expression to Redis 8 JavaScript-like filter expression
     * Redis 8 uses syntax like: '.year > 1950 and .rating >= 8.0'
     */
    private String convertSpringAIFilterToRedis8Expression(Filter.Expression expression) {
        StringBuilder filterBuilder = new StringBuilder();
        processFilterExpressionToString(expression, filterBuilder);
        return filterBuilder.toString();
    }

    /**
     * Process Spring AI Filter.Expression recursively to build Redis 8 JavaScript-like expression
     */
    private void processFilterExpressionToString(Filter.Expression expression, StringBuilder builder) {
        switch (expression.type()) {
            case EQ:
            case NE:
            case GT:
            case GTE:
            case LT:
            case LTE:
            case IN:
            case NIN:
                processComparisonExpressionToString(expression, builder);
                break;
            case AND:
            case OR:
                processLogicalExpressionToString(expression, builder);
                break;
            case NOT:
                processNotExpressionToString(expression, builder);
                break;
            default:
                log.warn("Unsupported filter expression type: {}", expression.type());
        }
    }

    /**
     * Process comparison expressions to Redis 8 JavaScript-like syntax
     */
    private void processComparisonExpressionToString(Filter.Expression expression, StringBuilder builder) {
        try {
            // For comparison expressions, left should be Filter.Key and right should be Filter.Value
            if (expression.left() instanceof Filter.Key key && expression.right() instanceof Filter.Value value) {
                String operator = getRedis8JSOperator(expression.type());

                // Build expression like: .fieldName == "value"
                builder.append(".").append(key.key()).append(" ").append(operator).append(" ");

                // Handle different value types
                Object val = value.value();
                if (val instanceof String) {
                    builder.append("\"").append(val).append("\"");
                } else if (val instanceof Number || val instanceof Boolean) {
                    builder.append(val);
                } else if (val instanceof java.util.Collection<?> collection) {
                    // Handle IN/NIN with arrays
                    builder.append("[");
                    boolean first = true;
                    for (Object item : collection) {
                        if (!first) builder.append(", ");
                        if (item instanceof String) {
                            builder.append("\"").append(item).append("\"");
                        } else {
                            builder.append(item);
                        }
                        first = false;
                    }
                    builder.append("]");
                } else {
                    builder.append("\"").append(val).append("\"");
                }
            } else {
                log.warn("Invalid operands for comparison expression: left={}, right={}",
                        expression.left().getClass().getSimpleName(),
                        expression.right().getClass().getSimpleName());
            }
        } catch (Exception e) {
            log.error("Error processing comparison expression: {}", e.getMessage());
        }
    }

    /**
     * Get Redis 8 JavaScript-like operator for Spring AI expression type
     */
    private String getRedis8JSOperator(Filter.ExpressionType type) {
        return switch (type) {
            case EQ -> "==";
            case NE -> "!=";
            case GT -> ">";
            case GTE -> ">=";
            case LT -> "<";
            case LTE -> "<=";
            case IN -> "in";
            case NIN -> "not in";
            default -> {
                log.warn("Unknown expression type: {}", type);
                yield "==";
            }
        };
    }


    /**
     * Process logical expressions (AND, OR) to Redis 8 JavaScript-like syntax
     */
    private void processLogicalExpressionToString(Filter.Expression expression, StringBuilder builder) {
        try {
            String operator = expression.type() == Filter.ExpressionType.AND ? " and " : " or ";

            // Add parentheses for grouping
            builder.append("(");

            // Process left operand
            if (expression.left() instanceof Filter.Expression leftExpr) {
                processFilterExpressionToString(leftExpr, builder);
            } else if (expression.left() instanceof Filter.Group leftGroup) {
                processFilterExpressionToString(leftGroup.content(), builder);
            }

            builder.append(operator);

            // Process right operand
            if (expression.right() instanceof Filter.Expression rightExpr) {
                processFilterExpressionToString(rightExpr, builder);
            } else if (expression.right() instanceof Filter.Group rightGroup) {
                processFilterExpressionToString(rightGroup.content(), builder);
            }

            builder.append(")");

        } catch (Exception e) {
            log.error("Error processing logical expression: {}", e.getMessage());
        }
    }

    /**
     * Process NOT expressions to Redis 8 JavaScript-like syntax
     */
    private void processNotExpressionToString(Filter.Expression expression, StringBuilder builder) {
        try {
            builder.append("not (");

            // NOT expressions typically use only the left operand
            if (expression.left() instanceof Filter.Expression leftExpr) {
                processFilterExpressionToString(leftExpr, builder);
            } else if (expression.left() instanceof Filter.Group leftGroup) {
                processFilterExpressionToString(leftGroup.content(), builder);
            } else {
                log.warn("Unexpected operand type for NOT expression: {}",
                        expression.left().getClass().getSimpleName());
                return;
            }

            builder.append(")");

        } catch (Exception e) {
            log.error("Error processing NOT expression: {}", e.getMessage());
        }
    }

    /**
     * Builder pattern for Redis8VectorStore
     */
    public static class Builder {
        private JedisPool jedisPool;
        private EmbeddingModel embeddingModel;
        private BatchingStrategy batchingStrategy;
        private Redis8VectorStoreConfig config;

        public Builder(JedisPool jedisPool, EmbeddingModel embeddingModel) {
            this.jedisPool = jedisPool;
            this.embeddingModel = embeddingModel;
        }

        public Builder batchingStrategy(BatchingStrategy batchingStrategy) {
            this.batchingStrategy = batchingStrategy;
            return this;
        }

        public Builder config(Redis8VectorStoreConfig config) {
            this.config = config;
            return this;
        }

        public Redis8VectorStore build() {
            if (config == null) {
                config = Redis8VectorStoreConfig.builder().build();
            }
            return new Redis8VectorStore(jedisPool, embeddingModel, batchingStrategy, config);
        }
    }

    public static Builder builder(JedisPool jedisPool, EmbeddingModel embeddingModel) {
        return new Builder(jedisPool, embeddingModel);
    }

    /**
     * Get vector set cardinality (number of vectors stored)
     * This helps verify if embeddings are actually being stored
     */
    public long getVectorSetCardinality() {
        try (Jedis jedis = jedisPool.getResource()) {
            Object result = jedis.sendCommand(VectorCommand.VCARD, vectorSetName);
            if (result instanceof byte[]) {
                String cardStr = new String((byte[]) result, StandardCharsets.UTF_8);
                return Long.parseLong(cardStr);
            } else if (result instanceof Number) {
                return ((Number) result).longValue();
            } else if (result != null) {
                return Long.parseLong(result.toString());
            }
            return 0L;
        } catch (Exception e) {
            log.error("Error getting vector set cardinality: {}", e.getMessage());
            return -1L;
        }
    }

    /**
     * Convert float array to byte array for FP32 format
     * Redis expects 32-bit floats in little-endian format
     */
    private byte[] floatArrayToByteArray(float[] floats) {
        ByteBuffer buffer = ByteBuffer.allocate(floats.length * 4);
        buffer.order(ByteOrder.LITTLE_ENDIAN); // Redis expects little-endian

        for (float f : floats) {
            buffer.putFloat(f);
        }

        return buffer.array();
    }

    /**
     * Convert mixed argument list to byte arrays for Jedis sendCommand
     * Jedis sendCommand expects byte[][] arguments
     */
    private byte[][] convertArgsToByteArrays(List<Object> args) {
        byte[][] result = new byte[args.size()][];

        for (int i = 0; i < args.size(); i++) {
            Object arg = args.get(i);
            if (arg instanceof byte[]) {
                result[i] = (byte[]) arg;
            } else if (arg instanceof String) {
                result[i] = SafeEncoder.encode((String) arg);
            } else {
                result[i] = SafeEncoder.encode(arg.toString());
            }
        }

        return result;
    }
}
