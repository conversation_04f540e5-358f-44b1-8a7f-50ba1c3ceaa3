/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.assertion.AssertionResponseDto;
import com.enttribe.promptanalyzer.rest.AssertionRest;
import com.enttribe.promptanalyzer.service.AssertionService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/assertion")
@RequiredArgsConstructor
public class AssertionRestImpl implements AssertionRest {

  private final AssertionService assertionService;

  @Override
  public List<AssertionResponseDto> searchAssertions(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug(
        "Inside @method searchAssertions with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
        filter,
        offset,
        size,
        orderBy,
        orderType);
    try {
      return assertionService.searchAssertion(filter, offset, size, orderBy, orderType);
    } catch (Exception e) {
      log.error("Error in @method searchAssertions : {}", e.getMessage(), e);
      throw e;
    }
  }
}
