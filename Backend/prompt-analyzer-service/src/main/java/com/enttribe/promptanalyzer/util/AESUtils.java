/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.exception.BusinessException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.util.Base64;
import java.util.Objects;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.PBEKeySpec;
import javax.crypto.spec.SecretKeySpec;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Utility class for AES encryption and decryption operations. This class provides methods for
 * decrypting strings using AES encryption with salt and IV. The class uses PBKDF2 with HMAC-SHA1
 * for key derivation and AES/CBC/PKCS5Padding for encryption.
 */
public final class AESUtils {

  private AESUtils() {
    // Private constructor to hide the implicit public one
  }

  private static final Logger LOGGER = LogManager.getLogger(AESUtils.class);
  private static final String AES_ALGORITHM = "QUVTL0NCQy9QS0NTNVBhZGRpbmc=";
  private static final String KEY_FACTORY_ALGORITHM = "PBKDF2WithHmacSHA1";
  private static final String AES_KEY_SPEC = "AES";
  private static final int KEY_LENGTH = 128;
  private static final int ITERATION_COUNT = 100;
  private static final int IV_LENGTH = 32;
  private static final int SALT_LENGTH = 32;
  private static final String DEFAULT_PHRASE = "productLogin/js/dist/loginLib.min.js";
  private static final int NIBBLE_SHIFT = 4;
  private static final int HEX_RADIX = 16;
  private static final int TWO = 2;
  private static final int BYTE_DIVISOR_FOR_HEX = 2;

  /**
   * Decrypts a string that was encrypted using AES encryption. The input string should contain the
   * IV, salt, and encrypted data concatenated together. The first 32 characters are the IV, the
   * next 32 characters are the salt, and the remaining characters are the encrypted data.
   *
   * @param cipherText The encrypted text to decrypt, containing IV, salt, and encrypted data
   * @return The decrypted string
   * @throws IllegalArgumentException if the cipher text length is invalid
   * @throws NullPointerException if the cipher text is null
   * @throws RuntimeException if decryption fails
   */
  public static String decryptString(String cipherText) {
    Objects.requireNonNull(cipherText, "Cipher text cannot be null");

    if (cipherText.length() < IV_LENGTH + SALT_LENGTH) {
      throw new IllegalArgumentException("Invalid cipher text length");
    }

    try {
      String iv = cipherText.substring(0, IV_LENGTH);
      String salt = cipherText.substring(IV_LENGTH, IV_LENGTH + SALT_LENGTH);
      String password = cipherText.substring(IV_LENGTH + SALT_LENGTH);
      return decryptAESEncryptWithSaltAndIV(password, DEFAULT_PHRASE, salt, iv);
    } catch (Exception e) {
      LOGGER.error("Failed to decrypt string: {}", e.getMessage(), e);
      return cipherText;
    }
  }

  /**
   * Decrypts data using AES encryption with salt and IV. This method performs the actual decryption
   * using the provided key, salt, and IV.
   *
   * @param encryptedData The encrypted data to decrypt (Base64 encoded)
   * @param key The encryption key used for decryption
   * @param salt The salt used in the encryption process (hex string)
   * @param iv The initialization vector used in the encryption process (hex string)
   * @return The decrypted string
   */
  public static String decryptAESEncryptWithSaltAndIV(
      String encryptedData, String key, String salt, String iv) {
    try {
      Objects.requireNonNull(encryptedData, "Encrypted data cannot be null");
      Objects.requireNonNull(key, "Key cannot be null");
      Objects.requireNonNull(salt, "Salt cannot be null");
      Objects.requireNonNull(iv, "IV cannot be null");

      byte[] saltBytes = hexStringToByteArray(salt);
      byte[] ivBytes = hexStringToByteArray(iv);
      IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
      SecretKeySpec secretKey = (SecretKeySpec) generateKeyFromPasswordWithSalt(key, saltBytes);

      Cipher cipher = Cipher.getInstance(new String(Base64.getDecoder().decode(AES_ALGORITHM)));
      cipher.init(Cipher.DECRYPT_MODE, secretKey, ivParameterSpec);

      byte[] decodedValue = Base64.getDecoder().decode(encryptedData.getBytes());
      byte[] decryptedBytes = cipher.doFinal(decodedValue);
      return new String(decryptedBytes);
    } catch (Exception e) {
      throw new BusinessException("Failed to decrypt AES data", e);
    }
  }

  /**
   * Converts a hexadecimal string to a byte array. The input string must have an even length as
   * each byte is represented by two hexadecimal characters.
   *
   * @param hexString The hexadecimal string to convert
   * @return The byte array representation of the hexadecimal string
   * @throws IllegalArgumentException if the hex string has an odd length
   * @throws NullPointerException if the hex string is null
   */
  public static byte[] hexStringToByteArray(String hexString) {
    Objects.requireNonNull(hexString, "Hex string cannot be null");
    if (hexString.length() % TWO != 0) {
      throw new IllegalArgumentException("Hex string must have an even length");
    }

    int len = hexString.length();
    byte[] data = new byte[len / TWO];
    for (int i = 0; i < len; i += TWO) {
      data[i / TWO] =
          (byte)
              ((Character.digit(hexString.charAt(i), HEX_RADIX) << NIBBLE_SHIFT)
                  + Character.digit(hexString.charAt(i + 1), HEX_RADIX));
    }
    return data;
  }

  /**
   * Generates a secret key from a password and salt using PBKDF2 with HMAC-SHA1. This method is
   * used to derive a cryptographic key from a password and salt.
   *
   * @param password The password to use for key generation
   * @param saltBytes The salt bytes to use in the key generation process
   * @return The generated secret key
   * @throws NoSuchAlgorithmException if the key generation algorithm is not available
   * @throws InvalidKeySpecException if the key specification is invalid
   * @throws NullPointerException if either password or saltBytes is null
   */
  public static SecretKey generateKeyFromPasswordWithSalt(String password, byte[] saltBytes)
      throws NoSuchAlgorithmException, InvalidKeySpecException {
    Objects.requireNonNull(password, "Password cannot be null");
    Objects.requireNonNull(saltBytes, "Salt bytes cannot be null");

    KeySpec keySpec =
        new PBEKeySpec(password.toCharArray(), saltBytes, ITERATION_COUNT, KEY_LENGTH);
    SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(KEY_FACTORY_ALGORITHM);
    SecretKey secretKey = keyFactory.generateSecret(keySpec);
    return new SecretKeySpec(secretKey.getEncoded(), AES_KEY_SPEC);
  }

  /**
   * Encrypts a string using AES encryption with a randomly generated IV and salt. The encrypted
   * data is returned as a concatenated string of the IV, salt, and encrypted data.
   *
   * @param plainText The plain text to encrypt
   * @return The encrypted string containing the IV, salt, and encrypted data
   * @throws BusinessException if encryption fails
   */
  public static String encryptString(String plainText) {
    try {
      byte[] ivBytes = generateRandomBytes(IV_LENGTH / BYTE_DIVISOR_FOR_HEX); // 16 bytes IV
      byte[] saltBytes = generateRandomBytes(SALT_LENGTH / BYTE_DIVISOR_FOR_HEX); // 16 bytes salt

      IvParameterSpec ivSpec = new IvParameterSpec(ivBytes); // NOSONAR
      SecretKeySpec secretKey =
          (SecretKeySpec) generateKeyFromPasswordWithSalt(DEFAULT_PHRASE, saltBytes);

      Cipher cipher = Cipher.getInstance(new String(Base64.getDecoder().decode(AES_ALGORITHM)));
      cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);

      byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
      String encryptedData = Base64.getEncoder().encodeToString(encryptedBytes);

      return bytesToHex(ivBytes) + bytesToHex(saltBytes) + encryptedData;

    } catch (Exception e) {
      LOGGER.error("Failed to encrypt string: {}", e.getMessage(), e);
      throw new BusinessException("Failed to encrypt AES data", e);
    }
  }

  /**
   * Converts a byte array to a hexadecimal string. Each byte is represented by two hexadecimal
   * characters.
   *
   * @param bytes The byte array to convert
   * @return The hexadecimal string representation of the byte array
   */
  private static String bytesToHex(byte[] bytes) {
    StringBuilder hexString = new StringBuilder();
    for (byte b : bytes) {
      String hex = Integer.toHexString(0xff & b);
      if (hex.length() == 1) {
        hexString.append('0');
      }
      hexString.append(hex);
    }
    return hexString.toString();
  }

  /**
   * Generates a random byte array of the specified length. This method uses a secure random number
   * generator to create the byte array.
   *
   * @param length The length of the byte array to generate
   * @return A byte array filled with random bytes
   */
  private static byte[] generateRandomBytes(int length) {
    byte[] bytes = new byte[length];
    new java.security.SecureRandom().nextBytes(bytes);
    return bytes;
  }
}
