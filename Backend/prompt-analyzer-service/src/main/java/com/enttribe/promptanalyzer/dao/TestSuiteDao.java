/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.TestSuite;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TestSuiteDao extends JpaRepository<TestSuite, Integer> {

  @Query(
      "SELECT t FROM TestSuite t LEFT JOIN FETCH t.agentTestCases WHERE t.nanoId = :nanoId and t.deleted = false")
  Optional<TestSuite> findByNanoId(String nanoId);

  @Query(
      """
            SELECT DISTINCT t FROM TestSuite t
            LEFT JOIN FETCH t.agentTestCases atc
            LEFT JOIN FETCH atc.tools
            LEFT JOIN FETCH atc.agentTestCaseAssertions
            LEFT JOIN FETCH atc.knowledgeBases
            LEFT JOIN FETCH atc.prompt
            WHERE t.nanoId = :nanoId AND t.deleted = false
            """)
  Optional<TestSuite> findByNanoIdWithAll(@Param("nanoId") String nanoId);
}
