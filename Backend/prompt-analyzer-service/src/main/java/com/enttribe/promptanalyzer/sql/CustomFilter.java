/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.sql;

import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.FilterConstants;
import com.enttribe.promptanalyzer.enums.CustomerType;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.util.SaaSUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Component for applying custom filters to database queries. Provides methods for searching and
 * counting entities based on dynamic query conditions.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CustomFilter {

  private static final int EXPECTED_KEY_VALUE_PAIR_LENGTH = 2;
  private static final int GLOBAL_CUSTOMER_ID = 1;
  @PersistenceContext private EntityManager entityManager;
  private final CustomerInfo customerInfo;

  public <T> List<T> searchByFilter(
      Class<T> type,
      CustomerType customerType,
      String query,
      String orderBy,
      String orderType,
      Integer offset,
      Integer size) {
    if (CustomerType.LOCAL.equals(customerType)) {
      SaaSUtils.enableFilter(
          entityManager,
          FilterConstants.CUSTOMER_ID_FILTER_LOCAL,
          customerInfo.getCustomerWrapper().getId());
    } else {
      SaaSUtils.enableFilter(
          entityManager, FilterConstants.CUSTOMER_ID_FILTER_GLOBAL, GLOBAL_CUSTOMER_ID);
    }
    return searchByFilter(type, query, orderBy, orderType, offset, size);
  }

  public <T> Long countByFilter(Class<T> type, CustomerType customerType, String query) {
    if (CustomerType.LOCAL.equals(customerType)) {
      SaaSUtils.enableFilter(
          entityManager,
          FilterConstants.CUSTOMER_ID_FILTER_LOCAL,
          customerInfo.getCustomerWrapper().getId());
    } else {
      SaaSUtils.enableFilter(
          entityManager, FilterConstants.CUSTOMER_ID_FILTER_GLOBAL, GLOBAL_CUSTOMER_ID);
    }
    return countByFilter(type, query);
  }

  /**
   * Searches for entities of the specified type based on the given filter criteria.
   *
   * @param type the entity class type
   * @param query the filter query string
   * @param orderBy the field to order by
   * @param orderType the order type (asc or desc)
   * @param offset the starting position of the result set
   * @param size the maximum number of results to return
   * @param <T> the type of the entity
   * @return a list of entities matching the filter criteria
   */
  public <T> List<T> searchByFilter(
      Class<T> type, String query, String orderBy, String orderType, Integer offset, Integer size) {
    // Initialize CriteriaBuilder and CriteriaQuery
    CriteriaBuilder builder = entityManager.getCriteriaBuilder();
    CriteriaQuery<T> criteria = builder.createQuery(type);
    Root<T> root = criteria.from(type);

    // Build predicates from query
    List<Predicate> predicates = parseQueryToPredicates(builder, root, query);

    // Apply the predicates to the query
    if (!predicates.isEmpty()) {
      criteria.where(predicates.toArray(new Predicate[0]));
    }

    // Apply ordering if provided
    applyOrdering(builder, criteria, root, orderBy, orderType);

    // Create and configure the query
    TypedQuery<T> typedQuery = entityManager.createQuery(criteria);
    applyPagination(typedQuery, offset, size);

    // Execute and return results
    return typedQuery.getResultList();
  }

  /**
   * Counts the number of entities of the specified type based on the given filter criteria.
   *
   * @param type the entity class type
   * @param query the filter query string
   * @param <T> the type of the entity
   * @return the count of entities matching the filter criteria
   */
  public <T> Long countByFilter(Class<T> type, String query) {
    // Initialize CriteriaBuilder and CriteriaQuery for counting
    CriteriaBuilder builder = entityManager.getCriteriaBuilder();
    CriteriaQuery<Long> criteria = builder.createQuery(Long.class);
    Root<T> root = criteria.from(type);

    // Set the select clause to count rows
    criteria.select(builder.count(root));

    // Build predicates from query
    List<Predicate> predicates = parseQueryToPredicates(builder, root, query);

    // Apply the predicates to the query
    if (!predicates.isEmpty()) {
      criteria.where(predicates.toArray(new Predicate[0]));
    }

    // Execute the count query
    return entityManager.createQuery(criteria).getSingleResult();
  }

  /**
   * Parses a query string into a list of predicates for filtering.
   *
   * @param builder the CriteriaBuilder
   * @param root the root entity
   * @param query the filter query string
   * @param <T> the type of the entity
   * @return a list of predicates for filtering
   */
  private <T> List<Predicate> parseQueryToPredicates(
      CriteriaBuilder builder, Root<T> root, String query) {
    List<Predicate> predicates = new ArrayList<>();

    if (query == null || query.trim().isEmpty()) {
      return predicates;
    } else {
      query = query.replace("((", "(").replace("))", ")");
    }

    String[] conditions = query.split(";");

    for (String condition : conditions) {
      condition = condition.trim();
      if (isOrGroup(condition)) {
        predicates.add(processOrConditionGroup(condition, builder, root));
      } else {
        addSinglePredicate(condition, builder, root, predicates);
      }
    }

    return predicates;
  }

  private boolean isOrGroup(String condition) {
    return condition.startsWith("(") && condition.endsWith(")");
  }

  private <T> Predicate processOrConditionGroup(
      String condition, CriteriaBuilder builder, Root<T> root) {
    String[] orConditions = condition.substring(1, condition.length() - 1).split(",");
    List<Predicate> orPredicates = new ArrayList<>();

    for (String orCondition : orConditions) {
      orCondition = orCondition.trim();
      Predicate predicate = createPredicate(builder, root, orCondition);
      if (predicate != null) {
        orPredicates.add(predicate);
      }
    }

    return orPredicates.isEmpty() ? null : builder.or(orPredicates.toArray(new Predicate[0]));
  }

  private <T> void addSinglePredicate(
      String condition, CriteriaBuilder builder, Root<T> root, List<Predicate> predicates) {
    Predicate predicate = createPredicate(builder, root, condition);
    if (predicate != null) {
      predicates.add(predicate);
    }
  }

  private <T> Predicate createPredicate(CriteriaBuilder builder, Root<T> root, String condition) {
    String[] operators = {"=ge=", "=le=", "=gt=", "=lt=", "==", "!="};
    for (String operator : operators) {
      if (condition.contains(operator)) {
        String[] keyValue = condition.split(Pattern.quote(operator));
        if (keyValue.length != EXPECTED_KEY_VALUE_PAIR_LENGTH) {
          return null;
        }
        String key = keyValue[0].trim();
        String value = keyValue[1].trim();
        return buildPredicate(builder, root, key, value, operator);
      }
    }
    return null;
  }

  private <T> Predicate buildPredicate(
      CriteriaBuilder builder, Root<T> root, String key, String value, String operator) {
    boolean isNested = key.contains(".");
    Path<Object> path = isNested ? resolvePath(root, key) : root.get(key);
    Class<?> fieldType = path.getJavaType();
    Object convertedValue = convertValueToFieldType(fieldType, value.replace("'", ""));
    boolean isWildcard = value.contains("*");

    return switch (operator) {
      case "==" -> isWildcard
          ? builder.like(
              path.as(String.class), "%" + convertedValue.toString().replace("*", "") + "%")
          : builder.equal(path, convertedValue);
      case "!=" -> isWildcard
          ? builder.notLike(
              path.as(String.class), "%" + convertedValue.toString().replace("*", "") + "%")
          : builder.notEqual(path, convertedValue);
      case "=gt=" -> castAndCompare(builder, path, convertedValue, ">");
      case "=lt=" -> castAndCompare(builder, path, convertedValue, "<");
      case "=ge=" -> castAndCompare(builder, path, convertedValue, ">=");
      case "=le=" -> castAndCompare(builder, path, convertedValue, "<=");
      default -> null;
    };
  }

  private Predicate castAndCompare(
      CriteriaBuilder builder, Path<?> path, Object value, String operator) {

    if (!(value instanceof Comparable<?>)) {
      throw new IllegalArgumentException("Field value is not Comparable for operator: " + operator);
    }

    @SuppressWarnings("unchecked")
    Path<Comparable<Object>> comparablePath = (Path<Comparable<Object>>) path;
    @SuppressWarnings("unchecked")
    Comparable<Object> comparableValue = (Comparable<Object>) value;

    return switch (operator) {
      case ">" -> builder.greaterThan(comparablePath, comparableValue);
      case "<" -> builder.lessThan(comparablePath, comparableValue);
      case ">=" -> builder.greaterThanOrEqualTo(comparablePath, comparableValue);
      case "<=" -> builder.lessThanOrEqualTo(comparablePath, comparableValue);
      default -> throw new IllegalArgumentException("Unsupported comparison operator: " + operator);
    };
  }

  private Path<Object> resolvePath(Root<?> root, String key) {
    String[] parts = key.split("\\.");
    Path<Object> path = root.get(parts[0]);
    for (int i = 1; i < parts.length; i++) {
      path = path.get(parts[i]);
    }
    return path;
  }

  /**
   * Converts a string value to the specified field type.
   *
   * @param fieldType the field type
   * @param value the string value to convert
   * @return the converted value
   */
  @SuppressWarnings("unchecked")
  private Object convertValueToFieldType(Class<?> fieldType, String value) {
    if (fieldType == Boolean.class || fieldType == boolean.class) {
      return Boolean.parseBoolean(value);
    } else if (fieldType == Integer.class || fieldType == int.class) {
      return Integer.parseInt(value);
    } else if (fieldType == Long.class || fieldType == long.class) {
      return Long.parseLong(value);
    } else if (fieldType == Double.class || fieldType == double.class) {
      return Double.parseDouble(value);
    } else if (fieldType == Float.class || fieldType == float.class) {
      return Float.parseFloat(value);
    } else if (fieldType == String.class) {
      return value;
    } else if (fieldType.isEnum()) {
      return parseEnumValue(fieldType, value);
    }

    throw new IllegalArgumentException("Unsupported field type: " + fieldType);
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  private <E extends Enum<E>> E parseEnumValue(Class<?> fieldType, String value) {
    try {
      Class<? extends Enum> enumClass = fieldType.asSubclass(Enum.class);
      return (E) Enum.valueOf(enumClass, value);
    } catch (Exception e) {
      throw new IllegalArgumentException(
          "Invalid enum value '" + value + "' for enum type: " + fieldType.getSimpleName(), e);
    }
  }

  /**
   * Applies ordering to the criteria query based on the specified order field and type.
   *
   * @param builder the CriteriaBuilder
   * @param criteria the CriteriaQuery
   * @param root the root entity
   * @param orderBy the field to order by
   * @param orderType the order type (asc or desc)
   * @param <T> the type of the entity
   */
  private <T> void applyOrdering(
      CriteriaBuilder builder,
      CriteriaQuery<T> criteria,
      Root<T> root,
      String orderBy,
      String orderType) {
    if (orderBy != null && orderType != null) {
      if ("desc".equalsIgnoreCase(orderType)) {
        criteria.orderBy(builder.desc(root.get(orderBy)));
      } else {
        criteria.orderBy(builder.asc(root.get(orderBy)));
      }
    }
  }

  /**
   * Applies pagination to the typed query based on the specified offset and size.
   *
   * @param query the TypedQuery
   * @param offset the starting position of the result set
   * @param size the maximum number of results to return
   */
  private void applyPagination(TypedQuery<?> query, Integer offset, Integer size) {
    if (offset != null && offset >= 0) {
      query.setFirstResult(offset);
    }
    if (size != null && size > 0) {
      query.setMaxResults(size);
    }
  }

  /**
   * Checks if the current user is allowed to delete a resource created by another user.
   *
   * @param currentUserId the ID of the current user
   * @param creatorId the ID of the creator of the resource
   * @throws AccessDeniedException if the current user is not allowed to delete
   */
  public void verifyCreator(Integer currentUserId, Integer creatorId) {
    if (creatorId == null || Objects.equals(currentUserId, creatorId)) {
      return;
    }
    throw new AccessDeniedException("Access denied: only the creator can delete this resource.");
  }
}
