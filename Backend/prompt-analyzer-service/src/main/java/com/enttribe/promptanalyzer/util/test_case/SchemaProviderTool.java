/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;

public class SchemaProviderTool
    implements Function<SchemaProviderTool.Request, SchemaProviderTool.Response> {

  private static final Logger log = LoggerFactory.getLogger(SchemaProviderTool.class);
  private final VectorStore vectorStore;

  public SchemaProviderTool(VectorStore vectorStore, KnowledgeBase knowledgeBase) {
    String metaData = knowledgeBase.getVectorMetaData();
    log.info("initializing SchemaProvider tool. metaData : {}", metaData);
    this.vectorStore = vectorStore;
  }

  /**
   * Processes a question and returns a response based on the knowledge base content. This method
   * performs a vector search using the provided question and returns the most relevant answer found
   * in the document store.
   *
   * @param request The request containing the question to be answered
   * @return A Response object containing the answer retrieved from the knowledge base
   */
  @Override
  public Response apply(Request request) {
    log.info("inside function KnowledgeBase. request : {}", request.tableName());
    String filterExpression = String.format("'doc_id' == '%s'", request.tableName());
    log.info("filterExpression for SchemaProviderTool : {}", filterExpression);
    SearchRequest searchRequest =
        SearchRequest.builder()
            .filterExpression(filterExpression)
            .query(request.tableName())
            .build();

    List<Document> documents = vectorStore.similaritySearch(searchRequest);
    String tableSchema =
        Optional.ofNullable(documents).orElse(List.of()).stream()
            .map(Document::getText)
            .collect(Collectors.joining());
    return new Response(tableSchema);
  }

  /**
   * Record representing a tableName request to the knowledge base.
   *
   * @param tableName The table name for which schema will be provided.
   */
  public record Request(
      @JsonProperty("The table name for which schema will be provided") String tableName) {}

  /**
   * Record representing the response from the knowledge base.
   *
   * @param result The answer retrieved from the knowledge base
   */
  public record Response(@JsonProperty("result") String result) {}
}
