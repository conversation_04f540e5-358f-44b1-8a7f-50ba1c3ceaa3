/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dao.TestCaseDao;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseResponseDto;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.service.TestCaseService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.IdUtil;
import com.enttribe.promptanalyzer.util.TestCaseUtils;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * Implementation of the {@link TestCaseService} interface. This class provides comprehensive
 * management of test cases including creation, updates, deletion, and search operations. It handles
 * the persistence and retrieval of test cases, supporting both basic CRUD operations and advanced
 * search capabilities with filtering and pagination.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TestCaseServiceImpl implements TestCaseService {

  private final TestCaseDao testCaseDao;
  private final CustomFilter customFilter;
  private final PromptDao promptDao;
  private final CustomerInfo customerInfo;

  private static final List<String> csvColumnHeader =
      List.of("TestcaseId", "InputJson", "Assertions", "Remark", "PromptId");
  private static final int MAX_IMPORT_RECORDS = 1000;

  @Override
  public Map<String, String> create(TestCaseRequestDto requestDto) {
    log.debug("Creating test case");
    try {
      TestCase testCase = TestCaseUtils.getTestCase(requestDto, promptDao);
      testCase.setDeleted(false);
      TestCase saved = testCaseDao.save(testCase);
      saved.setTestcaseId(IdUtil.getTestCaseId(saved.getId()));

      testCase.setCreatedTime(new Date());
      testCase.setNanoId(NanoIdUtils.randomNanoId());
      testCaseDao.save(testCase);
      log.info("Test case created successfully with ID: {}", saved.getId());
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error while creating test case: {}", e.getMessage(), e);
      throw new BusinessException("Unable to create test case");
    }
  }

  @Override
  public Map<String, String> createBatch(List<TestCaseRequestDto> requestDtos) {
    log.debug("Starting batch creation of test cases");
    if (requestDtos == null || requestDtos.isEmpty()) {
      log.error("No test case data provided for batch creation");
      throw new ResourceNotFoundException("no data is provided");
    }

    try {
      List<TestCase> testCases =
          requestDtos.stream().map(dto -> TestCaseUtils.getTestCase(dto, promptDao)).toList();

      List<TestCase> savedTestCases = testCaseDao.saveAll(testCases);
      for (TestCase testCase : savedTestCases) {
        testCase.setTestcaseId(IdUtil.getTestCaseId(testCase.getId()));
        testCase.setDeleted(false);
        testCase.setNanoId(NanoIdUtils.randomNanoId());
      }
      savedTestCases = testCaseDao.saveAll(testCases);

      boolean isSuccess = savedTestCases.size() == testCases.size(); // basic validation
      log.info(
          "Batch creation completed. Created {}/{} test cases successfully",
          savedTestCases.size(),
          testCases.size());

      return Map.of(
          PromptConstants.RESULT,
          isSuccess ? PromptConstants.SUCCESS : PromptConstants.PARTIAL_SUCCESS);
    } catch (Exception e) {
      log.error("Error while creating test cases: {}", e.getMessage(), e);
      throw new BusinessException("Unable to create test cases");
    }
  }

  @Override
  public Map<String, String> update(TestCaseRequestDto updatedTestcase) {
    log.debug("Updating test case with ID: {}", updatedTestcase.getId());
    Optional<TestCase> existingTestcaseOptional = testCaseDao.findByNanoId(updatedTestcase.getId());

    if (existingTestcaseOptional.isEmpty()) {
      log.warn("Test case with ID {} not found for update", updatedTestcase.getId());
      throw new ResourceNotFoundException("Test case not found");
    }

    try {
      TestCase existingTestcase = existingTestcaseOptional.get();
      if (updatedTestcase.getInputJson() != null) {
        existingTestcase.setInputJson(updatedTestcase.getInputJson());
      }
      if (updatedTestcase.getRemark() != null) {
        existingTestcase.setRemark(updatedTestcase.getRemark());
      }
      if (updatedTestcase.getPrompt() != null && updatedTestcase.getPrompt().getId() != null) {
        Prompt prompt =
            promptDao
                .findByNanoId(updatedTestcase.getPrompt().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Prompt not found for test case"));
        existingTestcase.setPrompt(prompt);
      }
      if (updatedTestcase.getAssertions() != null) {
        existingTestcase.setAssertions(updatedTestcase.getAssertions());
      }

      existingTestcase.setModifiedTime(new Date());
      testCaseDao.save(existingTestcase);
      log.info("Test case with ID {} updated successfully", existingTestcase.getNanoId());

      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error while updating test case: {}", e.getMessage(), e);
      throw new BusinessException("Unable to update test case");
    }
  }

  @Override
  public Map<String, String> deleteTestcase(String id) {
    log.debug("Attempting to delete test case with ID: {}", id);
    Optional<TestCase> testcaseOpt = testCaseDao.findByNanoId(id);

    if (testcaseOpt.isEmpty()) {
      log.warn("Test case with ID {} not found for deletion", id);
      throw new BusinessException("Test case not found");
    }
    try {
      TestCase testcase = testcaseOpt.get();
      customFilter.verifyCreator(customerInfo.getUserId(), testcase.getCreator());
      // Enforce creator-only soft delete
      testcase.setDeleted(true);
      testcase.setModifiedTime(new Date());
      testCaseDao.save(testcase);
      log.info("Test case with ID {} marked as deleted", id);

      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (Exception e) {
      log.error("Error while deleting test case: {}", e.getMessage(), e);
      throw new BusinessException("Unable to delete test case");
    }
  }

  @Override
  public List<TestCaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug(
        "Searching test cases with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
        filter,
        offset,
        size,
        orderBy,
        orderType);
    try {
      List<TestCase> results =
          customFilter.searchByFilter(TestCase.class, filter, orderBy, orderType, offset, size);
      log.debug("Found {} test cases matching search criteria", results.size());
      return TestCaseUtils.getTestCaseResponseDtoList(results);
    } catch (Exception e) {
      log.error("Error while searching test cases: {}", e.getMessage(), e);
      throw new BusinessException("Unable to search test cases");
    }
  }

  @Override
  public Long count(String filter) {
    log.debug("Counting test cases with filter: {}", filter);
    try {
      Long count = customFilter.countByFilter(TestCase.class, filter);
      log.debug("Found {} test cases matching count criteria", count);
      return count;
    } catch (Exception e) {
      log.error("Error while counting test cases: {}", e.getMessage(), e);
      throw new BusinessException("Unable to count test cases", e);
    }
  }

  @Override
  public ResponseEntity<Resource> importTestCase(MultipartFile file) {
    log.debug("Starting test case import from file: {}", file.getOriginalFilename());
    try {
      Function<CSVRecord, TestCase> recordTransformer =
          csvRecord -> {
            TestCase testCase = new TestCase();
            testCase.setTestcaseId(csvRecord.get("TestcaseId"));
            testCase.setInputJson(csvRecord.get("InputJson"));
            testCase.setAssertions(csvRecord.get("Assertions"));
            testCase.setRemark(csvRecord.get("Remark"));

            String promptId = csvRecord.get("PromptId");
            if (promptId != null && !promptId.isEmpty()) {
              Prompt prompt = promptDao.findByPromptId(promptId);
              if (prompt == null) {
                log.error("Prompt not found with ID: {}", promptId);
                throw new ResourceNotFoundException("Prompt not found");
              }
              testCase.setPrompt(prompt);
            }

            return testCase;
          };

      String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
      log.info("Importing test cases from file: {}", exportedFileName);
      return CSVUtils.importCSV(
          file,
          csvColumnHeader,
          recordTransformer,
          this::saveOrUpdateTestCase,
          exportedFileName,
          MAX_IMPORT_RECORDS);
    } catch (Exception e) {
      log.error("Error during test case import: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(new ByteArrayResource(e.getMessage().getBytes()));
    }
  }

  private void saveOrUpdateTestCase(TestCase testCase) {
    log.debug("Saving or updating test case with TestCase ID: {}", testCase.getTestcaseId());
    try {
      Optional<TestCase> existingTestCaseOpt =
          testCaseDao.findByTestcaseId(testCase.getTestcaseId());

      if (existingTestCaseOpt.isPresent()) {
        TestCase existingTestCase = existingTestCaseOpt.get();
        existingTestCase.setInputJson(testCase.getInputJson());
        existingTestCase.setAssertions(testCase.getAssertions());
        existingTestCase.setRemark(testCase.getRemark());
        existingTestCase.setPrompt(testCase.getPrompt());
        existingTestCase.setModifiedTime(new Date());
        testCaseDao.save(existingTestCase);
        log.info("Updating existing test case with ID: {}", testCase.getTestcaseId());
      } else {
        testCase.setDeleted(false);
        testCase.setModifiedTime(new Date());
        testCase.setCreatedTime(new Date());
        testCase.setNanoId(NanoIdUtils.randomNanoId());
        log.info("Creating new test case with ID: {}", testCase.getTestcaseId());
        testCaseDao.save(testCase);
      }
      log.info("Test case saved or updated successfully with ID: {}", testCase.getTestcaseId());
    } catch (Exception e) {
      log.error(
          "Error while saving/updating test case with ID {}: {}",
          testCase.getTestcaseId(),
          e.getMessage(),
          e);
      throw new BusinessException("Unable to save/update test case");
    }
  }

  @Override
  public ResponseEntity<Resource> exportTestCasesByIds(List<String> testCaseIds) {
    log.debug("Starting export of test cases for IDs: {}", testCaseIds);
    try {
      List<TestCase> testCaseList = testCaseDao.findAllByNanoIds(testCaseIds);
      log.debug("Retrieved {} test cases for export", testCaseList.size());
      return getResponse("testcases", testCaseList);
    } catch (Exception e) {
      log.error("Error during test case export: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(new ByteArrayResource(("Export failed: " + e.getMessage()).getBytes()));
    }
  }

  private static ResponseEntity<Resource> getResponse(
      String fileName, List<TestCase> testCaseList) {
    log.debug("Preparing CSV export for {} test cases", testCaseList.size());
    try {
      List<Function<TestCase, Object>> fieldExtractors = getTestCaseFieldExtractors();
      log.debug("Going to export test cases as CSV");
      return CSVUtils.exportCSV(testCaseList, csvColumnHeader, fileName, fieldExtractors);
    } catch (Exception e) {
      log.error("Error generating CSV export: {}", e.getMessage(), e);
      throw new BusinessException("Unable to generate CSV export");
    }
  }

  private static List<Function<TestCase, Object>> getTestCaseFieldExtractors() {
    log.debug("Getting field extractors for test case export");
    try {
      List<Function<TestCase, Object>> extractors =
          List.of(
              TestCase::getTestcaseId,
              TestCase::getInputJson,
              TestCase::getAssertions,
              TestCase::getRemark,
              testCase -> testCase.getPrompt().getPromptId());
      log.debug("Successfully created field extractors for test case export");
      return extractors;
    } catch (Exception e) {
      log.error("Error creating field extractors: {}", e.getMessage(), e);
      throw new BusinessException("Unable to create field extractors");
    }
  }
}
