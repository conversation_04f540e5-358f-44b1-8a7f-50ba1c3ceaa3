/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import java.util.List;
import java.util.Map;

public interface TestExecutionServiceV1 {

  List<String> getInputSchema(String name, String type);

  void executeTestSuite(String testSuiteId, long testResultId, String provider);

  void reExecuteTest(Map<String, Object> request);
}
