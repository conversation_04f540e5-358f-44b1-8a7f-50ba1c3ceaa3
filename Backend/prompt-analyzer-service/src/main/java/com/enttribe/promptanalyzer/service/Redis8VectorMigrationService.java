/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.service;

import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import com.enttribe.promptanalyzer.vectorstore.Redis8VectorStore;

/**
 * Service for migrating data between Redis Stack and Redis 8 vector stores
 * and providing utilities for Redis 8 vector operations.
 * 
 * This service helps with:
 * - Migrating existing vector data from Redis Stack to Redis 8
 * - Monitoring vector store performance
 * - Providing Redis 8 specific utilities
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Service
@ConditionalOnProperty(name = "vector.store.type", havingValue = "redis8")
public class Redis8VectorMigrationService {

    private static final Logger log = LoggerFactory.getLogger(Redis8VectorMigrationService.class);

    @Autowired
    @Qualifier("redis8VectorStore")
    private VectorStore redis8VectorStore;

    @Autowired
    private JedisPool jedisPool;

    /**
     * Migrate documents from Redis Stack to Redis 8 vector store
     * 
     * @param documents list of documents to migrate
     * @return number of successfully migrated documents
     */
    public int migrateDocuments(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            log.info("No documents to migrate");
            return 0;
        }

        log.info("Starting migration of {} documents to Redis 8 vector store", documents.size());
        
        try {
            redis8VectorStore.add(documents);
            log.info("Successfully migrated {} documents to Redis 8", documents.size());
            return documents.size();
            
        } catch (Exception e) {
            log.error("Error migrating documents to Redis 8 vector store", e);
            throw new RuntimeException("Migration failed", e);
        }
    }

    /**
     * Test Redis 8 vector store functionality with a sample query
     * 
     * @param testQuery query to test with
     * @return search results
     */
    public List<Document> testVectorSearch(String testQuery) {
        log.info("Testing Redis 8 vector search with query: {}", testQuery);
        
        try {
            SearchRequest searchRequest = SearchRequest.builder()
                .query(testQuery)
                .topK(5)
                .similarityThreshold(0.7)
                .build();
                
            List<Document> results = redis8VectorStore.similaritySearch(searchRequest);
            log.info("Redis 8 vector search returned {} results", results.size());
            
            return results;
            
        } catch (Exception e) {
            log.error("Error testing Redis 8 vector search", e);
            throw new RuntimeException("Vector search test failed", e);
        }
    }

    /**
     * Get Redis 8 vector store statistics
     *
     * @param vectorSetName name of the vector set to analyze
     * @return statistics map
     */
    public Map<String, Object> getVectorStoreStats(String vectorSetName) {
        log.debug("Getting statistics for Redis 8 vector set: {}", vectorSetName);

        try (Jedis jedis = jedisPool.getResource()) {
            // Use Redis INFO command to get vector set information
            String info = jedis.info("vectorsets");

            // Get vector set cardinality using VCARD command
            Object cardResult = jedis.sendCommand(Redis8VectorStore.VectorCommand.VCARD, vectorSetName);
            Long cardinality = cardResult != null ? Long.parseLong(cardResult.toString()) : 0L;

            // Parse and return relevant statistics
            Map<String, Object> stats = Map.of(
                "vectorSetName", vectorSetName,
                "cardinality", cardinality,
                "redisInfo", info,
                "timestamp", System.currentTimeMillis()
            );

            log.debug("Retrieved statistics for vector set: {}", vectorSetName);
            return stats;

        } catch (Exception e) {
            log.error("Error getting Redis 8 vector store statistics", e);
            throw new RuntimeException("Failed to get vector store statistics", e);
        }
    }

    /**
     * Check if Redis 8 vector sets are supported
     *
     * @return true if Redis 8 vector sets are available
     */
    public boolean isRedis8VectorSetsSupported() {
        try (Jedis jedis = jedisPool.getResource()) {
            // Try to execute a simple vector set command to check support
            Object result = jedis.sendCommand(redis.clients.jedis.Protocol.Command.COMMAND,
                                             "INFO", "VADD");

            log.info("Redis 8 vector sets are supported");
            return true;

        } catch (Exception e) {
            log.warn("Redis 8 vector sets are not supported: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Clean up a vector set (useful for testing)
     *
     * @param vectorSetName name of the vector set to clean
     * @return true if successful
     */
    public boolean cleanupVectorSet(String vectorSetName) {
        log.info("Cleaning up Redis 8 vector set: {}", vectorSetName);

        try (Jedis jedis = jedisPool.getResource()) {
            // Delete the entire vector set
            Long result = jedis.del(vectorSetName);

            boolean success = result != null && result > 0;
            log.info("Vector set {} cleanup {}", vectorSetName, success ? "successful" : "failed");

            return success;

        } catch (Exception e) {
            log.error("Error cleaning up Redis 8 vector set: {}", vectorSetName, e);
            return false;
        }
    }

    /**
     * Validate Redis 8 configuration
     * 
     * @return validation results
     */
    public Map<String, Object> validateConfiguration() {
        log.info("Validating Redis 8 vector store configuration");
        
        boolean vectorSetsSupported = isRedis8VectorSetsSupported();
        boolean connectionHealthy = testRedisConnection();
        
        Map<String, Object> validation = Map.of(
            "vectorSetsSupported", vectorSetsSupported,
            "connectionHealthy", connectionHealthy,
            "configurationValid", vectorSetsSupported && connectionHealthy,
            "timestamp", System.currentTimeMillis()
        );
        
        log.info("Redis 8 configuration validation: {}", validation);
        return validation;
    }

    /**
     * Test Redis connection health
     *
     * @return true if connection is healthy
     */
    private boolean testRedisConnection() {
        try (Jedis jedis = jedisPool.getResource()) {
            String pong = jedis.ping();
            return "PONG".equals(pong);

        } catch (Exception e) {
            log.error("Redis connection test failed", e);
            return false;
        }
    }

    /**
     * Get the Redis 8 vector store instance
     * 
     * @return the vector store instance
     */
    public VectorStore getVectorStore() {
        return redis8VectorStore;
    }
}
