/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.dao.AgentTestCaseDao;
import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dao.TestCaseResultDao;
import com.enttribe.promptanalyzer.dao.TestResultDao;
import com.enttribe.promptanalyzer.dao.TestSuiteDao;
import com.enttribe.promptanalyzer.dao.ToolDao;
import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.AgentTestCase;
import com.enttribe.promptanalyzer.model.AgentTestCaseAssertion;
import com.enttribe.promptanalyzer.model.Assertion;
import com.enttribe.promptanalyzer.model.AssertionResult;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCaseResult;
import com.enttribe.promptanalyzer.model.TestResult;
import com.enttribe.promptanalyzer.model.TestSuite;
import com.enttribe.promptanalyzer.model.Tool;
import com.enttribe.promptanalyzer.service.TestExecutionServiceV1;
import com.enttribe.promptanalyzer.util.ChatModelUtils;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.TemplateUtils;
import com.enttribe.promptanalyzer.util.test_case.KnowledgeBaseUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.transaction.Transactional;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AbstractMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.metadata.ChatResponseMetadata;
import org.springframework.ai.chat.metadata.EmptyUsage;
import org.springframework.ai.chat.metadata.Usage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TestExecutionServiceV1Impl implements TestExecutionServiceV1 {

  public static final double TEMPERATURE_FOR_EVAL = 0.1;
  private static final double PERCENTAGE_MULTIPLIER = 100.0;
  public static final int ACCURACY_THRESHOLD = 60;

  @Value("classpath:template/testing/test_case_evaluator_system.st")
  private Resource llmResponseEvaluatorSystem;

  @Value("classpath:template/testing/test_case_evaluator_user.st")
  private Resource llmResponseEvaluatorUser;

  private final AgentTestCaseDao agentTestCaseDao;
  private final ToolCallbackService toolCallbackService;
  private final InferenceManager inferenceManager;
  private final TestSuiteDao testSuiteDao;
  private final ToolDao toolDao;
  private final KnowledgeBaseDao knowledgeBaseDao;
  private final ChatClient chatClient;
  private final TestResultDao testResultDao;
  private final TestCaseResultDao testCaseResultDao;
  private final TestCaseResultBatchService batchService;
  private final KnowledgeBaseUtils knowledgeBaseUtils;
  private final TestCaseAssertionProcessor assertionProcessor;
  private final CustomerInfo customerInfo;

  public TestExecutionServiceV1Impl(
      AgentTestCaseDao agentTestCaseDao,
      ToolCallbackService toolCallbackService,
      InferenceManager inferenceManager,
      TestSuiteDao testSuiteDao,
      ToolDao toolDao,
      KnowledgeBaseDao knowledgeBaseDao,
      TestResultDao testResultDao,
      TestCaseResultDao testCaseResultDao,
      TestCaseResultBatchService batchService,
      KnowledgeBaseUtils knowledgeBaseUtils,
      CustomerInfo customerInfo) {
    this.agentTestCaseDao = agentTestCaseDao;
    this.toolCallbackService = toolCallbackService;
    this.inferenceManager = inferenceManager;
    this.testSuiteDao = testSuiteDao;
    this.toolDao = toolDao;
    this.knowledgeBaseDao = knowledgeBaseDao;
    this.testResultDao = testResultDao;
    this.testCaseResultDao = testCaseResultDao;
    this.batchService = batchService;
    this.knowledgeBaseUtils = knowledgeBaseUtils;
    this.customerInfo = customerInfo;

    ChatModel chatModel = inferenceManager.getChatModelByProvider("groq");
    OpenAiChatOptions chatOptions =
        OpenAiChatOptions.builder()
            .model("llama-3.3-70b-versatile")
            .temperature(TEMPERATURE_FOR_EVAL)
            .responseFormat(ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build())
            .build();
    this.chatClient = ChatClient.builder(chatModel).defaultOptions(chatOptions).build();
    this.assertionProcessor = new TestCaseAssertionProcessor(this::getLlmEval);
  }

  @Async
  @Transactional
  public void executeTestSuite(String testSuiteId, long testResultId, String provider) {
    log.debug("Inside @method executeTestSuite. @param -> agentTestSuiteId : {}", testSuiteId);

    TestSuite testSuite = getTestSuite(testSuiteId);
    TestResult testResult = getTestResult(testResultId);

    try {
      TestSuiteExecutionResult executionResult =
          executeAllTestCases(testSuite, testResult, provider);
      updateTestResultWithMetrics(testResult, testSuite, executionResult);
      testResult.setStatus("COMPLETED");
      testResultDao.save(testResult);
    } catch (Exception e) {
      log.error("error inside @method executeTestSuite : {}", e.getMessage(), e);
      testResult.setStatus("FAILED");
      testResultDao.save(testResult);
    }
  }

  private TestSuite getTestSuite(String testSuiteId) {
    return testSuiteDao
        .findByNanoIdWithAll(testSuiteId)
        .orElseThrow(() -> new ResourceNotFoundException("test suite not found"));
  }

  private TestResult getTestResult(long testResultId) {
    return testResultDao
        .findById(testResultId)
        .orElseThrow(() -> new ResourceNotFoundException("test result not found"));
  }

  private TestSuiteExecutionResult executeAllTestCases(
      TestSuite testSuite, TestResult testResult, String provider) {
    final int BATCH_SIZE = 20;
    List<TestCaseResult> batch = new ArrayList<>(BATCH_SIZE);
    Map<Long, List<ConsistencyRecord>> resultsByTestCase = new HashMap<>();
    TestSuiteMetrics metrics = new TestSuiteMetrics();

    Set<AgentTestCase> agentTestCases = testSuite.getAgentTestCases();

    for (int runNumber = 0; runNumber < testResult.getNumberOfRuns(); runNumber++) {
      for (AgentTestCase agentTestCase : agentTestCases) {
        if (agentTestCase.isDeleted()) {
          log.info("test case : {} is deleted. moving to next", agentTestCase.getName());
          continue;
        }

        TestCaseResult testCaseResult =
            executeTestCaseWithErrorHandling(
                agentTestCase, provider, testResult.getModel(), runNumber + 1, testResult);

        batch.add(testCaseResult);
        updateMetrics(metrics, testCaseResult);
        updateConsistencyTracking(resultsByTestCase, testCaseResult);

        if (batch.size() >= BATCH_SIZE) {
          batchService.saveBatch(batch);
          batch.clear();
        }
      }
    }

    // Save any remaining results
    if (!batch.isEmpty()) {
      batchService.saveBatch(batch);
    }

    double averageConsistency = getAverageConsistency(resultsByTestCase);
    return new TestSuiteExecutionResult(metrics, averageConsistency);
  }

  private TestCaseResult executeTestCaseWithErrorHandling(
      AgentTestCase agentTestCase,
      String provider,
      String model,
      int runNumber,
      TestResult testResult) {
    try {
      TestCaseResult result =
          executeTest(agentTestCase.getNanoId(), provider, model, new TestCaseResult());
      result.setRunNumber(runNumber);
      result.setTestResult(testResult);
      return result;
    } catch (Exception e) {
      log.error("error in executing test case : {}", e.getMessage(), e);
      return createErrorTestCaseResult(agentTestCase, e, runNumber, testResult);
    }
  }

  private TestCaseResult createErrorTestCaseResult(
      AgentTestCase agentTestCase, Exception e, int runNumber, TestResult testResult) {
    return TestCaseResult.builder()
        .isPassed(false)
        .isError(true)
        .failureReason(e.getMessage())
        .agentTestCase(agentTestCase)
        .nanoId(NanoIdUtils.randomNanoId())
        .accuracy(0.0)
        .latency(0L)
        .promptToken(0)
        .generationTokens(0)
        .runNumber(runNumber)
        .testResult(testResult)
        .build();
  }

  private void updateMetrics(TestSuiteMetrics metrics, TestCaseResult testCaseResult) {
    metrics.addPromptTokens(testCaseResult.getPromptToken());
    metrics.addGenerationTokens(testCaseResult.getGenerationTokens());
    metrics.addAccuracy(testCaseResult.getAccuracy());
    metrics.addLatency(testCaseResult.getLatency());

    if (testCaseResult.isPassed()) {
      metrics.incrementTestsPassed();
    } else {
      metrics.incrementTestsFailed();
    }
  }

  private void updateConsistencyTracking(
      Map<Long, List<ConsistencyRecord>> resultsByTestCase, TestCaseResult testCaseResult) {
    resultsByTestCase
        .computeIfAbsent(testCaseResult.getAgentTestCase().getId(), k -> new ArrayList<>())
        .add(new ConsistencyRecord(testCaseResult.isPassed()));
  }

  private void updateTestResultWithMetrics(
      TestResult testResult, TestSuite testSuite, TestSuiteExecutionResult executionResult) {
    TestSuiteMetrics metrics = executionResult.metrics();
    int totalTests = metrics.getTestsPassed() + metrics.getTestsFailed();
    int size = totalTests == 0 ? 1 : totalTests;

    testResult.setTestSuite(testSuite);
    testResult.setAccuracy(metrics.getTotalAccuracy() / size);
    testResult.setLatency(metrics.getTotalLatency() / size);
    testResult.setConsistency(executionResult.averageConsistency() * PERCENTAGE_MULTIPLIER);
    testResult.setTestCasesPassed(metrics.getTestsPassed());
    testResult.setTestCasesFailed(metrics.getTestsFailed());
    testResult.setModifiedTime(new Date());
    testResult.setPromptToken(metrics.getTotalPromptTokens() / size);
    testResult.setGenerationTokens(metrics.getTotalGenerationTokens() / size);
  }

  private static String removeThinkBlock(String input) {
    if (input == null) {
      return null;
    }
    return input.replaceFirst("(?s)<think>.*?</think>", "").trim();
  }

  @Override
  public void reExecuteTest(Map<String, Object> request) {
    String testCaseId = (String) request.get("testCaseResultId");
    log.debug("Inside @method reExecuteFailedTest. @param -> testCaseResultId : {}", testCaseId);
    TestCaseResult testCaseResult =
        testCaseResultDao
            .findByNanoId(testCaseId)
            .orElseThrow(() -> new ResourceNotFoundException("TestCase result not found"));

    try {
      AgentTestCase agentTestCase = testCaseResult.getAgentTestCase();
      TestResult testResult = testCaseResult.getTestResult();
      TestCaseResult reExecutedResult =
          executeTest(
              agentTestCase.getNanoId(),
              testResult.getProvider(),
              testResult.getModel(),
              testCaseResult);
      reExecutedResult.setError(false);
      reExecutedResult.setNanoId(testCaseId);
      testCaseResultDao.save(reExecutedResult);
      reCalculateTestResult(testResult.getNanoId());
    } catch (Exception e) {
      log.error("error in reExecuteFailedTest : {}", e.getMessage(), e);
      throw new BusinessException("failed to re-execute test case", e);
    }
  }

  private void reCalculateTestResult(String testResultNanoId) {
    log.debug(
        "Inside @method reCalculateTestResult. @param -> testResultNanoId : {}", testResultNanoId);
    try {
      TestResult testResult =
          testResultDao
              .findWithResultsByNanoId(testResultNanoId)
              .orElseThrow(() -> new ResourceNotFoundException("test result not found"));

      Set<TestCaseResult> testCaseResults = testResult.getTestCaseResults();
      TestSuiteMetrics metrics = calculateTestSuiteMetrics(testCaseResults);
      Map<Long, List<ConsistencyRecord>> resultsByTestCase = buildConsistencyMap(testCaseResults);
      double averageConsistency = getAverageConsistency(resultsByTestCase);

      updateTestResultMetrics(testResult, metrics, averageConsistency, testCaseResults.size());
      testResultDao.save(testResult);
    } catch (ResourceNotFoundException e) {
      log.error("error in recalculating : {}", e.getMessage(), e);
    }
  }

  private TestSuiteMetrics calculateTestSuiteMetrics(Set<TestCaseResult> testCaseResults) {
    TestSuiteMetrics metrics = new TestSuiteMetrics();

    for (TestCaseResult tcr : testCaseResults) {
      if (tcr.isPassed()) {
        metrics.incrementTestsPassed();
      } else {
        metrics.incrementTestsFailed();
      }
      metrics.addAccuracy(tcr.getAccuracy());
      metrics.addLatency(tcr.getLatency());
    }

    return metrics;
  }

  private Map<Long, List<ConsistencyRecord>> buildConsistencyMap(
      Set<TestCaseResult> testCaseResults) {
    Map<Long, List<ConsistencyRecord>> resultsByTestCase = new HashMap<>();

    for (TestCaseResult tcr : testCaseResults) {
      resultsByTestCase
          .computeIfAbsent(tcr.getAgentTestCase().getId(), k -> new ArrayList<>())
          .add(new ConsistencyRecord(tcr.isPassed()));
    }

    return resultsByTestCase;
  }

  private void updateTestResultMetrics(
      TestResult testResult,
      TestSuiteMetrics metrics,
      double averageConsistency,
      int totalResults) {
    int size = totalResults == 0 ? 1 : totalResults;

    testResult.setAccuracy(metrics.getTotalAccuracy() / size);
    testResult.setLatency(metrics.getTotalLatency() / size);
    testResult.setConsistency(averageConsistency * PERCENTAGE_MULTIPLIER);
    testResult.setTestCasesPassed(metrics.getTestsPassed());
    testResult.setTestCasesFailed(metrics.getTestsFailed());
    testResult.setModifiedTime(new Date());
  }

  private static double getAverageConsistency(
      Map<Long, List<ConsistencyRecord>> resultsByTestCase) {
    double totalConsistency = 0.0;
    for (List<ConsistencyRecord> results : resultsByTestCase.values()) {
      int passed = 0;
      int failed = 0;
      for (ConsistencyRecord tcr : results) {
        if (tcr.isPassed()) {
          passed++;
        } else {
          failed++;
        }
      }
      int majority = Math.max(passed, failed);
      double consistencyForTestCase =
          results.isEmpty() ? 0.0 : ((double) majority) / results.size();
      totalConsistency += consistencyForTestCase;
    }
    return resultsByTestCase.isEmpty() ? 0.0 : totalConsistency / resultsByTestCase.size();
  }

  record ConsistencyRecord(boolean isPassed) {}

  record TestSuiteExecutionResult(TestSuiteMetrics metrics, double averageConsistency) {}

  public TestCaseResult executeTest(
      String agentTestCaseId, String provider, String model, TestCaseResult testCaseResult) {
    log.debug("Inside @method executeTest. @param -> agentTestCaseId : {}", agentTestCaseId);

    AgentTestCase agentTestCase = getAgentTestCase(agentTestCaseId);
    TestExecutionContext context = buildExecutionContext(agentTestCase, provider, model);
    ChatResponse chatResponse = executeChatRequest(context);

    String llmResponse = extractLlmResponse(chatResponse);
    List<AssertionResult> assertionResults =
        processAssertions(agentTestCase, llmResponse, context.getUserQuestion(), testCaseResult);

    return buildTestCaseResult(
        testCaseResult,
        agentTestCase,
        chatResponse,
        assertionResults,
        context.getTimeTakenMillis());
  }

  private AgentTestCase getAgentTestCase(String agentTestCaseId) {
    return agentTestCaseDao
        .findByNanoId(agentTestCaseId)
        .orElseThrow(() -> new ResourceNotFoundException("Test case not found"));
  }

  private TestExecutionContext buildExecutionContext(
      AgentTestCase agentTestCase, String provider, String model) {
    Prompt prompt = agentTestCase.getPrompt();
    String systemMessage = extractSystemMessage(prompt);
    String resolvedPrompt =
        TemplateUtils.getResolvedPrompt(systemMessage, agentTestCase.getVariableMap());

    List<ToolCallback> toolCallbacks =
        toolCallbackService.registerToolCallback(new ArrayList<>(agentTestCase.getTools()));
    List<ToolCallback> knowledgeBaseTools =
        knowledgeBaseUtils.getKnowledgeBaseToolsV1(agentTestCase.getKnowledgeBases());

    List<Message> messageList = buildMessageList(resolvedPrompt, agentTestCase.getHistory());
    ChatModel chatModel = inferenceManager.getChatModelByProvider(provider);
    boolean shouldExecuteTool = shouldExecuteTool(agentTestCase.getAgentTestCaseAssertions());

    return TestExecutionContext.builder()
        .prompt(prompt)
        .messageList(messageList)
        .chatModel(chatModel)
        .userQuestion(agentTestCase.getUserQuestion())
        .model(model)
        .toolCallbacks(toolCallbacks)
        .knowledgeBaseTools(knowledgeBaseTools)
        .shouldExecuteTool(shouldExecuteTool)
        .build();
  }

  private String extractSystemMessage(Prompt prompt) {
    return prompt.getMessages().stream()
        .filter(message -> message.getRole().equals("system"))
        .findFirst()
        .orElseThrow(() -> new ResourceNotFoundException("System prompt not found"))
        .getContent();
  }

  private List<Message> buildMessageList(String resolvedPrompt, String history) {
    List<Message> messages = ChatModelUtils.getMessageForTest(history);
    ArrayList<Message> messageList = new ArrayList<>();
    messageList.add(new SystemMessage(resolvedPrompt));
    if (!messages.isEmpty()) {
      messageList.addAll(messages);
    }
    return messageList;
  }

  private ChatResponse executeChatRequest(TestExecutionContext context) {
    Instant startTime = Instant.now();

    ChatResponse chatResponse =
        ChatClient.create(context.getChatModel())
            .prompt()
            .messages(context.getMessageList())
            .options(buildChatOptions(context))
            .toolCallbacks(context.getToolCallbacks())
            .toolCallbacks(context.getKnowledgeBaseTools())
            .user(context.getUserQuestion())
            .call()
            .chatResponse();

    Instant endTime = Instant.now();
    context.setTimeTakenMillis(Duration.between(startTime, endTime).toMillis());

    return chatResponse;
  }

  private OpenAiChatOptions buildChatOptions(TestExecutionContext context) {
    String token = customerInfo.getAccessToken() != null ? customerInfo.getAccessToken() : "";
    Prompt prompt = context.getPrompt();
    return OpenAiChatOptions.builder()
        .temperature(prompt.getTemperature())
        .model(context.getModel())
        .maxCompletionTokens(prompt.getMaxToken())
        .toolContext(
            Map.of(
                "shouldExecuteTool",
                context.isShouldExecuteTool(),
                "token",
                Base64.getEncoder().encodeToString(token.getBytes())))
        .topP(prompt.getTopP())
        .build();
  }

  private String extractLlmResponse(ChatResponse chatResponse) {
    String llmResponse =
        Optional.ofNullable(chatResponse)
            .map(ChatResponse::getResult)
            .map(Generation::getOutput)
            .map(AbstractMessage::getText)
            .orElse(null);

    log.debug("llm response for executeTest : {}", llmResponse);
    return removeThinkBlock(llmResponse);
  }

  private List<AssertionResult> processAssertions(
      AgentTestCase agentTestCase,
      String llmResponse,
      String userQuestion,
      TestCaseResult testCaseResult) {
    return assertionProcessor.processAssertions(
        agentTestCase, llmResponse, userQuestion, testCaseResult);
  }

  private TestCaseResult buildTestCaseResult(
      TestCaseResult testCaseResult,
      AgentTestCase agentTestCase,
      ChatResponse chatResponse,
      List<AssertionResult> assertionResults,
      Long timeTakenMillis) {
    testCaseResult.getAssertionResults().clear();
    testCaseResult.getAssertionResults().addAll(new HashSet<>(assertionResults));

    TestCaseMetrics metrics = calculateMetrics(assertionResults);
    Usage usage = extractUsage(chatResponse);

    testCaseResult.setAccuracy(metrics.accuracy());
    testCaseResult.setAssertionsPassed(metrics.assertionsPassed());
    testCaseResult.setAssertionsFailed(metrics.assertionsFailed());
    testCaseResult.setPassed(metrics.isPassed());
    testCaseResult.setLatency(timeTakenMillis);
    testCaseResult.setAgentTestCase(agentTestCase);
    testCaseResult.setCreatedTime(new Date());
    testCaseResult.setNanoId(NanoIdUtils.randomNanoId());
    testCaseResult.setPromptToken(usage.getPromptTokens());
    testCaseResult.setGenerationTokens(usage.getCompletionTokens());

    return testCaseResult;
  }

  private TestCaseMetrics calculateMetrics(List<AssertionResult> assertionResults) {
    double accuracy = 0;
    boolean isPassed = true;
    int assertionsPassed = 0;
    int assertionsFailed = 0;

    for (AssertionResult assertionResult : assertionResults) {
      accuracy += assertionResult.getAccuracyScore();
      if (!assertionResult.isPassed()) {
        isPassed = false;
        assertionsFailed++;
      } else {
        assertionsPassed++;
      }
    }

    int size = (assertionsPassed + assertionsFailed) == 0 ? 1 : assertionsPassed + assertionsFailed;
    return new TestCaseMetrics(accuracy / size, isPassed, assertionsPassed, assertionsFailed);
  }

  private Usage extractUsage(ChatResponse chatResponse) {
    return Optional.ofNullable(chatResponse)
        .map(ChatResponse::getMetadata)
        .map(ChatResponseMetadata::getUsage)
        .orElse(new EmptyUsage());
  }

  private record TestCaseMetrics(
      double accuracy, boolean isPassed, int assertionsPassed, int assertionsFailed) {}

  private boolean shouldExecuteTool(Set<AgentTestCaseAssertion> agentTestCaseAssertions) {
    try {
      for (AgentTestCaseAssertion atca : agentTestCaseAssertions) {
        Assertion assertion = atca.getAssertion();
        if (assertion.getAssertionType().equals(AssertionType.LLM_RESPONSE)) {
          return true;
        }
      }
    } catch (Exception e) {
      log.error("error in checking shouldExecuteTool : {}", e.getMessage(), e);
    }
    return false;
  }

  @Override
  public List<String> getInputSchema(String name, String type) {
    log.debug("Inside @method getInputSchema. @param : name -> {} type -> {}", name, type);
    try {
      String inputSchema = getInputSchemaByType(name, type);
      return extractPropertyKeys(inputSchema);
    } catch (Exception e) {
      log.error("Error inside getInputSchema : {}", e.getMessage(), e);
      throw new BusinessException("failed to get input schema", e);
    }
  }

  private String getInputSchemaByType(String name, String type) {
    if (type == null || type.equalsIgnoreCase("tool")) {
      return getToolInputSchema(name);
    } else {
      return getKnowledgeBaseInputSchema(name);
    }
  }

  private String getToolInputSchema(String name) {
    Tool tool = toolDao.findByToolName(name);
    ToolCallback toolCallback = toolCallbackService.getToolCallback(tool);
    return toolCallback.getToolDefinition().inputSchema();
  }

  private String getKnowledgeBaseInputSchema(String name) {
    KnowledgeBase knowledgeBase = knowledgeBaseDao.findByName(name);
    ToolCallback knowledgeBaseTool = KnowledgeBaseUtils.getKnowledgeBaseTool(knowledgeBase);
    return knowledgeBaseTool.getToolDefinition().inputSchema();
  }

  private static List<String> extractPropertyKeys(String jsonSchema) {
    List<String> keys = new ArrayList<>();
    try {
      ObjectMapper mapper = JsonUtils.getObjectMapper();
      JsonNode root = mapper.readTree(jsonSchema);
      JsonNode propertiesNode = root.path("properties");
      if (propertiesNode.isObject()) {
        Iterator<String> fieldNames = propertiesNode.fieldNames();
        while (fieldNames.hasNext()) {
          keys.add(fieldNames.next());
        }
      }
    } catch (Exception e) {
      log.error("error in getting properties keys : {}", e.getMessage());
    }
    return keys;
  }

  private LlmEval getLlmEval(TestCaseAssertionProcessor.LlmEvalRequest request) {
    return getLlmEval(request.llmResponse(), request.userQuestion(), request.expectedValue());
  }

  private LlmEval getLlmEval(String llmResponse, String userQuestion, String userExpectation) {
    log.debug(
        "inside @method getLlmEval. llmResponse: {}, userQuestion: {}, userExpectation: {}",
        llmResponse,
        userQuestion,
        userExpectation);
    BeanOutputConverter<LlmEval> converter = new BeanOutputConverter<>(LlmEval.class);

    try {
      Map<String, Object> variableMap =
          Map.of(
              "llmResponse",
              llmResponse,
              "format",
              converter.getFormat(),
              "userQuestion",
              userQuestion,
              "userExpectation",
              userExpectation);

      String resolvedSystemPrompt =
          TemplateUtils.getResolvedPrompt(llmResponseEvaluatorSystem, variableMap);
      String resolvedUserPrompt =
          TemplateUtils.getResolvedPrompt(llmResponseEvaluatorUser, variableMap);

      LlmEval llmEval =
          chatClient
              .prompt()
              .system(resolvedSystemPrompt)
              .user(resolvedUserPrompt)
              .call()
              .entity(LlmEval.class);
      if (llmEval == null) {
        return new LlmEval(0, "could not evaluate", false);
      }
      boolean isPassed = llmEval.accuracyScore() > ACCURACY_THRESHOLD;
      return new LlmEval(llmEval.accuracyScore(), llmEval.reason(), isPassed);
    } catch (Exception e) {
      log.error("error in getting llm eval : {}", e.getMessage(), e);
      return new LlmEval(0, null, false);
    }
  }

  public record LlmEval(
      @JsonPropertyDescription("factual correctedness of the response") double accuracyScore,
      @JsonPropertyDescription("reasoning for the provided score") String reason,
      @JsonIgnore Boolean isPassed) {}
}
