/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.TagDao;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Tag;
import com.enttribe.promptanalyzer.service.TagService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.TagUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link TagService} interface. This service class provides the business
 * logic for managing tags, including operations for searching, creating, updating, and deleting
 * tags.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TagServiceImpl implements TagService {

  private final CustomFilter customFilter;
  private final TagDao tagDao;
  private final CustomerInfo customerInfo;

  @Override
  public List<TagResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info(
        "Searching tags with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
        filter,
        offset,
        size,
        orderBy,
        orderType);
    try {
      List<Tag> tags =
          customFilter.searchByFilter(Tag.class, filter, orderBy, orderType, offset, size);
      log.debug("Found {} tags matching the search criteria", tags.size());
      return TagUtils.getTagList(tags);
    } catch (Exception e) {
      log.error("Failed to search tags: {}", e.getMessage(), e);
      throw new BusinessException("Failed to search tags");
    }
  }

  @Override
  public Long count(String filter) {
    log.debug("Counting tags with filter: {}", filter);
    try {
      Long count = customFilter.countByFilter(Tag.class, filter);
      log.debug("Found {} tags matching the count criteria", count);
      return count;
    } catch (Exception e) {
      log.error("Failed to count tags: {}", e.getMessage(), e);
      throw new BusinessException("Failed to count tags");
    }
  }

  @Override
  public Map<String, String> save(TagRequestDto dto) {
    log.info("Creating new tag with name: {}", dto.getName());
    Map<String, String> response = new HashMap<>();
    try {
      Tag tag = new Tag();
      TagUtils.mapDtoToEntity(tag, dto);
      tag.setCreatedTime(new Date());
      tag.setModifiedTime(new Date());
      Tag savedTag = tagDao.save(tag);
      log.info("Successfully created tag with ID: {}", savedTag.getId());
      response.put(PromptConstants.STATUS, PromptConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Failed to create tag: {}", e.getMessage(), e);
      throw new BusinessException("Failed to create tag");
    }
    return response;
  }

  @Override
  public Map<String, String> update(TagRequestDto dto) {
    log.info("Updating tag with ID: {}", dto.getId());
    Map<String, String> response = new HashMap<>();
    try {
      Tag tag =
          tagDao
              .findByNanoId(dto.getId())
              .orElseThrow(
                  () -> new ResourceNotFoundException("Tag not found with ID: " + dto.getId()));
      TagUtils.mapDtoToEntity(tag, dto);
      tag.setModifiedTime(new Date());
      Tag updatedTag = tagDao.save(tag);
      log.info("Successfully updated tag with ID: {}", updatedTag.getNanoId());
      response.put(PromptConstants.STATUS, PromptConstants.SUCCESS);
    } catch (ResourceNotFoundException e) {
      log.error("Tag not found for update: {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Failed to update tag: {}", e.getMessage(), e);
      throw new BusinessException("Failed to update tag");
    }
    return response;
  }

  @Override
  public Map<String, String> softDelete(String id) {
    log.info("Attempting to soft delete tag with ID: {}", id);
    Map<String, String> result = new HashMap<>();

    Tag tag =
        tagDao.findByNanoId(id).orElseThrow(() -> new ResourceNotFoundException("Tag not found"));
    try {
      // Enforce creator-only soft delete
      customFilter.verifyCreator(customerInfo.getUserId(), tag.getCreator());
      tag.setDeleted(true);
      tagDao.save(tag);

      log.info("Successfully soft deleted tag with ID: {}", id);
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (ResourceNotFoundException e) {
      log.error("Tag not found for soft delete: {}", e.getMessage(), e);
      throw e;
    } catch (Exception e) {
      log.error("Failed to soft delete tag: {}", e.getMessage(), e);
      throw new BusinessException("Failed to soft delete tag: " + e.getMessage(), e);
    }
    return result;
  }
}
