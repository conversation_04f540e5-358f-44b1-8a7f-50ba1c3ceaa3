/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Processor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Processor entity operations. Extends JpaRepository to provide
 * standard CRUD operations and pagination support for Processor entities.
 *
 * @see Processor
 * @see JpaRepository
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface ProcessorDao extends JpaRepository<Processor, Integer> {

  @Query("SELECT p FROM Processor p WHERE p.nanoId = :nanoId AND p.deleted = false")
  Processor findByNanoId(String nanoId);
}
