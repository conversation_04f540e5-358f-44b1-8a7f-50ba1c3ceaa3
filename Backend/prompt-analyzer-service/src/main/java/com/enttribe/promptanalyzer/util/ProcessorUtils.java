/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.model.Processor;
import java.util.List;

/**
 * Utility class for handling Processor entity and DTO conversions. Provides methods for mapping
 * between Processor entities and DTOs.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ProcessorUtils {

  /** Private constructor to prevent instantiation of utility class. */
  private ProcessorUtils() {}

  /**
   * Maps data from a ProcessorRequestDto to a Processor entity.
   *
   * @param requestDto The source DTO containing processor data
   * @param processor The target Processor entity to be updated
   * @return The updated Processor entity
   */
  public static Processor mapDtoToEntity(ProcessorRequestDto requestDto, Processor processor) {
    processor.setKey(requestDto.getKey());
    processor.setDisplayName(requestDto.getDisplayName());
    processor.setIcon(requestDto.getIcon());
    processor.setStyleType(requestDto.getStyleType());
    processor.setCategory(requestDto.getCategory());
    processor.setJsonStructure(requestDto.getJsonStructure());
    processor.setSubCategory(requestDto.getSubCategory());
    return processor;
  }

  /**
   * Converts a Processor entity to a new ProcessorResponseDto.
   *
   * @param processor The Processor entity to convert
   * @return A new ProcessorResponseDto containing the processor data, or null if the input is null
   */
  public static ProcessorResponseDto convertToDTO(Processor processor) {
    if (processor == null) {
      return null;
    }
    return ProcessorResponseDto.builder()
        .id(processor.getNanoId())
        .key(processor.getKey())
        .icon(processor.getIcon())
        .displayName(processor.getDisplayName())
        .category(processor.getCategory())
        .subCategory(processor.getSubCategory())
        .styleType(processor.getStyleType())
        .jsonStructure(processor.getJsonStructure())
        .build();
  }

  /**
   * Converts a list of Processor entities to a list of ProcessorResponseDtos.
   *
   * @param processors The list of Processor entities to convert
   * @return A list of ProcessorResponseDtos, or an empty list if the input is null or empty
   */
  public static List<ProcessorResponseDto> getProcessorList(List<Processor> processors) {
    if (processors == null || processors.isEmpty()) {
      return List.of();
    }
    return processors.stream().map(ProcessorUtils::convertToDTO).toList();
  }
}
