///*
// * Copyright (c) 2024 Vwaves Technologies Private Limited
// *
// * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
// * Product: Aspose.Total for Java
// * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
// * Order ID: 250610115636
// *
// * This source code is part of a proprietary software system and may not be
// * distributed, modified, or reused without explicit written permission from
// * Vwaves Technologies Private Limited.
// *
// * Contact: <EMAIL>
// */
//package com.enttribe.promptanalyzer.controller;
//
//import com.enttribe.promptanalyzer.service.Redis8VectorMigrationService;
//import java.util.List;
//import java.util.Map;
//
//import com.enttribe.promptanalyzer.vectorstore.Redis8VectorStore;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.ai.document.Document;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * REST Controller for Redis 8 Vector Store operations.
// *
// * Provides endpoints for:
// * - Testing Redis 8 vector functionality
// * - Migrating data from Redis Stack to Redis 8
// * - Monitoring vector store performance
// * - Managing vector sets
// *
// * Only available when vector.store.type=redis8
// *
// * <AUTHOR>
// * @version 1.0
// */
//@RestController
//@RequestMapping("/api/v1/redis8-vector")
//@ConditionalOnProperty(name = "vector.store.type", havingValue = "redis8")
//public class Redis8VectorController {
//
//    private static final Logger log = LoggerFactory.getLogger(Redis8VectorController.class);
//
//    @Autowired
//    private Redis8VectorMigrationService migrationService;
//
//    /**
//     * Test Redis 8 vector search functionality
//     *
//     * @param query test query string
//     * @return search results
//     */
//    @GetMapping("/test-search")
//    public ResponseEntity<Map<String, Object>> testVectorSearch(
//            @RequestParam(defaultValue = "test query") String query) {
//
//        log.info("Testing Redis 8 vector search with query: {}", query);
//
//        try {
//            List<Document> results = migrationService.testVectorSearch(query);
//
//            Map<String, Object> response = Map.of(
//                "success", true,
//                "query", query,
//                "resultCount", results.size(),
//                "results", results.stream()
//                    .map(doc -> Map.of(
//                        "id", doc.getId(),
//                        "content", doc.getText().substring(0, Math.min(100, doc.getText().length())) + "...",
//                        "metadata", doc.getMetadata()
//                    ))
//                    .toList(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("Error testing Redis 8 vector search", e);
//
//            Map<String, Object> errorResponse = Map.of(
//                "success", false,
//                "error", e.getMessage(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.internalServerError().body(errorResponse);
//        }
//    }
//
//    /**
//     * Get Redis 8 vector store statistics
//     *
//     * @param vectorSetName name of the vector set (optional)
//     * @return statistics
//     */
//    @GetMapping("/stats")
//    public ResponseEntity<Map<String, Object>> getVectorStoreStats(
//            @RequestParam(defaultValue = "vector_store_knowledge_base") String vectorSetName) {
//
//        log.info("Getting Redis 8 vector store statistics for: {}", vectorSetName);
//
//        try {
//            Map<String, Object> stats = migrationService.getVectorStoreStats(vectorSetName);
//
//            Map<String, Object> response = Map.of(
//                "success", true,
//                "stats", stats,
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("Error getting Redis 8 vector store statistics", e);
//
//            Map<String, Object> errorResponse = Map.of(
//                "success", false,
//                "error", e.getMessage(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.internalServerError().body(errorResponse);
//        }
//    }
//
//    /**
//     * Validate Redis 8 configuration
//     *
//     * @return validation results
//     */
//    @GetMapping("/validate")
//    public ResponseEntity<Map<String, Object>> validateConfiguration() {
//        log.info("Validating Redis 8 vector store configuration");
//
//        try {
//            Map<String, Object> validation = migrationService.validateConfiguration();
//
//            Map<String, Object> response = Map.of(
//                "success", true,
//                "validation", validation,
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("Error validating Redis 8 configuration", e);
//
//            Map<String, Object> errorResponse = Map.of(
//                "success", false,
//                "error", e.getMessage(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.internalServerError().body(errorResponse);
//        }
//    }
//
//    /**
//     * Check if Redis 8 vector sets are supported
//     *
//     * @return support status
//     */
//    @GetMapping("/support-check")
//    public ResponseEntity<Map<String, Object>> checkVectorSetsSupport() {
//        log.info("Checking Redis 8 vector sets support");
//
//        try {
//            boolean supported = migrationService.isRedis8VectorSetsSupported();
//
//            Map<String, Object> response = Map.of(
//                "success", true,
//                "vectorSetsSupported", supported,
//                "message", supported ? "Redis 8 vector sets are supported" : "Redis 8 vector sets are not supported",
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("Error checking Redis 8 vector sets support", e);
//
//            Map<String, Object> errorResponse = Map.of(
//                "success", false,
//                "error", e.getMessage(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.internalServerError().body(errorResponse);
//        }
//    }
//
//    /**
//     * Migrate documents to Redis 8 vector store
//     *
//     * @param documents list of documents to migrate
//     * @return migration results
//     */
//    @PostMapping("/migrate")
//    public ResponseEntity<Map<String, Object>> migrateDocuments(
//            @RequestBody List<Document> documents) {
//
//        log.info("Migrating {} documents to Redis 8 vector store", documents.size());
//
//        try {
//            int migratedCount = migrationService.migrateDocuments(documents);
//
//            Map<String, Object> response = Map.of(
//                "success", true,
//                "totalDocuments", documents.size(),
//                "migratedCount", migratedCount,
//                "message", "Successfully migrated " + migratedCount + " documents",
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("Error migrating documents to Redis 8", e);
//
//            Map<String, Object> errorResponse = Map.of(
//                "success", false,
//                "error", e.getMessage(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.internalServerError().body(errorResponse);
//        }
//    }
//
//    /**
//     * Clean up a vector set (useful for testing)
//     *
//     * @param vectorSetName name of the vector set to clean
//     * @return cleanup results
//     */
//    @DeleteMapping("/cleanup/{vectorSetName}")
//    public ResponseEntity<Map<String, Object>> cleanupVectorSet(
//            @PathVariable String vectorSetName) {
//
//        log.info("Cleaning up Redis 8 vector set: {}", vectorSetName);
//
//        try {
//            boolean success = migrationService.cleanupVectorSet(vectorSetName);
//
//            Map<String, Object> response = Map.of(
//                "success", success,
//                "vectorSetName", vectorSetName,
//                "message", success ? "Vector set cleaned up successfully" : "Vector set cleanup failed",
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.ok(response);
//
//        } catch (Exception e) {
//            log.error("Error cleaning up Redis 8 vector set", e);
//
//            Map<String, Object> errorResponse = Map.of(
//                "success", false,
//                "error", e.getMessage(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.internalServerError().body(errorResponse);
//        }
//    }
//
//    /**
//     * Debug Redis 8 vector store operations
//     *
//     * @return debug information
//     */
//    @GetMapping("/debug")
//    public ResponseEntity<Map<String, Object>> debugRedis8VectorStore() {
//        log.info("Debugging Redis 8 vector store operations");
//
//        try {
//            if (vectorStore instanceof Redis8VectorStore redis8Store) {
//                Map<String, Object> debugInfo = redis8Store.debugVectorSet();
//
//                Map<String, Object> response = Map.of(
//                    "success", true,
//                    "debugInfo", debugInfo,
//                    "timestamp", System.currentTimeMillis()
//                );
//
//                return ResponseEntity.ok(response);
//            } else {
//                Map<String, Object> errorResponse = Map.of(
//                    "success", false,
//                    "error", "Redis 8 vector store not active. Current: " + vectorStore.getClass().getSimpleName(),
//                    "timestamp", System.currentTimeMillis()
//                );
//
//                return ResponseEntity.badRequest().body(errorResponse);
//            }
//        } catch (Exception e) {
//            log.error("Error debugging Redis 8 vector store", e);
//
//            Map<String, Object> errorResponse = Map.of(
//                "success", false,
//                "error", e.getMessage(),
//                "timestamp", System.currentTimeMillis()
//            );
//
//            return ResponseEntity.internalServerError().body(errorResponse);
//        }
//    }
//}
