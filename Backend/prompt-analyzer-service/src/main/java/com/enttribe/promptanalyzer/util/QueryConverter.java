/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import com.enttribe.promptanalyzer.model.Query;
import java.util.List;

/**
 * Utility class for converting Query entities to QueryResponseDto objects. Provides methods to
 * convert individual entities and lists of entities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public final class QueryConverter {

  // Private constructor to prevent instantiation
  private QueryConverter() {}

  /**
   * Converts a list of Query entities to a list of QueryResponseDto objects.
   *
   * @param queries the list of Query entities to convert
   * @return a list of converted QueryResponseDto objects
   */
  public static List<QueryResponseDto> getQueryDtoList(List<Query> queries) {
    return queries.stream().map(QueryConverter::convertToQueryDto).toList();
  }

  /**
   * Converts a single Query entity to a QueryResponseDto object.
   *
   * @param query the Query entity to convert
   * @return the converted QueryResponseDto object
   */
  private static QueryResponseDto convertToQueryDto(Query query) {
    return QueryResponseDto.builder()
        .id(query.getId())
        .userId(query.getUserId())
        .question(query.getQuestion())
        .type(query.getType())
        .nanoId(query.getNanoId())
        .build();
  }
}
