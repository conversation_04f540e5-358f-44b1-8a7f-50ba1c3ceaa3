/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.TriggerDao;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Trigger;
import com.enttribe.promptanalyzer.service.TriggerService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.TriggerConverter;
import java.util.Comparator;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link TriggerService} interface. This class provides functionality for
 * managing triggers in the system, including searching, creating, and updating trigger records. It
 * handles the business logic for trigger operations and interacts with the persistence layer
 * through the DAO.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TriggerServiceImpl implements TriggerService {

  private final CustomFilter customFilter;
  private final TriggerDao triggerDao;

  @Override
  public List<TriggerResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    try {
      log.debug("Inside @method search. @param : filter -> {}", filter);
      List<Trigger> triggers =
          customFilter.searchByFilter(Trigger.class, filter, orderBy, orderType, offset, size);
      log.debug("Number of triggers found: {}", triggers.size());
      // Sort the list to ensure "Recurrence" comes first
      triggers.sort(
          Comparator.comparing((Trigger t) -> !"Recurrence".equalsIgnoreCase(t.getType()))
              .thenComparing(Trigger::getId)); // Preserving original order for other fields

      return TriggerConverter.getTriggerDtoList(triggers);
    } catch (Exception e) {
      log.error("Error while searching for triggers: {}", e.getMessage(), e);
      throw new BusinessException("Failed to search triggers");
    }
  }
}
