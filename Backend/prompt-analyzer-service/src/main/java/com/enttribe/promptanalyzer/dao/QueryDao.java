/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Query;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Query entity operations. Provides methods to interact with the
 * Query table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface QueryDao extends JpaRepository<Query, Integer> {

  @org.springframework.data.jpa.repository.Query(
      "SELECT q FROM Query q WHERE q.nanoId = :nanoId and q.deleted = false")
  Optional<Query> findByNanoId(String nanoId);
}
