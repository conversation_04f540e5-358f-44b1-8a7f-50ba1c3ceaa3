///*
//
//           * Copyright (c) 2024 Vwaves Technologies Private Limited
//
//           *
//
//           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
//
//           * Product: Aspose.Total for Java
//
//           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
//
//           * Order ID: 250610115636
//
//           *
//
//           * This source code is part of a proprietary software system and may not be
//
//           * distributed, modified, or reused without explicit written permission from
//
//           * Vwaves Technologies Private Limited.
//
//           *
//
//           * Contact: <EMAIL>
//
//           */
//package com.enttribe.promptanalyzer.config.redis;
//
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.security.KeyManagementException;
//import java.security.KeyStore;
//import java.security.KeyStoreException;
//import java.security.NoSuchAlgorithmException;
//import java.security.cert.CertificateException;
//import java.time.Duration;
//import javax.net.ssl.SSLContext;
//import javax.net.ssl.SSLSocketFactory;
//import javax.net.ssl.TrustManagerFactory;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.ai.embedding.BatchingStrategy;
//import org.springframework.ai.embedding.EmbeddingModel;
//import org.springframework.ai.embedding.TokenCountBatchingStrategy;
//import org.springframework.ai.vectorstore.VectorStore;
//import org.springframework.ai.vectorstore.redis.RedisVectorStore;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
//import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
//import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
//import redis.clients.jedis.DefaultJedisClientConfig;
//import redis.clients.jedis.HostAndPort;
//import redis.clients.jedis.JedisClientConfig;
//import redis.clients.jedis.JedisPoolConfig;
//import redis.clients.jedis.JedisPooled;
//
//@Configuration
//@EnableConfigurationProperties({RedisProperties.class})
//@ConditionalOnProperty(name = "vector.store.type", havingValue = "redis-stack", matchIfMissing = true)
//public class RedisVectorConfig {
//
//  private static final Logger log = LoggerFactory.getLogger(RedisVectorConfig.class);
//
//  @Value("${vector.redis.trustStorePath:}")
//  private String trustStorePath;
//
//  @Value("${vector.redis.trustStorePassword:}")
//  private String trustStorePassword;
//
//  @Value("${commons.ai.sdk.keyStoreInstance:jks}")
//  private String keyStoreInstance;
//
//  @Value("${spring.ai.vectorstore.redis.index-name}")
//  private String indexName;
//
//  @Value("${spring.ai.vectorstore.redis.prefix}")
//  private String redisPrefix;
//
//  @Value("${hint.collection.name}")
//  private String hintCollectionName;
//
//  @Value("${spring.data.redis.ssl.enable}")
//  private boolean sslEnable;
//
//  @Value("${spring.data.redis.timeout.connection:10000}")
//  private int connectionTimeout;
//
//  @Value("${spring.data.redis.timeout.socket:10000}")
//  private int socketTimeout;
//
//  @Value("${spring.data.redis.timeout.blocking:10000}")
//  private int blockingTimeout;
//
//  @Value("${spring.data.redis.timeout.read:10000}")
//  private int readTimeout;
//
//  @Value("${spring.data.redis.pool.max-total:50}")
//  private int maxTotal;
//
//  @Value("${spring.data.redis.pool.max-idle:20}")
//  private int maxIdle;
//
//  @Value("${spring.data.redis.pool.min-idle:5}")
//  private int minIdle;
//
//  @Bean
//  @ConditionalOnMissingBean(BatchingStrategy.class)
//  BatchingStrategy batchingStrategy() {
//    return new TokenCountBatchingStrategy();
//  }
//
//  @Bean
//  @Primary
//  public VectorStore vectorStore(JedisPooled jedisPooled, EmbeddingModel embeddingModel) {
//    log.info("creating redis vector store");
//    RedisVectorStore redisVectorStore =
//        RedisVectorStore.builder(jedisPooled, embeddingModel)
//            .indexName(indexName)
//            .prefix(redisPrefix)
//            .metadataFields(
//                RedisVectorStore.MetadataField.tag("doc_id"),
//                RedisVectorStore.MetadataField.tag("custom_agent_id"))
//            .initializeSchema(true)
//            .batchingStrategy(new TokenCountBatchingStrategy())
//            .build();
//
//    redisVectorStore.afterPropertiesSet();
//    return redisVectorStore;
//  }
//
//  @Bean
//  public VectorStore vectorStoreHint(JedisPooled jedisPooled, EmbeddingModel embeddingModel) {
//    RedisVectorStore redisVectorStore =
//        RedisVectorStore.builder(jedisPooled, embeddingModel)
//            .indexName(hintCollectionName)
//            .prefix("emp_base")
//            .metadataFields(
//                RedisVectorStore.MetadataField.tag("name"),
//                RedisVectorStore.MetadataField.numeric("id"),
//                RedisVectorStore.MetadataField.tag("first_name"),
//                RedisVectorStore.MetadataField.tag("user_name"),
//                RedisVectorStore.MetadataField.tag("last_name"),
//                RedisVectorStore.MetadataField.tag("employeeName"),
//                RedisVectorStore.MetadataField.tag("type"),
//                RedisVectorStore.MetadataField.tag("filter"),
//                RedisVectorStore.MetadataField.text("employeeData"),
//                RedisVectorStore.MetadataField.tag("entity"),
//                RedisVectorStore.MetadataField.tag("reference id"))
//            .initializeSchema(true)
//            .batchingStrategy(new TokenCountBatchingStrategy())
//            .build();
//
//    redisVectorStore.afterPropertiesSet();
//    return redisVectorStore;
//  }
//
//  @Bean
//  public JedisPooled jedisPooled(RedisProperties redisProperties)
//      throws CertificateException,
//          KeyStoreException,
//          IOException,
//          NoSuchAlgorithmException,
//          KeyManagementException {
//    log.info("Creating JedisPooled with configuration - connectionTimeout: {}, socketTimeout: {}, blockingTimeout: {}, readTimeout: {}, maxTotal: {}, maxIdle: {}, minIdle: {}",
//             connectionTimeout, socketTimeout, blockingTimeout, readTimeout, maxTotal, maxIdle, minIdle);
//    JedisConnectionFactory jedisConnectionFactory = this.getJedisConnectionFactory(redisProperties);
//    JedisPooled jedisPooled = this.jedisPooled(jedisConnectionFactory);
//    jedisPooled.set("foo", "bar");
//    String string = jedisPooled.get("foo");
//    System.out.println("JedisPooled created with value: " + string);
//    return jedisPooled;
//  }
//
//  private JedisPooled jedisPooled(JedisConnectionFactory jedisConnectionFactory) {
//    String host = jedisConnectionFactory.getHostName();
//    int port = jedisConnectionFactory.getPort();
//
//    if (sslEnable) {
//      SSLSocketFactory sslSocketFactory =
//          jedisConnectionFactory
//              .getClientConfiguration()
//              .getSslSocketFactory()
//              .orElseThrow(() -> new IllegalStateException("SSL socket factory is not available"));
//      JedisClientConfig clientConfig =
//          DefaultJedisClientConfig.builder()
//              .ssl(jedisConnectionFactory.isUseSsl())
//              .sslSocketFactory(sslSocketFactory)
//              .clientName(jedisConnectionFactory.getClientName())
//              .socketTimeoutMillis(socketTimeout)
//              .connectionTimeoutMillis(connectionTimeout)
//              .blockingSocketTimeoutMillis(blockingTimeout)
//              .password(jedisConnectionFactory.getPassword())
//              .build();
//
//      return new JedisPooled(new HostAndPort(host, port), clientConfig);
//    } else {
//      JedisClientConfig clientConfig =
//          DefaultJedisClientConfig.builder()
//              .clientName(jedisConnectionFactory.getClientName())
//              .socketTimeoutMillis(socketTimeout)
//              .connectionTimeoutMillis(connectionTimeout)
//              .blockingSocketTimeoutMillis(blockingTimeout)
//              .password(jedisConnectionFactory.getPassword())
//              .build();
//
//      return new JedisPooled(new HostAndPort(host, port), clientConfig);
//    }
//  }
//
//  private JedisConnectionFactory getJedisConnectionFactory(RedisProperties redisProperties)
//      throws KeyStoreException,
//          IOException,
//          NoSuchAlgorithmException,
//          KeyManagementException,
//          CertificateException {
//
//    RedisStandaloneConfiguration standaloneConfig = new RedisStandaloneConfiguration();
//    standaloneConfig.setHostName(redisProperties.getHost());
//    standaloneConfig.setPort(redisProperties.getPort());
//    standaloneConfig.setDatabase(redisProperties.getDatabase());
//
//    if (redisProperties.getPassword() != null) {
//      standaloneConfig.setPassword(redisProperties.getPassword());
//    }
//
//    JedisPoolConfig poolConfig = new JedisPoolConfig();
//    poolConfig.setMaxTotal(maxTotal);
//    poolConfig.setMaxIdle(maxIdle);
//    poolConfig.setMinIdle(minIdle);
//    poolConfig.setTestOnBorrow(true);
//    poolConfig.setTestWhileIdle(true);
//    poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(30000));
//    poolConfig.setMinEvictableIdleDuration(Duration.ofMillis(60000));
//    poolConfig.setNumTestsPerEvictionRun(-1);
//
//    if (sslEnable) {
//      log.info("setting up ssl for redis connection");
//      KeyStore trustStore = KeyStore.getInstance(keyStoreInstance);
//      trustStore.load(new FileInputStream(trustStorePath), trustStorePassword.toCharArray());
//
//      TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance("X509");
//      trustManagerFactory.init(trustStore);
//
//      SSLContext sslContext = SSLContext.getInstance("TLS");
//      sslContext.init(null, trustManagerFactory.getTrustManagers(), null);
//
//      SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
//
//      JedisClientConfiguration clientConfig =
//          JedisClientConfiguration.builder().usePooling().poolConfig(poolConfig).and().connectTimeout(Duration.ofMillis(connectionTimeout)).readTimeout(Duration.ofMillis(readTimeout)).useSsl().sslSocketFactory(sslSocketFactory).build();
//      return new JedisConnectionFactory(standaloneConfig, clientConfig);
//    }
//
//    JedisClientConfiguration clientConfig = JedisClientConfiguration.builder().usePooling().poolConfig(poolConfig).and().connectTimeout(Duration.ofMillis(connectionTimeout)).readTimeout(Duration.ofMillis(readTimeout)).build();
//
//    return new JedisConnectionFactory(standaloneConfig, clientConfig);
//  }
//}
