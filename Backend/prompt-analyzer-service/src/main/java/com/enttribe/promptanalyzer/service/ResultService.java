/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.result.AssertionResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestCaseResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestResultResponseDto;
import java.util.List;

public interface ResultService {

  // AssertionResult
  List<AssertionResultResponseDto> searchAssertionResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  Long countAssertionResults(String filter);

  // TestCaseResult
  List<TestCaseResultResponseDto> searchTestCaseResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  Long countTestCaseResults(String filter);

  // TestResult
  List<TestResultResponseDto> searchTestResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  Long countTestResults(String filter);
}
