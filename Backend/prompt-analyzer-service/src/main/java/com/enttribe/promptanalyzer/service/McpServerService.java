/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import java.util.List;
import java.util.Map;

/**
 * Service interface for managing MCP servers. Provides methods for creating, updating, searching,
 * and deleting MCP servers.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface McpServerService {

  /**
   * Creates a new MCP server.
   *
   * @param serverDto The DTO containing the server details
   * @return Map containing the operation result
   */
  Map<String, String> create(McpServerDto serverDto);

  /**
   * Updates an existing MCP server.
   *
   * @param serverDto The DTO containing the updated server details
   * @return Map containing the operation result
   */
  Map<String, String> update(McpServerDto serverDto);

  /**
   * Searches for MCP servers based on specified criteria and pagination parameters.
   *
   * @param filter The search filter criteria
   * @param offset The starting position in the result set
   * @param size The maximum number of results to return
   * @param orderBy The field to sort results by
   * @param orderType The sort direction (ascending/descending)
   * @return List of MCP servers matching the search criteria
   */
  List<McpServerDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts MCP servers based on the provided filter.
   *
   * @param filter The filter criteria to count matching servers
   * @return Count of matching servers
   */
  Long count(String filter);

  /**
   * Soft deletes an MCP server by ID.
   *
   * @param id The ID of the server to delete
   * @return Map containing the operation result
   */
  Map<String, String> softDelete(String id);

  /**
   * Retrieves MCP servers by a list of integer IDs.
   *
   * @param ids the list of MCP server IDs
   * @return a list of MCP server DTOs
   */
  List<McpServerDto> getMcpServerByIds(List<String> ids);
}
