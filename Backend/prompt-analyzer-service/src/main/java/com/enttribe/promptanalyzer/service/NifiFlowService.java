/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import java.util.List;
import java.util.Map;

/**
 * Manages NiFi flow creation and configuration operations. This service handles the generation and
 * management of NiFi flows, providing functionality to create and configure data processing
 * pipelines.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface NifiFlowService {

  /**
   * Creates a NiFi trigger flow based on the provided flow DTO.
   *
   * @param flowDto the NifiFlowDto containing flow configuration
   * @return a map containing the result of the trigger creation
   */
  Map<String, String> createTriggerNifi(NifiFlowDto flowDto);

  /**
   * Retrieves validation errors for a given process group ID.
   *
   * @param processGroupId the process group ID
   * @return a list of validation error messages
   */
  List<String> getValidationErrorsProcessGroup(String processGroupId);
}
