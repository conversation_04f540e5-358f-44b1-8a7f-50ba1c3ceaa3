/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.Trigger;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Data Access Object interface for Trigger entity operations. Provides basic CRUD operations for
 * managing triggers in the database. Extends JpaRepository to inherit standard database operations.
 *
 * <AUTHOR>
 * @version 1.0
 * @see Trigger
 * @see JpaRepository
 */
@Repository
public interface TriggerDao extends JpaRepository<Trigger, Integer> {

  @Query("SELECT t FROM Trigger t WHERE t.name = :name and t.deleted = false")
  Trigger findByName(String name);

  @Query("SELECT t FROM Trigger t WHERE t.nanoId = :id and t.deleted = false")
  Optional<Trigger> findByNanoId(String id);
}
