/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import jakarta.persistence.*;
import java.lang.reflect.*;
import java.util.*;

public class EntityCloner {

  public static <T> T cloneEntity(T source) {
    if (source == null) {
      return null;
    }

    try {
      Class<?> clazz = source.getClass();
      @SuppressWarnings("unchecked")
      T clone = (T) clazz.getDeclaredConstructor().newInstance();

      for (Field field : getAllFields(clazz)) {
        int modifiers = field.getModifiers();

        // Skip static or final fields
        if (Modifier.isStatic(modifiers) || Modifier.isFinal(modifiers)) {
          continue;
        }

        field.setAccessible(true);

        // Skip ID field
        if (field.isAnnotationPresent(Id.class)) {
          continue;
        }

        Object value = field.get(source);

        if (value == null) {
          field.set(clone, null);
          continue;
        }

        // Handle OneToMany - deep clone
        if (field.isAnnotationPresent(OneToMany.class)) {
          Collection<?> collection = (Collection<?>) value;
          Collection<Object> clonedCollection = createEmptyCollection(field.getType());

          for (Object item : collection) {
            clonedCollection.add(cloneEntity(item)); // recursive deep clone
          }

          field.set(clone, clonedCollection);
        }

        // Handle ManyToMany - shallow copy
        else if (field.isAnnotationPresent(ManyToMany.class)) {
          Collection<?> collection = (Collection<?>) value;
          field.set(clone, new HashSet<>(collection)); // shallow copy
        }

        // Handle embedded or scalar fields
        else if (isEntity(value.getClass())) {
          // For ManyToOne or OneToOne — shallow copy
          field.set(clone, value);
        } else {
          field.set(clone, value);
        }
      }

      return clone;

    } catch (Exception e) {
      throw new RuntimeException("Failed to clone entity", e);
    }
  }

  private static boolean isEntity(Class<?> clazz) {
    return clazz.isAnnotationPresent(Entity.class);
  }

  private static Collection<Object> createEmptyCollection(Class<?> collectionType) {
    if (collectionType.isAssignableFrom(Set.class)) {
      return new HashSet<>();
    } else if (collectionType.isAssignableFrom(List.class)) {
      return new ArrayList<>();
    } else if (!collectionType.isInterface()) {
      try {
        return (Collection<Object>) collectionType.getDeclaredConstructor().newInstance();
      } catch (Exception ignored) {
      }
    }
    throw new UnsupportedOperationException("Unsupported collection type: " + collectionType);
  }

  private static List<Field> getAllFields(Class<?> clazz) {
    List<Field> fields = new ArrayList<>();
    while (clazz != null && clazz != Object.class) {
      fields.addAll(List.of(clazz.getDeclaredFields()));
      clazz = clazz.getSuperclass();
    }
    return fields;
  }
}
