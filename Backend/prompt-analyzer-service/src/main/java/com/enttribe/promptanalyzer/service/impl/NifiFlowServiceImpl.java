/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.config.RestTemplateSingleton;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.dto.nififlow.ProcessorCreateDto;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.enttribe.promptanalyzer.util.MessageUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

/**
 * Implementation of the {@link NifiFlowService} interface. This class provides the actual business
 * logic for managing NiFi flows. It handles the creation, modification, and deletion of NiFi
 * processors and their connections within process groups.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NifiFlowServiceImpl implements NifiFlowService {

  private static final int INITIAL_X_POSITION = 100;
  private static final int INITIAL_Y_POSITION = 100;
  private static final int POSITION_INCREMENT = 200;

  private final HttpServletRequest httpServletRequest;

  @Value("${nifi.api.url}")
  private String nifiApiBaseUrl;

  @Value("${nifi.custom.agent.url}")
  private String customApiBaseUrl;

  @Value("${process.groups}")
  private String processGroups;

  @Override
  public Map<String, String> createTriggerNifi(NifiFlowDto flowDto) {
    try {
      String authorization = httpServletRequest.getHeader(PromptConstants.AUTHORIZATION);
      log.info("Authorization Header: {} ", authorization);
      log.info("flowObject is : {}", flowDto.getFlowObject());
      JSONArray planArray = new JSONArray(flowDto.getFlowObject());
      deleteAllConnections(flowDto.getProcessGroupId(), authorization);
      deleteAllProcessors(flowDto.getProcessGroupId(), authorization);
      createTrigger(
          "TestTriggerMultipleActionCustom", planArray, flowDto.getProcessGroupId(), authorization);
      return Map.of("status", PromptConstants.SUCCESS, "message", MessageUtils.getTriggerMessage());
    } catch (Exception e) {
      log.error("Error while creating flow in nifi: {}", e.getMessage(), e);
      return Map.of("status", "failed");
    }
  }

  @Override
  public List<String> getValidationErrorsProcessGroup(String processGroupId) {
    HttpHeaders headers = new HttpHeaders();
    String authorization = httpServletRequest.getHeader(PromptConstants.AUTHORIZATION);
    headers.set(PromptConstants.AUTHORIZATION, "Bearer " + authorization);
    headers.set(PromptConstants.CONTENT_TYPE, PromptConstants.APPLICATION_JSON);

    HttpEntity<String> entity = new HttpEntity<>(headers);

    String url = String.format("%s/flow/process-groups/%s", nifiApiBaseUrl, processGroupId);

    List<String> failedValidation = new ArrayList<>();
    try {
      ResponseEntity<String> response =
          RestTemplateSingleton.getRestTemplate()
              .exchange(url, HttpMethod.GET, entity, String.class);

      String responseBody = response.getBody();
      JSONObject jsonObject = new JSONObject(responseBody);
      JSONObject processGroupFlow = jsonObject.getJSONObject("processGroupFlow");
      JSONObject flow = processGroupFlow.getJSONObject("flow");
      JSONArray processors = flow.getJSONArray("processors");
      for (int i = 0; i < processors.length(); i++) {
        JSONObject processor = processors.getJSONObject(i);
        JSONObject component = processor.getJSONObject(PromptConstants.COMPONENT);
        String name = component.getString("name");
        String validationStatus = component.getString("validationStatus");

        if (!validationStatus.equalsIgnoreCase("VALID")) {
          String validationErrors = component.getJSONArray("validationErrors").toString();
          failedValidation.add(
              "validation failed for " + name + ", details are : " + validationErrors);
        }
      }
      log.debug("failed validations : {}", failedValidation);
      return failedValidation;
    } catch (RestClientException e) {
      log.error("error while getting validationErrors : {}", e.getMessage(), e);
      return new ArrayList<>();
    }
  }

  private void createTrigger(
      String flowName, JSONArray flowArray, String parentGroupId, String authorization)
      throws Exception {
    int x = INITIAL_X_POSITION;
    int y = INITIAL_Y_POSITION;
    log.info("createTrigger is called with flowName: {}", flowName);

    Map<String, Object> tempProcessor = new HashMap<>();
    Map<String, Object> triggerProcessor = null;
    log.info("flow array is : {}", flowArray.toString());
    for (int i = 0; i < flowArray.length(); i++) {
      JSONObject flowObject = flowArray.getJSONObject(i);
      log.info("Processing flow object: {}", flowObject);

      if (isTrigger(flowObject)) {
        log.debug("Creating trigger processor for flow object: {}", flowObject);
        triggerProcessor = createTriggerProcessor(flowObject, parentGroupId, x, y, authorization);
        y += POSITION_INCREMENT;
      } else {
        log.debug("Creating action processor for flow object: {}", flowObject);
        Map<String, Object> actionProcessor =
            createActionProcessor(
                flowObject, parentGroupId, x, y, i, flowArray.length(), authorization);
        y += POSITION_INCREMENT;

        if (triggerProcessor != null) {
          log.debug("Connecting trigger processor to action processor");
          connectProcessors(
              parentGroupId,
              triggerProcessor,
              actionProcessor,
              List.of(PromptConstants.SUCCESS),
              authorization);
          triggerProcessor = null;
        } else {
          log.debug("Connecting temporary processor to action processor");
          if (tempProcessor != null && tempProcessor.containsKey(PromptConstants.COMPONENT)) {
            connectProcessors(
                parentGroupId,
                tempProcessor,
                actionProcessor,
                List.of(PromptConstants.SUCCESS),
                authorization);
          }
        }
        tempProcessor = actionProcessor;
      }
      log.info("Processors connected PromptConstants.SUCCESSfully!");
    }
  }

  private boolean isTrigger(JSONObject flowObject) {
    log.debug("Checking if flow object is a trigger: {}", flowObject);
    return flowObject.getString("type").equalsIgnoreCase("TRIGGER");
  }

  private Map<String, Object> createTriggerProcessor(
      JSONObject flowObject, String parentGroupId, int x, int y, String authorization)
      throws Exception {
    log.debug("Creating trigger processor for flow object: {}", flowObject);
    if (flowObject.has(PromptConstants.CONDITION)) {
      log.info("condition is {}", flowObject.getJSONObject(PromptConstants.CONDITION).toString());
      return createProcessor(
          new ProcessorCreateDto(
              parentGroupId,
              flowObject.getString(PromptConstants.PACKAGE),
              flowObject.getString("name"),
              x,
              y,
              null,
              flowObject.getJSONObject(PromptConstants.CONDITION).toMap(),
              authorization));
    } else {
      return createProcessor(
          new ProcessorCreateDto(
              parentGroupId,
              flowObject.getString(PromptConstants.PACKAGE),
              flowObject.getString("name"),
              x,
              y,
              null,
              null,
              authorization));
    }
  }

  private Map<String, Object> createActionProcessor(
      JSONObject flowObject,
      String parentGroupId,
      int x,
      int y,
      int index,
      int length,
      String authorization)
      throws Exception {
    log.debug("Creating action processor for flow object: {}", flowObject);
    List<String> relationship = new ArrayList<>();
    if (index == length - 1) {
      relationship.add(PromptConstants.SUCCESS);
      relationship.add("failure");
    } else {
      relationship.add("failure");
    }

    if (flowObject.has(PromptConstants.CONDITION)
        && flowObject.getJSONObject(PromptConstants.CONDITION) != null) {
      log.info("condition is {}", flowObject.getJSONObject(PromptConstants.CONDITION).toString());
      return createProcessor(
          new ProcessorCreateDto(
              parentGroupId,
              flowObject.getString(PromptConstants.PACKAGE),
              flowObject.getString("name"),
              x,
              y,
              relationship,
              flowObject.getJSONObject(PromptConstants.CONDITION).toMap(),
              authorization));
    } else {
      return createProcessor(
          new ProcessorCreateDto(
              parentGroupId,
              flowObject.getString(PromptConstants.PACKAGE),
              flowObject.getString("name"),
              x,
              y,
              relationship,
              null,
              authorization));
    }
  }

  private Map<String, Object> createProcessor(ProcessorCreateDto dto) throws Exception {
    String url = nifiApiBaseUrl + processGroups + dto.getParentGroupId() + "/processors";
    log.debug("Creating processor at URL: {}", url);

    Map<String, Object> payload = new HashMap<>();
    payload.put(PromptConstants.REVISION, Map.of(PromptConstants.VERSION, 0));

    Map<String, Object> config = new HashMap<>();
    if (dto.getAutoTerminatedRelationships() != null) {
      config.put("autoTerminatedRelationships", dto.getAutoTerminatedRelationships());
    }
    if (dto.getProperties() != null) {
      config.put("properties", dto.getProperties());
    }

    Map<String, Object> component = new HashMap<>();
    component.put("type", dto.getProcessorType());
    component.put("name", dto.getName());
    component.put("position", Map.of("x", dto.getX(), "y", dto.getY()));
    component.put("config", config);

    payload.put(PromptConstants.COMPONENT, component);

    log.info("Processor payload: {}", payload);
    String response = sendPostRequest(url, payload, dto.getAuthorization());
    log.debug("Processor creation response: {}", response);

    return new ObjectMapper().readValue(response, new TypeReference<Map<String, Object>>() {});
  }

  private void connectProcessors(
      String parentGroupId,
      Map<String, Object> sourceProcessor,
      Map<String, Object> targetProcessor,
      List<String> connection,
      String authorization)
      throws IOException, URISyntaxException {
    String url = nifiApiBaseUrl + processGroups + parentGroupId + "/connections";
    log.debug("Connecting processors at URL: {}", url);

    String sourceId =
        ((Map<?, ?>) sourceProcessor.get(PromptConstants.COMPONENT)).get("id").toString();
    String targetId =
        ((Map<?, ?>) targetProcessor.get(PromptConstants.COMPONENT)).get("id").toString();

    Map<String, Object> payload = new HashMap<>();
    payload.put(PromptConstants.REVISION, Map.of(PromptConstants.VERSION, 0));
    payload.put(
        PromptConstants.COMPONENT,
        Map.of(
            "source",
            Map.of(
                "groupId", parentGroupId,
                "id", sourceId,
                "type", "PROCESSOR"),
            "destination",
            Map.of(
                "groupId", parentGroupId,
                "id", targetId,
                "type", "PROCESSOR"),
            "selectedRelationships",
            connection));

    sendPostRequest(url, payload, authorization);
  }

  private static String sendPostRequest(
      String apiUrl, Map<String, Object> payload, String authorization)
      throws IOException, URISyntaxException {
    String jsonPayload = new ObjectMapper().writeValueAsString(payload);
    log.debug("Sending POST request to URL: {} with payload: {}", apiUrl, jsonPayload);

    URI uri = new URI(apiUrl);
    URL url = uri.toURL();
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    connection.setRequestMethod("POST");
    connection.setRequestProperty(PromptConstants.CONTENT_TYPE, PromptConstants.APPLICATION_JSON);
    connection.setRequestProperty(PromptConstants.AUTHORIZATION, authorization);
    connection.setDoOutput(true);

    try (OutputStream os = connection.getOutputStream()) {
      byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
      os.write(input, 0, input.length);
    }

    int responseCode = connection.getResponseCode();
    StringBuilder response = new StringBuilder();
    switch (responseCode) {
      case HttpURLConnection.HTTP_OK, HttpURLConnection.HTTP_CREATED -> {
        try (BufferedReader in =
            new BufferedReader(
                new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
          String inputLine;
          while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
          }
        }
      }

      case HttpURLConnection.HTTP_INTERNAL_ERROR -> {
        try (BufferedReader in =
            new BufferedReader(
                new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
          String inputLine;
          while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
          }
        }
        log.error("HTTP 500 Error - Server error. Response: {}", response);
      }

      default -> {
        log.error("HTTP Error {} : {}", responseCode, connection.getResponseMessage());
        try (BufferedReader in =
            new BufferedReader(
                new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
          String inputLine;
          while ((inputLine = in.readLine()) != null) {
            response.append(inputLine);
          }
        }
      }
    }
    log.debug("Response from POST request: {}", response);
    connection.disconnect();
    return response.toString();
  }

  public void deleteAllProcessors(String processGroupId, String authorization) {
    try {
      String url = nifiApiBaseUrl + processGroups + processGroupId + "/processors";
      HttpURLConnection connection = createConnection(url, "GET", authorization);

      String response = readResponse(connection);

      JSONObject responseObject = new JSONObject(response);
      JSONArray processors = responseObject.getJSONArray("processors");

      for (int i = 0; i < processors.length(); i++) {
        String processorId = processors.getJSONObject(i).getString("id");
        String clientId = null;
        if (processors
            .getJSONObject(i)
            .getJSONObject(PromptConstants.REVISION)
            .has(PromptConstants.CLIENT_ID)) {
          clientId =
              processors
                  .getJSONObject(i)
                  .getJSONObject(PromptConstants.REVISION)
                  .getString(PromptConstants.CLIENT_ID);
        }
        int version =
            processors
                .getJSONObject(i)
                .getJSONObject(PromptConstants.REVISION)
                .getInt(PromptConstants.VERSION);
        deleteProcessor(processorId, clientId, version, authorization);
      }
    } catch (Exception e) {
      log.error("Error while deleting processors: {}", e.getMessage());
    }
  }

  public void deleteAllConnections(String processGroupId, String authorization) {
    try {
      String url = nifiApiBaseUrl + processGroups + processGroupId + "/connections";
      HttpURLConnection connection = createConnection(url, "GET", authorization);

      String response = readResponse(connection);

      JSONObject responseObject = new JSONObject(response);
      JSONArray processors = responseObject.getJSONArray("connections");

      for (int i = 0; i < processors.length(); i++) {
        String processorId = processors.getJSONObject(i).getString("id");
        String clientId = null;
        if (processors
            .getJSONObject(i)
            .getJSONObject(PromptConstants.REVISION)
            .has(PromptConstants.CLIENT_ID)) {
          clientId =
              processors
                  .getJSONObject(i)
                  .getJSONObject(PromptConstants.REVISION)
                  .getString(PromptConstants.CLIENT_ID);
        }
        int version =
            processors
                .getJSONObject(i)
                .getJSONObject(PromptConstants.REVISION)
                .getInt(PromptConstants.VERSION);
        deleteConnection(processorId, clientId, version, authorization);
      }
    } catch (Exception e) {
      log.error("Error while deleting connections: {} ", e.getMessage());
    }
  }

  public void deleteConnection(
      String connectionId, String clientId, int version, String authorization) {
    try {
      String url;
      if (clientId != null) {
        url =
            nifiApiBaseUrl
                + "/connections/"
                + connectionId
                + "?clientId="
                + clientId
                + "&version="
                + version
                + PromptConstants.DISCONNECTED_NODE_ACKNOWLEDGED;
      } else {
        url =
            nifiApiBaseUrl
                + "/connections/"
                + connectionId
                + "?version="
                + version
                + PromptConstants.DISCONNECTED_NODE_ACKNOWLEDGED;
      }
      HttpURLConnection connection = createConnection(url, "DELETE", authorization);

      int responseCode = connection.getResponseCode();
      if (responseCode == HttpURLConnection.HTTP_OK) {
        log.info("Deleted processor: {} ", connectionId);
      } else {
        log.error("Failed to delete processor {} . Response code: {} ", connectionId, responseCode);
      }
    } catch (Exception e) {
      log.error("Error while deleting processor {} : {} ", connectionId, e.getMessage());
    }
  }

  // Deletes a single processor by ID
  public void deleteProcessor(
      String processorId, String clientId, int version, String authorization) {
    try {
      String url;
      if (clientId != null) {
        url =
            nifiApiBaseUrl
                + "/processors/"
                + processorId
                + "?clientId="
                + clientId
                + "&version="
                + version
                + PromptConstants.DISCONNECTED_NODE_ACKNOWLEDGED;
      } else {
        url =
            nifiApiBaseUrl
                + "/processors/"
                + processorId
                + "?version="
                + version
                + PromptConstants.DISCONNECTED_NODE_ACKNOWLEDGED;
      }
      HttpURLConnection connection = createConnection(url, "DELETE", authorization);

      int responseCode = connection.getResponseCode();
      if (responseCode == HttpURLConnection.HTTP_OK) {
        log.info("Deleted processor: {}", processorId);
      } else {
        log.error("Failed to delete processor {}. Response code: {}", processorId, responseCode);
      }
    } catch (Exception e) {
      log.error("Error while deleting processor {}: {}", processorId, e.getMessage());
    }
  }

  private static String readResponse(HttpURLConnection connection) throws IOException {
    try (BufferedReader in =
        new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
      StringBuilder response = new StringBuilder();
      String inputLine;
      while ((inputLine = in.readLine()) != null) {
        response.append(inputLine);
      }
      return response.toString();
    }
  }

  private static HttpURLConnection createConnection(
      String urlString, String method, String authorization) throws IOException {
    log.debug("Creating HTTP connection to URL: {} with method: {}", urlString, method);
    try {
      URI uri = new URI(urlString);
      URL url = uri.toURL();
      HttpURLConnection connection = (HttpURLConnection) url.openConnection();
      connection.setRequestMethod(method);
      connection.setRequestProperty(PromptConstants.AUTHORIZATION, authorization);
      connection.setRequestProperty(PromptConstants.CONTENT_TYPE, PromptConstants.APPLICATION_JSON);
      connection.setDoOutput(true);
      return connection;
    } catch (Exception e) {
      log.error("Failed to create HTTP connection: {}", e.getMessage());
      throw new IOException("Failed to create HTTP connection", e);
    }
  }
}
