/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.vectorstore;

/**
 * Configuration class for Redis 8 Vector Store.
 * 
 * Provides configuration options for Redis 8's native vector sets including:
 * - Vector set naming and key prefixes
 * - Quantization settings (8-bit, binary, none)
 * - Dimensionality reduction options
 * - Schema initialization control
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Redis8VectorStoreConfig {

    /**
     * Quantization types supported by Redis 8 vector sets
     * Based on Redis documentation: https://redis.io/docs/latest/develop/data-types/vector-sets/
     */
    public enum QuantizationType {
        /** 8-bit quantization (default) - reduces memory usage while maintaining good accuracy */
        QUANTIZE_8BIT("Q8"),

        /** Binary quantization - maximum memory savings with reduced accuracy */
        BINARY("BIN"),

        /** No quantization - full precision, maximum memory usage */
        NONE("NOQUANT");

        private final String redisValue;

        QuantizationType(String redisValue) {
            this.redisValue = redisValue;
        }

        public String getRedisValue() {
            return redisValue;
        }

        // Keep backward compatibility
        public String getValue() {
            return redisValue;
        }
    }

    // Default configuration values
    private static final String DEFAULT_VECTOR_SET_NAME = "spring-ai";
    private static final String DEFAULT_KEY_PREFIX = "spring-ai-prefix:";
    private static final QuantizationType DEFAULT_QUANTIZATION = QuantizationType.QUANTIZE_8BIT;
    private static final boolean DEFAULT_INITIALIZE_SCHEMA = true;
    private static final int DEFAULT_DIMENSIONS = -1; // -1 means no dimensionality reduction

    // Configuration properties
    private final String vectorSetName;
    private final String keyPrefix;
    private final QuantizationType quantizationType;
    private final boolean initializeSchema;
    private final int targetDimensions;
    private final boolean enableDimensionalityReduction;

    /**
     * Private constructor - use builder pattern
     */
    private Redis8VectorStoreConfig(Builder builder) {
        this.vectorSetName = builder.vectorSetName;
        this.keyPrefix = builder.keyPrefix;
        this.quantizationType = builder.quantizationType;
        this.initializeSchema = builder.initializeSchema;
        this.targetDimensions = builder.targetDimensions;
        this.enableDimensionalityReduction = builder.targetDimensions > 0;
    }

    // Getters
    public String getVectorSetName() {
        return vectorSetName;
    }

    public String getKeyPrefix() {
        return keyPrefix;
    }

    public QuantizationType getQuantizationType() {
        return quantizationType;
    }

    public boolean isInitializeSchema() {
        return initializeSchema;
    }

    public int getTargetDimensions() {
        return targetDimensions;
    }

    public boolean isEnableDimensionalityReduction() {
        return enableDimensionalityReduction;
    }

    /**
     * Builder pattern for Redis8VectorStoreConfig
     */
    public static class Builder {
        private String vectorSetName = DEFAULT_VECTOR_SET_NAME;
        private String keyPrefix = DEFAULT_KEY_PREFIX;
        private QuantizationType quantizationType = DEFAULT_QUANTIZATION;
        private boolean initializeSchema = DEFAULT_INITIALIZE_SCHEMA;
        private int targetDimensions = DEFAULT_DIMENSIONS;

        /**
         * Set the name of the Redis vector set
         * 
         * @param vectorSetName the name of the vector set
         * @return this builder
         */
        public Builder vectorSetName(String vectorSetName) {
            this.vectorSetName = vectorSetName != null ? vectorSetName : DEFAULT_VECTOR_SET_NAME;
            return this;
        }

        /**
         * Set the key prefix for document keys
         * 
         * @param keyPrefix the prefix to use for document keys
         * @return this builder
         */
        public Builder keyPrefix(String keyPrefix) {
            this.keyPrefix = keyPrefix != null ? keyPrefix : DEFAULT_KEY_PREFIX;
            return this;
        }

        /**
         * Set the quantization type for vectors
         * 
         * @param quantizationType the quantization type to use
         * @return this builder
         */
        public Builder quantizationType(QuantizationType quantizationType) {
            this.quantizationType = quantizationType != null ? quantizationType : DEFAULT_QUANTIZATION;
            return this;
        }

        /**
         * Set whether to initialize the schema on startup
         * 
         * @param initializeSchema true to initialize schema, false otherwise
         * @return this builder
         */
        public Builder initializeSchema(boolean initializeSchema) {
            this.initializeSchema = initializeSchema;
            return this;
        }

        /**
         * Set target dimensions for dimensionality reduction
         * 
         * @param targetDimensions target number of dimensions (must be > 0 to enable reduction)
         * @return this builder
         */
        public Builder targetDimensions(int targetDimensions) {
            this.targetDimensions = targetDimensions;
            return this;
        }

        /**
         * Enable 8-bit quantization (default)
         * 
         * @return this builder
         */
        public Builder with8BitQuantization() {
            this.quantizationType = QuantizationType.QUANTIZE_8BIT;
            return this;
        }

        /**
         * Enable binary quantization for maximum memory savings
         * 
         * @return this builder
         */
        public Builder withBinaryQuantization() {
            this.quantizationType = QuantizationType.BINARY;
            return this;
        }

        /**
         * Disable quantization for full precision
         * 
         * @return this builder
         */
        public Builder withoutQuantization() {
            this.quantizationType = QuantizationType.NONE;
            return this;
        }

        /**
         * Enable dimensionality reduction to the specified number of dimensions
         * 
         * @param dimensions target number of dimensions
         * @return this builder
         */
        public Builder withDimensionalityReduction(int dimensions) {
            if (dimensions <= 0) {
                throw new IllegalArgumentException("Target dimensions must be positive");
            }
            this.targetDimensions = dimensions;
            return this;
        }

        /**
         * Build the configuration
         * 
         * @return the Redis8VectorStoreConfig instance
         */
        public Redis8VectorStoreConfig build() {
            return new Redis8VectorStoreConfig(this);
        }
    }

    /**
     * Create a new builder instance
     * 
     * @return a new builder
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Create a default configuration
     * 
     * @return default configuration
     */
    public static Redis8VectorStoreConfig defaultConfig() {
        return builder().build();
    }

    /**
     * Create configuration from existing vector set name and prefix
     * 
     * @param vectorSetName the vector set name
     * @param keyPrefix the key prefix
     * @return configuration with specified name and prefix
     */
    public static Redis8VectorStoreConfig of(String vectorSetName, String keyPrefix) {
        return builder()
            .vectorSetName(vectorSetName)
            .keyPrefix(keyPrefix)
            .build();
    }

    @Override
    public String toString() {
        return "Redis8VectorStoreConfig{" +
                "vectorSetName='" + vectorSetName + '\'' +
                ", keyPrefix='" + keyPrefix + '\'' +
                ", quantizationType=" + quantizationType +
                ", initializeSchema=" + initializeSchema +
                ", targetDimensions=" + targetDimensions +
                ", enableDimensionalityReduction=" + enableDimensionalityReduction +
                '}';
    }
}
