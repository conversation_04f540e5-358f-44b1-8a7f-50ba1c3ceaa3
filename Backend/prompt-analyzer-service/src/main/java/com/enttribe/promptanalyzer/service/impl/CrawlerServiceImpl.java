/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dto.crawl.CrawlResponse;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.service.CrawlerService;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CrawlerServiceImpl implements CrawlerService {

  private final ApiService apiService;
  private final KnowledgeBaseDao knowledgeBaseDao;
  private final KnowledgeBaseService knowledgeBaseService;

  @Override
  public void processWebSiteKB() {
    log.info("Starting to process website knowledge bases");
    try {
      List<KnowledgeBase> knowledgeBases = knowledgeBaseDao.getWebsiteTypeUnprocessedKB();
      log.debug("Found {} knowledge bases available for processing", knowledgeBases.size());
      for (KnowledgeBase knowledgeBase : knowledgeBases) {
        processIndividualKnowledgeBase(knowledgeBase);
      }
      log.info("Completed processing all website knowledge bases");
    } catch (Exception e) {
      log.error("Error in processWebSiteKB: {}", e.getMessage(), e);
      throw new BusinessException("Error processing website knowledge bases", e);
    }
  }

  private void processIndividualKnowledgeBase(KnowledgeBase knowledgeBase) {
    try {
      String taskId = knowledgeBase.getWebsiteTaskId();
      log.debug(
          "Getting status for taskId: {} for website: {}", taskId, knowledgeBase.getWebSiteUrl());

      CrawlResponse crawlResponse = apiService.getTaskStatus(taskId);
      if (crawlResponse.getError() != null) {
        log.warn("Error received for task {}: {}", taskId, crawlResponse.getError());
        knowledgeBase.setWebsiteTaskStatus("PROCESSING");
        knowledgeBase.setWebsiteTaskError(crawlResponse.getError());
        String newTaskId = apiService.triggerCrawl(knowledgeBase.getWebSiteUrl());
        log.info(
            "Triggered new crawl for website {}: new task ID {}",
            knowledgeBase.getWebSiteUrl(),
            newTaskId);
        knowledgeBase.setWebsiteTaskId(newTaskId);
        knowledgeBaseDao.save(knowledgeBase);
      } else if (crawlResponse.getStatus().equals("completed")) {
        log.info("Task {} completed for website {}", taskId, knowledgeBase.getWebSiteUrl());
        String markdown = crawlResponse.getResult().markdown();
        VectorResponseDto vectorResponse = knowledgeBaseService.saveInVector(List.of(markdown));
        log.debug(
            "Document saved in vector with filter: {} for website: {}",
            vectorResponse.getFilter(),
            knowledgeBase.getWebSiteUrl());
        knowledgeBase.setFilter(vectorResponse.getFilter());
        knowledgeBase.setDocMetaData(JsonUtils.convertToJSON(vectorResponse.getMetadata()));
        knowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));
        knowledgeBase.setWebsiteTaskStatus("COMPLETED");
        knowledgeBaseDao.save(knowledgeBase);
        log.info(
            "Successfully processed knowledge base for website: {}", knowledgeBase.getWebSiteUrl());
      } else {
        log.debug(
            "Task status is {} for website {}",
            crawlResponse.getStatus(),
            knowledgeBase.getWebSiteUrl());
      }
    } catch (Exception e) {
      log.error(
          "Error processing knowledge base for website {}: {}",
          knowledgeBase.getWebSiteUrl(),
          e.getMessage(),
          e);
      throw new BusinessException("Error processing knowledge base", e);
    }
  }
}
