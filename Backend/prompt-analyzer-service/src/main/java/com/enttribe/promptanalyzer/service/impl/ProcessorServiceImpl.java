/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.ProcessorDao;
import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Processor;
import com.enttribe.promptanalyzer.service.ProcessorService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.ProcessorUtils;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link ProcessorService} interface. This service class provides the
 * business logic for managing processors, including operations for searching, creating, updating,
 * and deleting processors.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@RequiredArgsConstructor
public class ProcessorServiceImpl implements ProcessorService {

  private static final Logger log = LoggerFactory.getLogger(ProcessorServiceImpl.class);
  private final ProcessorDao processorDao;
  private final CustomFilter customFilter;

  @Override
  public Map<String, Object> save(ProcessorRequestDto requestDto) {
    log.debug("Starting to save processor with key: {}", requestDto.getKey());
    try {
      Processor processor = new Processor();
      ProcessorUtils.mapDtoToEntity(requestDto, processor);
      processor.setCreatedTime(new Date());
      processor.setModifiedTime(new Date());
      processor.setNanoId(NanoIdUtils.randomNanoId());
      processorDao.save(processor);
      log.info("Successfully saved processor with key: {}", requestDto.getKey());
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (Exception e) {
      if (e.getMessage().contains("unique_key")) {
        log.error("Duplicate entry found for processor key: {}", requestDto.getKey());
        throw new BusinessException("duplicate entry for " + requestDto.getKey());
      }
      log.error("Error saving processor with key {}: {}", requestDto.getKey(), e.getMessage(), e);
      throw new BusinessException("Failed to save processor");
    }
  }

  @Override
  public Map<String, Object> update(String nanoId, ProcessorRequestDto requestDto) {
    log.debug("Starting to update processor with id: {} and key: {}", nanoId, requestDto.getKey());

    Processor processor =
        Optional.ofNullable(processorDao.findByNanoId(nanoId))
            .orElseThrow(
                () -> {
                  log.error("Processor not found with id: {}", nanoId);
                  return new ResourceNotFoundException(
                      "processor is not found with id : " + nanoId);
                });
    try {
      ProcessorUtils.mapDtoToEntity(requestDto, processor);
      processor.setModifiedTime(new Date());
      processorDao.save(processor);
      log.info(
          "Successfully updated processor with id: {} and key: {}", nanoId, requestDto.getKey());
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (Exception e) {
      if (e.getMessage().contains("unique_key")) {
        log.error("Duplicate entry found for processor key: {}", requestDto.getKey());
        throw new BusinessException("duplicate entry for " + requestDto.getKey());
      }
      log.error("Error updating processor with id {}: {} ", nanoId, e.getMessage(), e);
      throw new BusinessException("Failed to update processor");
    }
  }

  @Override
  public List<ProcessorResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug(
        "Searching processors with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
        filter,
        offset,
        size,
        orderBy,
        orderType);
    if (filter != null) {
      filter = filter.replace("(", "").replace(")", "");
      log.debug("Cleaned filter: {}", filter);
    }
    try {
      List<Processor> processors =
          customFilter.searchByFilter(Processor.class, filter, orderBy, orderType, offset, size);
      log.info("Found {} processors matching the search criteria", processors.size());
      return ProcessorUtils.getProcessorList(processors);
    } catch (Exception e) {
      log.error("Error searching processors: {}", e.getMessage(), e);
      throw new BusinessException("Failed searching processors");
    }
  }

  @Override
  public Long count(String filter) {
    log.debug("Counting processors with filter: {}", filter);
    if (filter != null) {
      filter = filter.replace("(", "").replace(")", "");
      log.debug("Cleaned filter: {}", filter);
    }
    try {
      Long count = customFilter.countByFilter(Processor.class, filter);
      log.info("Found {} processors matching the count criteria", count);
      return count;
    } catch (Exception e) {
      log.error("Error counting processors: {}", e.getMessage(), e);
      throw new BusinessException("Failed count processors");
    }
  }
}
