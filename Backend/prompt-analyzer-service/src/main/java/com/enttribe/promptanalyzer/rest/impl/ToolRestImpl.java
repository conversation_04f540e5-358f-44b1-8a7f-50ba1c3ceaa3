/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.microintent.MicroIntentResponseDto;
import com.enttribe.promptanalyzer.dto.tool.SwaggerDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.dto.tool.ToolWorkflowDto;
import com.enttribe.promptanalyzer.rest.ToolRest;
import com.enttribe.promptanalyzer.service.ToolService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * REST controller for managing tool operations. Provides endpoints for creating, updating,
 * searching, and managing tools, including support for Swagger integration, workflow generation,
 * and import/export functionality. All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/tool")
@RequiredArgsConstructor
public class ToolRestImpl implements ToolRest {

  private final ToolService toolService;
  private static final String STATUS = "status";
  private static final String SUCCESS = "success";
  private static final String FAILED = "failed";
  private static final String MESSAGE = "message";

  /**
   * Saves a new tool.
   *
   * @param toolDto the tool data transfer object to be saved
   * @return a map containing the result of the save operation
   */
  @Override
  public Map<String, String> save(ToolDto toolDto) {
    log.info("Saving tool");
    return toolService.createTool(toolDto);
  }

  /**
   * Creates tools from a Swagger definition.
   *
   * @param swaggerDto the Swagger data transfer object containing tool definitions
   * @return a response entity containing the result of the creation
   */
  @Override
  public ResponseEntity<Map<String, String>> createToolFromSwagger(SwaggerDto swaggerDto) {
    Map<String, String> response = new HashMap<>();
    try {
      log.info("Creating tools from Swagger json");
      Map<String, Integer> result = toolService.generateTools(swaggerDto);
      response.put(STATUS, SUCCESS);
      response.put(
          MESSAGE,
          String.format(
              "tools created || success : %s, failed : %s",
              result.get(SUCCESS), result.get(FAILED)));
      return ResponseEntity.status(HttpStatus.OK).body(response);
    } catch (Exception e) {
      log.error("Failed to create tools from Swagger: {}", e.getMessage());
      response.put(STATUS, FAILED);
      response.put(MESSAGE, e.getMessage());
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

  /**
   * Searches for tools based on the provided filter and pagination options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the number of results to return
   * @param orderBy the field to order the results by
   * @param orderType the type of ordering (ascending or descending)
   * @return a list of tool converter data transfer objects matching the search criteria
   */
  @Override
  public List<ToolConvertorDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching tools");
    return toolService.search(filter, offset, size, orderBy, orderType);
  }

  /**
   * Counts the number of tools matching the provided filter.
   *
   * @param filter the search filter
   * @return the count of tools matching the filter
   */
  @Override
  public Long count(String filter) {
    return toolService.count(filter);
  }

  /**
   * Deletes a tool by its ID.
   *
   * @param map a map containing the ID of the tool to be deleted
   * @return a map containing the result of the deletion
   */
  @Override
  public Map<String, String> deleteById(Map<String, String> map) {
    log.info("Inside @method deleteById @param id: {}", map.get("id"));
    return toolService.softDelete(map.get("id"));
  }

  /**
   * Retrieves a tool by its ID.
   *
   * @param toolId the ID of the tool to retrieve
   * @return the tool converter data transfer object
   */
  @Override
  public ToolConvertorDto getToolById(String toolId) {
    log.info("Retrieving tool by ID: {}", toolId);
    return toolService.getToolById(toolId);
  }

  @Override
  public List<MicroIntentResponseDto> getX101ToolList() {
    log.debug("Inside @method getX101ToolList");
    return toolService.getX101ToolList();
  }

  /**
   * Changes the status of a tool.
   *
   * @param requestMap a map containing the ID and new status of the tool
   * @return a map containing the result of the status change
   */
  @Override
  public Map<String, String> changeToolStatus(Map<String, Object> requestMap) {
    String id = (String) requestMap.get("id");
    String status = (String) requestMap.get(STATUS);
    log.info("Changing status of tool ID: {} to status: {}", id, status);
    return toolService.changeToolStatus(id, status);
  }

  /**
   * Updates an existing tool.
   *
   * @param tool the tool data transfer object containing updated information
   * @return a map containing the result of the update operation
   */
  @Override
  public Map<String, String> update(ToolDto tool) {
    log.info("Updating tool: {}", tool.getId());
    return toolService.updateTool(tool);
  }

  /**
   * Creates tools from a workflow definition.
   *
   * @param toolWorkflowDto the workflow data transfer object containing tool definitions
   * @return a response entity containing the result of the creation
   */
  @Override
  public ResponseEntity<Map<String, String>> createToolFromWorkflow(
      ToolWorkflowDto toolWorkflowDto) {
    Map<String, String> response = new HashMap<>();
    try {
      log.info("Creating tools from workflow");
      toolService.generateToolsFromWorkflow(toolWorkflowDto);
      response.put(STATUS, SUCCESS);
      response.put(MESSAGE, "Tool created successfully");
      return ResponseEntity.status(HttpStatus.OK).body(response);
    } catch (Exception e) {
      log.error("Failed to create tools from workflow: {}", e.getMessage());
      response.put(STATUS, FAILED);
      response.put(MESSAGE, e.getMessage());
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
  }

  /**
   * Retrieves tools by their IDs.
   *
   * @param ids the list of tool IDs to retrieve
   * @return a list of tool data transfer objects corresponding to the provided IDs
   */
  @Override
  public List<ToolDtoSdk> getToolsByIds(List<Integer> ids) {
    log.info("Retrieving tools by IDs: {}", ids);
    return toolService.getToolsByIds(ids);
  }

  /**
   * Retrieves tools by their IDs (version 1).
   *
   * @param ids the list of tool IDs to retrieve
   * @return a list of tool converter data transfer objects corresponding to the provided IDs
   */
  @Override
  public List<ToolConvertorDto> getToolsByIdsV1(List<String> ids) {
    log.info("Retrieving tools by IDs V1: {}", ids);
    return toolService.getToolsByIdsV1(ids);
  }

  /**
   * Checks the compilation status of a given source code and class name.
   *
   * @param requestMap a map containing the source code and class name
   * @return a map indicating the result of the compilation check
   */
  @Override
  public Map<String, Object> checkCompilation(Map<String, String> requestMap) {
    String sourceCode = requestMap.get("sourceCode");
    String className = requestMap.get("className");
    log.info("Checking compilation for class: {}", className);
    return toolService.checkCompilation(sourceCode, className);
  }

  /**
   * Exports a tool associated with a specific application.
   *
   * @param appName the name of the application
   * @return a response entity containing the exported tool resource
   */
  @Override
  public ResponseEntity<Resource> exportTool(String appName) {
    log.info("Exporting tool for application: {}", appName);
    return toolService.exportTool(appName);
  }

  /**
   * Imports a tool from a file.
   *
   * @param file the multipart file containing the tool data
   * @return a response entity containing the result of the import operation
   */
  @Override
  public ResponseEntity<Resource> importTool(MultipartFile file) {
    log.info("Importing tool from file: {}", file.getOriginalFilename());
    return toolService.importTool(file);
  }

  /**
   * Checks the existence of a tool by its name.
   *
   * @param requestMap a map containing the tool name
   * @return a map indicating whether the tool exists
   */
  @Override
  public Map<String, Boolean> existsTool(Map<String, String> requestMap) {
    String toolName = requestMap.get("toolName");
    log.info("Checking existence of tool: {}", toolName);
    return toolService.existsTool(toolName);
  }

  /**
   * Updates tags for a tool by its ID.
   *
   * @param id the ID of the tool
   * @param tags a map containing the tags to update
   * @return a map containing the result of the update operation
   */
  @Override
  public Map<String, String> updateTagById(String id, Map<String, String> tags) {
    log.info("Updating tags for tool ID: {}", id);
    return toolService.updateTagById(id, tags);
  }

  @Override
  public ToolDto getToolByName(String toolName) {
    return toolService.getToolByName(toolName);
  }

  @Override
  public List<ToolDto> findToolsByIds(List<Integer> ids) {
    return toolService.findToolsByIds(ids);
  }
}
