/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import com.enttribe.promptanalyzer.model.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for handling Tag entity and DTO conversions. Provides methods for mapping between
 * Tag entities and DTOs.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
public class TagUtils {

  /** Private constructor to prevent instantiation of utility class. */
  private TagUtils() {}

  /**
   * Maps data from a TagRequestDto to a Tag entity.
   *
   * @param tag The target Tag entity to be updated
   * @param tagRequestDto The source DTO containing tag data
   * @return The updated Tag entity
   */
  public static Tag mapDtoToEntity(Tag tag, TagRequestDto tagRequestDto) {
    tag.setName(tagRequestDto.getName());
    tag.setType(tagRequestDto.getType());
    if (tag.getNanoId() == null) {
      tag.setNanoId(NanoIdUtils.randomNanoId());
    }
    return tag;
  }

  /**
   * Converts a Tag entity to a TagResponseDto.
   *
   * @param tag The Tag entity to convert
   * @return A new TagResponseDto containing the tag data, or null if the input is null
   */
  public static TagResponseDto convertToDTO(Tag tag) {
    if (tag == null) {
      return null;
    }
    return TagResponseDto.builder()
        .id(tag.getNanoId())
        .name(tag.getName())
        .type(tag.getType())
        .build();
  }

  /**
   * Converts a list of Tag entities to a list of TagResponseDtos.
   *
   * @param tags The list of Tag entities to convert
   * @return A list of TagResponseDtos, or an empty list if the input is null or empty
   */
  public static List<TagResponseDto> getTagList(List<Tag> tags) {
    if (tags == null || tags.isEmpty()) {
      return List.of();
    }
    return tags.stream().map(TagUtils::convertToDTO).toList();
  }
}
