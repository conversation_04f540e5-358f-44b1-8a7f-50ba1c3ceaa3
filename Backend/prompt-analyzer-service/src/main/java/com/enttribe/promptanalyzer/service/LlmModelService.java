/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import java.util.List;

/**
 * Manages Language Learning Model (LLM) configurations and operations. This service handles model
 * management across different providers, including model creation, retrieval, and SDK-specific
 * operations.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface LlmModelService {

  /**
   * Retrieves models by provider name.
   *
   * @param provider the provider name
   * @return a ProviderModelDto containing models for the provider
   */
  ProviderModelDto getModelsByProvider(String provider);

  /**
   * Retrieves all providers with their models.
   *
   * @return a list of ProviderModelDto objects
   */
  List<ProviderModelDto> getAllProvidersWithModels();

  /**
   * Retrieves unique inferences by type.
   *
   * @param type the type of inference
   * @return a list of LlmModelSdkDto objects
   */
  List<LlmModelSdkDto> getUniqueInferencesByType(String type);

  /**
   * Retrieves LLM models for SDK by application name and type.
   *
   * @param appName the application name
   * @param type the type of model
   * @return a list of LlmModelSdkDto objects
   */
  List<LlmModelSdkDto> getLlmModelsForSDK(String appName, String type);

  /**
   * Retrieves LLM models by type for SDK.
   *
   * @param type the type of model
   * @return a list of LlmModelSdkDto objects
   */
  List<LlmModelSdkDto> getLlmModelsByTypeForSDK(String type);
}
