/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestSuiteMetrics {
  private int testsPassed = 0;
  private int testsFailed = 0;
  private double totalAccuracy = 0.0;
  private long totalLatency = 0L;
  private int totalPromptTokens = 0;
  private int totalGenerationTokens = 0;

  public void incrementTestsPassed() {
    this.testsPassed++;
  }

  public void incrementTestsFailed() {
    this.testsFailed++;
  }

  public void addAccuracy(double accuracy) {
    this.totalAccuracy += accuracy;
  }

  public void addLatency(long latency) {
    this.totalLatency += latency;
  }

  public void addPromptTokens(int tokens) {
    this.totalPromptTokens += tokens;
  }

  public void addGenerationTokens(int tokens) {
    this.totalGenerationTokens += tokens;
  }
}
