/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Tool;
import com.enttribe.promptanalyzer.util.test_case.DynamicBeanRegistrar;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ToolCallbackService {

  private static final Logger log = LoggerFactory.getLogger(ToolCallbackService.class);
  private final ApplicationContext applicationContext;

  public List<ToolCallback> registerToolCallback(List<Tool> tools) {
    log.info("inside @method registerToolCallback");
    DynamicBeanRegistrar dynamicBeanRegistrar = new DynamicBeanRegistrar(applicationContext);
    return dynamicBeanRegistrar.registerToolCallbacks(tools);
  }

  public ToolCallback getToolCallback(Tool tool) {
    log.info("inside @method getToolCallback");
    if (tool == null) {
      throw new ResourceNotFoundException("tool not found");
    }
    DynamicBeanRegistrar dynamicBeanRegistrar = new DynamicBeanRegistrar(applicationContext);
    List<ToolCallback> toolCallbacks = dynamicBeanRegistrar.registerToolCallbacks(List.of(tool));
    return toolCallbacks.getFirst();
  }
}
