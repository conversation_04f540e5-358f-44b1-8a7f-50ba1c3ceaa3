/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.AgentHistoryDao;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.service.AgentHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgentHistoryServiceImpl implements AgentHistoryService {

  private final AgentHistoryDao agentHistoryDao;

  @Override
  public AgentHistory getAgentHistory(String agentName) {
    log.debug("Retrieving agent history for agent: {}", agentName);
    try {
      AgentHistory history = agentHistoryDao.getAgentHistory(agentName).orElse(null);
      if (history == null) {
        log.debug("No history found for agent: {}", agentName);
      } else {
        log.debug("Successfully retrieved history for agent: {}", agentName);
      }
      return history;
    } catch (Exception e) {
      log.error("Error retrieving agent history for agent {}: {}", agentName, e.getMessage(), e);
      throw new BusinessException("Error retrieving agent history");
    }
  }
}
