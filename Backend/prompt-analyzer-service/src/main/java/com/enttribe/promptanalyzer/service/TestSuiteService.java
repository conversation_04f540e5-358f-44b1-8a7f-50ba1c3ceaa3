/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteResponseDto;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface TestSuiteService {

  Map<String, String> create(TestSuiteRequestDto requestDto);

  Map<String, String> update(TestSuiteRequestDto requestDto);

  List<TestSuiteResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  Long count(String filter);

  Map<String, String> deleteById(String nanoId);

  Map<String, String> addAgentTestCasesToSuite(TestSuiteRequestDto requestDto);

  ResponseEntity<Resource> exportTestCasesBySuiteId(String suiteId);

  ResponseEntity<Resource> importTestCasesToSuite(String suiteId, MultipartFile file)
      throws IOException;
}
