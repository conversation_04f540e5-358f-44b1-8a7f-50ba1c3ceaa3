/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.model.AgentHistory;

/** Service interface for managing agent history operations. Author: VisionWaves Version: 1.0 */
public interface AgentHistoryService {

  /**
   * Retrieves the history of a specific agent by name.
   *
   * @param agentName the name of the agent whose history is to be retrieved
   * @return the AgentHistory object containing the agent's history information
   */
  AgentHistory getAgentHistory(String agentName);
}
