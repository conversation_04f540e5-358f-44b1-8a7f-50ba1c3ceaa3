package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.exception.BusinessException;
import feign.utils.ExceptionUtils;
import jakarta.validation.ConstraintViolationException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.transaction.TransactionSystemException;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Map;

/**
 * Utility class for handling exceptions in the Prompt Analyzer service.
 * It provides methods to handle custom exceptions and map validation messages.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ExceptionUtil {

    private static final Map<String, String> VALIDATION_MESSAGES = Map.of(
            "topK must be in the range of 1 to 15", "topK must be in the range of 1 to 15",
            "similarity threshold is required", "Similarity threshold is required",
            "topk is required", "topK is required",
            "invalid name: only a–z, 0–9, _ , and - are allowed", "Invalid name: only a–z, 0–9, _ , and - are allowed",
            "duplicate entry", "Name already exists"
    );

    public static void handleCustomException(Exception e) {
        handleException(e);
    }

    private static void handleException(Exception e) {
        Throwable rootCause = ExceptionUtils.getRootCause(e);
        String message = (rootCause != null && rootCause.getMessage() != null)
                ? rootCause.getMessage()
                : "";
        // Handle ConstraintViolationException
        if (rootCause instanceof ConstraintViolationException constraintEx) {
            constraintEx.getConstraintViolations().forEach(violation -> {
                String interpolatedMessage = violation.getMessage();
                VALIDATION_MESSAGES.forEach((key, value) -> {
                    if (interpolatedMessage.equalsIgnoreCase(key)) {
                        throw new BusinessException(value);
                    }
                });
            });
        }
        // Handle common validation-related exceptions
        if (rootCause instanceof TransactionSystemException
                || rootCause instanceof DataIntegrityViolationException
                || rootCause instanceof SQLIntegrityConstraintViolationException) {
            VALIDATION_MESSAGES.forEach((key, value) -> {
                if (message.toLowerCase().contains(key.toLowerCase())) {
                    throw new BusinessException(value);
                }
            });
        }
    }

}
