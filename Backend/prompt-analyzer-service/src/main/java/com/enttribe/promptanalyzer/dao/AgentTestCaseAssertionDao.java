/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.model.AgentTestCaseAssertion;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AgentTestCaseAssertionDao extends JpaRepository<AgentTestCaseAssertion, Long> {

  @Query(
      """
            SELECT a
            FROM AgentTestCaseAssertion a
            WHERE a.agentTestCase.name = :testCaseName
            AND a.assertion.matchStrategy = :matchStrategy
            AND a.assertion.assertionType = :assertionType
            AND a.parameterName = :parameterName
            """)
  Optional<AgentTestCaseAssertion> findByTestCaseNameAndStrategyAndType(
      @Param("testCaseName") String testCaseName,
      @Param("matchStrategy") MatchStrategy matchStrategy,
      @Param("assertionType") AssertionType assertionType,
      @Param("parameterName") String parameterName);
}
