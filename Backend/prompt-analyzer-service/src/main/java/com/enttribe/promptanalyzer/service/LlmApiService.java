/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;
import java.util.Map;

/**
 * Handles interactions with Language Learning Model (LLM) APIs. This service manages chat
 * completions and prompt executions, providing a clean interface for communicating with LLM
 * services.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface LlmApiService {

  /**
   * Performs a chat completion using the provided request DTO.
   *
   * @param requestDto the chat completion request DTO
   * @return the result of the chat completion (could be a response object or string)
   */
  Object chatCompletion(ChatCompletionRequestDto requestDto);

  /**
   * Executes a prompt with the given prompt ID and variable map, with optional formatting.
   *
   * @param promptId the prompt ID
   * @param variableMap the map of variables for the prompt
   * @param format whether to format the result
   * @return the result of the prompt execution
   */
  Object executePrompt(String promptId, Map<String, Object> variableMap, Boolean format);

  /**
   * Executes a prompt (version 1) with the given prompt ID and variable map.
   *
   * @param promptId the prompt ID
   * @param variableMap the map of variables for the prompt
   * @return the result of the prompt execution as a string
   */
  String executePromptV1(String promptId, Map<String, Object> variableMap);

  /**
   * Generates a fresh system prompt based on user input and type.
   *
   * @param userInput the user input
   * @param type the type of prompt
   * @return the generated system prompt as a string
   */
  String generateFreshSystemPrompt(String userInput, String type);

  /**
   * Improves an existing system prompt based on user input, old prompt, and type.
   *
   * @param userInput the user input
   * @param oldPrompt the old system prompt
   * @param type the type of prompt
   * @return the improved system prompt as a string
   */
  String improveSystemPrompt(String userInput, String oldPrompt, String type);

  /**
   * Generates a test case for a prompt.
   *
   * @param promptId the prompt ID
   * @param request the request map containing test case details
   * @return a map containing the generated test case and related information
   */
  Map<String, Object> generateTestCase(String promptId, Map<String, String> request);
}
