/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.prompt.PromptFooDto;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import java.util.List;

/**
 * Utility class for formatting messages and converting Prompt entities to PromptFooDto objects.
 * Provides methods to format message lists and convert individual entities and lists of entities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public final class PromptFooUtils {

  // Private constructor to prevent instantiation
  private PromptFooUtils() {}

  /**
   * Formats a list of Message objects into a single string. Each message is formatted as "role :
   * content" and separated by a newline.
   *
   * @param messages the list of Message objects to format
   * @return a formatted string of messages, or an empty string if the input is null or empty
   */
  public static String formatMessages(List<Message> messages) {
    if (messages == null || messages.isEmpty()) {
      return "";
    }

    StringBuilder result = new StringBuilder();

    for (Message message : messages) {
      if (message != null && message.getRole() != null && message.getContent() != null) {
        result.append(message.getRole()).append(" : ").append(message.getContent()).append("\n");
      }
    }

    return result.toString().trim();
  }

  /**
   * Converts a single Prompt entity to a PromptFooDto object.
   *
   * @param prompt the Prompt entity to convert
   * @return the converted PromptFooDto object
   */
  public static PromptFooDto getPromptFooDto(Prompt prompt) {
    return PromptFooDto.builder()
        .id(prompt.getNanoId())
        .application(prompt.getApplication())
        .name(prompt.getName())
        .category(prompt.getCategory())
        .temperature(prompt.getTemperature())
        .maxToken(prompt.getMaxToken())
        .version(prompt.getVersion())
        .topP(prompt.getTopP())
        .prompt(formatMessages(prompt.getMessages()))
        .reasoningEffort(prompt.getReasoningEffort())
        .build();
  }

  /**
   * Converts a list of Prompt entities to a list of PromptFooDto objects.
   *
   * @param prompts the list of Prompt entities to convert
   * @return a list of converted PromptFooDto objects
   */
  public static List<PromptFooDto> getPromptFooDtoList(List<Prompt> prompts) {
    return prompts.stream().map(PromptFooUtils::getPromptFooDto).toList();
  }
}
