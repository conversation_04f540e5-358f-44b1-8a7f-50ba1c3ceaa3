/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * DAO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Access Object (DAO) classes and interfaces for managing
 * database operations in the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Repository and DAO interfaces</li>
 *   <li>Support for CRUD and custom queries</li>
 *   <li>Facilitation of data access and persistence</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>DAO classes are used by services and managers to perform database operations
 * and manage persistent data.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dao;
