/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import java.util.Map;

/**
 * Handles custom agent operations and query planning. This service manages the generation of plans
 * based on user queries and creates trigger name descriptions for custom agents.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface CustomAgentService {
  /**
   * Generates a plan for a user query using a custom agent.
   *
   * @param agent the custom agent DTO containing query details
   * @return a map containing the generated plan and related information
   */
  Map<String, String> getPlanforUserQuery(CustomAgentDto agent);

  /**
   * Creates a trigger name and description based on a user query.
   *
   * @param userQuery the user query string
   * @return a map containing the trigger name and its description
   */
  Map<String, String> createTriggerNameDescription(String userQuery);
}
