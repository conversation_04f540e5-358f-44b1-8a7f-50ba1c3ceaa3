/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.model.Prompt;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.tool.ToolCallback;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestExecutionContext {
  private Prompt prompt;
  private List<Message> messageList;
  private ChatModel chatModel;
  private String userQuestion;
  private String model;
  private List<ToolCallback> toolCallbacks;
  private List<ToolCallback> knowledgeBaseTools;
  private boolean shouldExecuteTool;
  private Long timeTakenMillis;
}
