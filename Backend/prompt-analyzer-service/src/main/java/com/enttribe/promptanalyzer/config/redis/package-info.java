/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Redis Configuration package for the Prompt Analyzer application.
 *
 * <p>This package contains configuration classes for integrating Redis as a caching
 * and data storage solution in the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Redis connection and integration configuration</li>
 *   <li>Support for caching and data storage</li>
 *   <li>Custom Redis property management</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Redis configuration classes are used by the application to manage Redis connections
 * and caching strategies.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.config.redis;
