/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import java.util.List;

/**
 * Manages trigger operations and event handling. This service handles the creation and management
 * of triggers, providing functionality for trigger updates, searching, and tracking with support
 * for pagination and filtering.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TriggerService {

  /**
   * Searches for triggers based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of trigger response DTOs
   */
  List<TriggerResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);
}
