/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.TestCaseResultDao;
import com.enttribe.promptanalyzer.model.TestCaseResult;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class TestCaseResultBatchService {

  private final TestCaseResultDao testCaseResultDao;

  @PersistenceContext private EntityManager entityManager;

  public TestCaseResultBatchService(TestCaseResultDao testCaseResultDao) {
    this.testCaseResultDao = testCaseResultDao;
  }

  @Transactional(Transactional.TxType.REQUIRES_NEW)
  public void saveBatch(List<TestCaseResult> batch) {
    testCaseResultDao.saveAll(batch);
    entityManager.flush();
    entityManager.clear();
  }
}
