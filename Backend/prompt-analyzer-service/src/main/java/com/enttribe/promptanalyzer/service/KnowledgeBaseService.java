/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.List;
import java.util.Map;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Manages knowledge base operations and document handling. This service provides comprehensive
 * functionality for managing knowledge bases, including CRUD operations for documents and knowledge
 * base entries, along with search capabilities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface KnowledgeBaseService {

  /**
   * Retrieves a knowledge base entry by its integer ID.
   *
   * @param id the knowledge base ID
   * @return the knowledge base response DTO
   */
  KnowledgeBaseResponseDto getKnowledgeBaseByIdV1(String id);

  KnowledgeBaseResponseDto getKnowledgeBaseById(Integer id);

  /**
   * Saves a new document to the knowledge base.
   *
   * @param documentDto the document request DTO
   * @return a map containing the result of the save operation
   */
  Map<String, String> saveDocument(DocumentRequestDto documentDto);

  /**
   * Counts the number of knowledge base entries matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching entries
   */
  Long count(String filter);

  /**
   * Searches for knowledge base entries based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of knowledge base response DTOs
   */
  List<KnowledgeBaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Updates an existing document in the knowledge base.
   *
   * @param nanoId the nano ID of the document
   * @param documentDto the updated document request DTO
   * @return a map containing the result of the update operation
   */
  Map<String, String> updateDocument(String nanoId, DocumentRequestDto documentDto)
      throws JsonProcessingException;

  /**
   * Soft deletes a knowledge base entry by its integer ID.
   *
   * @param id the knowledge base ID
   * @return a map containing the result of the delete operation
   */
  Map<String, String> softDelete(String id);

  /**
   * Retrieves knowledge base entries by a list of integer IDs.
   *
   * @param ids the list of knowledge base IDs
   * @return a list of knowledge base SDK DTOs
   */
  List<KnowledgeBaseSdkDto> getKnowledgeBaseByIds(List<String> ids);

  /**
   * Saves content in the vector store.
   *
   * @param contents the list of content strings
   * @return a vector response DTO
   */
  VectorResponseDto saveInVector(List<String> contents);

  /**
   * Updates tags for a knowledge base entry by its integer ID.
   *
   * @param id the knowledge base ID
   * @param tags the tags to update
   * @return a map containing the result of the update operation
   */
  Map<String, String> updateTagById(String id, Map<String, String> tags);

  /**
   * Retrieves the names of tables for a given name.
   *
   * @param name the name or context
   * @return a list of table names
   */
  List<String> getTablesName(String name);

  /**
   * Checks if a website URL exists in the knowledge base.
   *
   * @param webSiteUrl the website URL
   * @return a map indicating existence and related information
   */
  Map<String, Object> existsWebsiteUrl(String webSiteUrl);

  /**
   * Saves content to the knowledge base.
   *
   * @param dto the document request DTO
   * @return a map containing the result of the save operation
   */
  Map<String, String> saveContent(DocumentRequestDto dto);

  /**
   * Imports CSV data to Milvus vector database.
   *
   * @param file the multipart file containing CSV data
   * @return a ResponseEntity containing the import result
   */
  ResponseEntity<String> milvusImportCsv(MultipartFile file);

  /**
   * Gets an answer from the knowledge base using vector search.
   *
   * @param knowledgeBaseName the knowledge base name
   * @param userQuestion the user question
   * @return a map containing the answer
   */
  Map<String, String> getAnswer(String knowledgeBaseName, String userQuestion);

  /**
   * Exports knowledge bases by their integer IDs.
   *
   * @param ids the list of knowledge base IDs
   * @return a ResponseEntity containing the exported resource
   */
  ResponseEntity<Resource> exportKnowledgeBases(List<String> ids);

  /**
   * Imports knowledge bases from a file.
   *
   * @param file the multipart file containing knowledge bases
   * @return a ResponseEntity containing the import result
   */
  ResponseEntity<Resource> importKnowledgeBases(MultipartFile file);

  /**
   * Updates the return direct flag for a knowledge base entry by its integer ID.
   *
   * @param id the knowledge base ID
   * @param returnDirectValue the new value for the return direct flag
   * @return a map containing the result of the update operation
   */
  Map<String, String> updateReturnDirect(String id, Boolean returnDirectValue);

  /**
   * Saves a website to the knowledge base.
   *
   * @param documentDto the document request DTO
   * @return a map containing the result of the save operation
   */
  Map<String, String> saveWebSite(DocumentRequestDto documentDto);

  /**
   * Checks if a knowledge base name exists.
   *
   * @param name the knowledge base name
   * @return a map indicating existence
   */
  Map<String, Boolean> existsKnowledgeBaseName(String name);

  byte[] downloadFileFromS3(String fileName);
}
