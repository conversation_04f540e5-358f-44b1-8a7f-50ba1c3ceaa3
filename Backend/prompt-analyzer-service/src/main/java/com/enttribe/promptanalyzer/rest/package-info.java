/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * REST API package for the Prompt Analyzer application.
 *
 * <p>This package contains REST controller classes and interfaces for exposing and consuming
 * API endpoints in the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>REST controller and API endpoint definitions</li>
 *   <li>Support for CRUD and business operations</li>
 *   <li>Integration with OpenAPI/Swagger</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>REST API classes are used by clients and other services to interact with the Prompt Analyzer
 * application's business logic and data over HTTP.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.rest;
