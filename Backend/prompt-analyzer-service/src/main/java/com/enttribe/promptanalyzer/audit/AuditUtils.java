/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: ************

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.audit;

import java.util.UUID;

public final class AuditUtils {

  private AuditUtils() {
    throw new UnsupportedOperationException("utility class can not be instantiated");
  }

  private static final String APPLICATION_NAME = "PROMPT_ANALYZER_APP_NAME";

  /**
   * Generates an audit ID by combining the application name with either a provided audit ID or a
   * new UUID.
   *
   * @param auditId The existing audit ID to use, or null to generate a new UUID
   * @return A formatted string containing the application name and audit ID
   */
  public static String generateAuditId(String auditId) {
    String resolvedAuditId = auditId == null ? UUID.randomUUID().toString() : auditId;
    return String.format("%s_%s", APPLICATION_NAME, resolvedAuditId);
  }
}
