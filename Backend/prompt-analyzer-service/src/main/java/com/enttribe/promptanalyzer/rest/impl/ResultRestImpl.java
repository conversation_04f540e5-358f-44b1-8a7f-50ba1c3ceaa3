/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.result.AssertionResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestCaseResultResponseDto;
import com.enttribe.promptanalyzer.dto.result.TestResultResponseDto;
import com.enttribe.promptanalyzer.rest.ResultRest;
import com.enttribe.promptanalyzer.service.ResultService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/result")
@RequiredArgsConstructor
public class ResultRestImpl implements ResultRest {

  private final ResultService resultService;

  // AssertionResult
  @Override
  public List<AssertionResultResponseDto> searchAssertionResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching assertion results");
    return resultService.searchAssertionResults(filter, offset, size, orderBy, orderType);
  }

  @Override
  public Long countAssertionResults(String filter) {
    log.info("Counting assertion results");
    return resultService.countAssertionResults(filter);
  }

  // TestCaseResult
  @Override
  public List<TestCaseResultResponseDto> searchTestCaseResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching test case results");
    return resultService.searchTestCaseResults(filter, offset, size, orderBy, orderType);
  }

  @Override
  public Long countTestCaseResults(String filter) {
    log.info("Counting test case results");
    return resultService.countTestCaseResults(filter);
  }

  // TestResult
  @Override
  public List<TestResultResponseDto> searchTestResults(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching test results");
    return resultService.searchTestResults(filter, offset, size, orderBy, orderType);
  }

  @Override
  public Long countTestResults(String filter) {
    log.info("Counting test results");
    return resultService.countTestResults(filter);
  }
}
