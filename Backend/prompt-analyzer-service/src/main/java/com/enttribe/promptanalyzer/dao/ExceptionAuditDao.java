/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.ExceptionAudit;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * Data Access Object interface for ExceptionAudit entity operations.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface ExceptionAuditDao extends JpaRepository<ExceptionAudit, Integer> {}
