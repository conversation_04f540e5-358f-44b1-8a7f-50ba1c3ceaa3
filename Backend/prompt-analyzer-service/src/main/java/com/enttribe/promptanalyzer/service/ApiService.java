/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.agent.AgentApiResponse;
import com.enttribe.promptanalyzer.dto.crawl.CrawlResponse;
import com.enttribe.promptanalyzer.dto.microintent.MicroIntentResponseDto;
import java.util.List;
import java.util.Map;

/**
 * Service interface for managing API operations related to web crawling. This interface defines
 * methods for triggering crawls, checking task status, retrieving table schemas, and getting table
 * names. Author: VisionWaves Version: 1.0
 */
public interface ApiService {

  /**
   * Triggers a web crawl for the specified website URL.
   *
   * @param websiteUrl the URL of the website to crawl
   * @return a string representing the crawl task ID or status
   */
  String triggerCrawl(String websiteUrl);

  /**
   * Retrieves the status of a crawl task by its task ID.
   *
   * @param taskId the ID of the crawl task
   * @return a CrawlResponse object containing the status and details of the task
   */
  CrawlResponse getTaskStatus(String taskId);

  /**
   * Retrieves the schema of a table for a given execution name and table name.
   *
   * @param executionName the execution context or job name
   * @param tableName the name of the table
   * @return a string representing the table schema
   */
  String getTableSchema(String executionName, String tableName);

  /**
   * Retrieves the names of tables for a given context or name.
   *
   * @param name the context or database name
   * @return a list of table names
   */
  List<String> getTablesName(String name);

  /**
   * Retrieves a map of available processors and their details.
   *
   * @return a map where the key is the processor name and the value is its description or details
   */
  Map<String, String> getProcessors();

  AgentApiResponse getAgentDetails(String agentId, String agentType);

  /**
   * Gets micro intent details.
   *
   * @return the micro intent details
   */
  List<MicroIntentResponseDto> getMicroIntentDetails();
}
