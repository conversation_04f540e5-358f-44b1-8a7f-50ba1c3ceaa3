/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.assertion.AssertionTemplateDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptConvertorDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptDtoSdk;
import com.enttribe.promptanalyzer.dto.prompt.PromptRequestDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVariableDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptVersionDetailsDto;
import com.enttribe.promptanalyzer.model.Prompt;
import java.util.List;
import java.util.Map;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Manages prompt-related operations and lifecycle management. This service handles all aspects of
 * prompt management including creation, versioning, searching, and variable management.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PromptService {

  /**
   * Saves a new prompt.
   *
   * @param prompt the prompt data transfer object
   * @return a map containing the result of the save operation
   */
  Map<String, String> savePrompt(PromptDto prompt);

  /**
   * Retrieves all versions of a prompt based on application, name, category, and status.
   *
   * @param application the application name
   * @param name the prompt name
   * @param category the prompt category
   * @param status the prompt status
   * @return a list of prompt version details
   */
  List<PromptVersionDetailsDto> getVersionsOfPrompt(
      String application, String name, String category, String status);

  /**
   * Soft deletes a prompt by its integer ID.
   *
   * @param id the prompt ID
   * @return a map containing the result of the delete operation
   */
  Map<String, String> softDelete(String id);

  /**
   * Retrieves a prompt by its integer ID as a PromptConvertorDto.
   *
   * @param id the prompt ID
   * @return the prompt convertor DTO
   */
  PromptConvertorDto getPromptById(String id);

  /**
   * Finds a prompt by its integer ID as a PromptDtoSdk.
   *
   * @param id the prompt ID
   * @return the prompt DTO SDK
   */
  PromptDtoSdk findPromptById(Integer id);

  /**
   * Checks if a prompt exists based on the given request DTO.
   *
   * @param promptRequestDto the prompt request DTO
   * @return a map indicating existence and related information
   */
  Map<String, Object> exists(PromptRequestDto promptRequestDto);

  /**
   * Updates the assertion template for a prompt.
   *
   * @param assertionTemplateDto the assertion template DTO
   * @return a map containing the result of the update operation
   */
  Map<String, String> updateAssertionTemplate(AssertionTemplateDto assertionTemplateDto);

  /**
   * Updates an existing prompt.
   *
   * @param prompt the prompt DTO
   * @return a map containing the result of the update operation
   */
  Map<String, String> updatePrompt(PromptDto prompt);

  /**
   * Searches for prompts based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of prompt convertor DTOs
   */
  List<PromptConvertorDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Counts the number of prompts matching a filter.
   *
   * @param filter the search filter
   * @return the count of matching prompts
   */
  Long count(String filter);

  /**
   * Retrieves prompts by application name.
   *
   * @param appName the application name
   * @return a list of prompt DTO SDKs
   */
  List<PromptDtoSdk> getPromptByApplication(String appName);

  /**
   * Gets distinct application names, optionally filtered by application name.
   *
   * @param applicationName the application name filter
   * @return a list of distinct application names
   */
  List<String> getDistinctApplications(String applicationName);

  /**
   * Gets distinct categories for a given application name.
   *
   * @param applicationName the application name
   * @return a list of distinct categories
   */
  List<String> getDistinctCategoriesByApp(String applicationName);

  /**
   * Filters prompts based on a map of filter criteria.
   *
   * @param filterMap the filter criteria
   * @return a list of prompts matching the filter
   */
  List<Prompt> filter(Map<String, Object> filterMap);

  /**
   * Searches for prompts (version 1) based on a filter and pagination/sorting options.
   *
   * @param filter the search filter
   * @param offset the offset for pagination
   * @param size the page size
   * @param orderBy the field to order by
   * @param orderType the order type (asc/desc)
   * @return a list of prompts
   */
  List<Prompt> searchV1(
      String filter, Integer offset, Integer size, String orderBy, String orderType);

  /**
   * Gets basic details of prompts by application name.
   *
   * @param applicationName the application name
   * @return a list of maps containing basic prompt details
   */
  List<Map<String, String>> getPromptBasicDetailByApplication(String applicationName);

  /**
   * Exports prompts for a given application name.
   *
   * @param appName the application name
   * @return a ResponseEntity containing the exported resource
   */
  ResponseEntity<Resource> exportPrompt(String appName);

  /**
   * Imports prompts from a file.
   *
   * @param file the multipart file containing prompts
   * @return a ResponseEntity containing the import result
   */
  ResponseEntity<Resource> importPrompt(MultipartFile file);

  /**
   * Gets prompt variables by prompt ID.
   *
   * @param promptId the prompt ID
   * @return the prompt variable DTO
   */
  PromptVariableDto getPromptVariablesById(String promptId);

  /**
   * Gets prompt variables by application name.
   *
   * @param appName the application name
   * @return a list of prompt variable DTOs
   */
  List<PromptVariableDto> getPromptVariablesByAppName(String appName);

  /**
   * Updates tags for a prompt by its integer ID.
   *
   * @param id the prompt ID
   * @param tags the tags to update
   * @return a map containing the result of the update operation
   */
  Map<String, String> updateTagById(String id, Map<String, String> tags);

  /**
   * Exports prompts by their integer IDs.
   *
   * @param promptIds the list of prompt IDs
   * @return a ResponseEntity containing the exported resource
   */
  ResponseEntity<Resource> exportPromptsByIds(List<String> promptIds);

  /**
   * Fetches a prompt by its integer ID as a PromptDto.
   *
   * @param id the prompt ID
   * @return the prompt DTO
   */
  PromptDto fetchPromptById(String id);

  /**
   * Gets a prompt by its name.
   *
   * @param promptName the prompt name
   * @return the prompt DTO
   */
  PromptDto getPromptByName(String promptName);

  /**
   * Gets a prompt by its id.
   *
   * @param id the prompt id
   * @return the prompt DTO
   */
  PromptDtoSdk getPromptByIdV1(String id);

  /**
   * Gets a prompt by its promptId.
   *
   * @param promptId the prompt promptId
   * @return the prompt DTO
   */
  PromptConvertorDto getPromptByPromptId(String promptId);
}
