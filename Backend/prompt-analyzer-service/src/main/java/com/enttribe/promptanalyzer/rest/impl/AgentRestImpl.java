/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.promptanalyzer.dao.TestResultDao;
import com.enttribe.promptanalyzer.dao.TestSuiteDao;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.model.TestResult;
import com.enttribe.promptanalyzer.model.TestSuite;
import com.enttribe.promptanalyzer.rest.AgentRest;
import com.enttribe.promptanalyzer.service.AgentHistoryService;
import com.enttribe.promptanalyzer.service.CustomAgentService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.enttribe.promptanalyzer.service.TestExecutionServiceV1;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing agent-related operations. Provides endpoints for creating agents,
 * searching agents, and managing NiFi triggers.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/agent")
@RequiredArgsConstructor
public class AgentRestImpl implements AgentRest {

  private final CustomAgentService customAgentService;
  private final NifiFlowService nifiFlowService;
  private final AgentHistoryService agentHistoryService;
  private final TestExecutionServiceV1 testExecutionServiceV1;
  private final TestResultDao testResultDao;
  private final TestSuiteDao testSuiteDao;

  @Override
  public Map<String, String> executeTestSuite(Map<String, Object> request) {
    log.debug("Inside @method executeTestSuite");
    try {
      String agentTestSuiteId = (String) request.get("agentTestSuiteId");
      String model = (String) request.get("model");
      String provider = (String) request.get("provider");
      String description = (String) request.get("description");
      Integer numberOfRuns = (Integer) request.get("numberOfRuns");
      TestResult testResult = new TestResult();

      testResult.setDescription(description);
      testResult.setCreatedTime(new Date());
      testResult.setModifiedTime(new Date());
      testResult.setNumberOfRuns(numberOfRuns);
      testResult.setModel(model);
      testResult.setProvider(provider);
      testResult.setNanoId(NanoIdUtils.randomNanoId());
      testResult.setStatus("IN_PROGRESS");
      testResult = testResultDao.save(testResult);

      testResult.setExecutionId("TestResult_" + testResult.getId());
      TestSuite testSuite =
          testSuiteDao
              .findByNanoIdWithAll(agentTestSuiteId)
              .orElseThrow(() -> new ResourceNotFoundException("test suite not found"));
      testResult.setTestSuite(testSuite);
      if (testSuite.getAgentTestCases().isEmpty()) {
        testResult.setStatus("COMPLETED");
      }
      testResult = testResultDao.save(testResult);
      testExecutionServiceV1.executeTestSuite(agentTestSuiteId, testResult.getId(), provider);
      return Map.of("result", "executed");
    } catch (Exception e) {
      log.error("failed to execute test suite : {}", e.getMessage(), e);
      throw new BusinessException("failed to execute test suite", e);
    }
  }

  @Override
  public Map<String, String> reExecuteTest(Map<String, Object> request) {
    testExecutionServiceV1.reExecuteTest(request);
    return Map.of("result", "executed");
  }

  @Override
  public List<String> getInputSchema(Map<String, String> request) {
    log.debug("Inside @method getInputSchema. @param -> request : {}", request);
    String name = request.get("name");
    String type = request.get("type");
    List<String> inputSchema = testExecutionServiceV1.getInputSchema(name, type);
    log.debug("Successfully retrieved input schema");
    return inputSchema;
  }

  /**
   * Generates a plan based on user query for custom agent.
   *
   * @param agent Custom agent details containing the user query
   * @return Map containing the generated plan response
   */
  @Override
  public Map<String, String> getPlanforUserQuery(CustomAgentDto agent) {
    log.info("Received request to generate plan for user query: {}", agent.getUserQuery());
    Map<String, String> planResponse = customAgentService.getPlanforUserQuery(agent);
    log.info("Plan response is {}", planResponse.toString());
    return planResponse;
  }

  @Override
  public AgentHistory getAgentHistory(String agentName) {
    return agentHistoryService.getAgentHistory(agentName);
  }

  /**
   * Creates a trigger in NiFi based on flow configuration.
   *
   * @param flowDto Flow configuration details
   * @return Map containing the result of trigger creation
   */
  @Override
  public Map<String, String> createTriggerInNifi(NifiFlowDto flowDto) {
    return nifiFlowService.createTriggerNifi(flowDto);
  }

  /**
   * Generates a trigger name and description based on user query.
   *
   * @param requestBody Map containing the user query under "userQuery" key
   * @return Map containing the generated trigger name and description
   */
  @Override
  public Map<String, String> createTriggerName(Map<String, String> requestBody) {
    return customAgentService.createTriggerNameDescription(requestBody.get("userQuery"));
  }
}
