/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.rest.KnowledgeBaseRest;
import com.enttribe.promptanalyzer.service.CrawlerService;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * REST controller for managing knowledge base operations. Provides endpoints for creating,
 * updating, searching, and managing knowledge base documents.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/knowledge-base")
@RequiredArgsConstructor
@Slf4j
public class KnowledgeBaseRestImpl implements KnowledgeBaseRest {

  private final KnowledgeBaseService knowledgeBaseService;
  private final CrawlerService crawlerService;

  /**
   * Retrieves knowledge base data by ID.
   *
   * @param id The ID of the knowledge base entry
   * @return KnowledgeBaseResponseDto containing the requested data
   */
  @Override
  public KnowledgeBaseResponseDto getKnowledgeBaseById(Integer id) {
    log.info("Retrieving knowledge base data for nanoId: {}", id);
    return knowledgeBaseService.getKnowledgeBaseById(id);
  }

  @Override
  public KnowledgeBaseResponseDto getKnowledgeBaseByIdV1(String id) {
    log.info("Retrieving knowledge base v1 data for nanoId: {}", id);
    return knowledgeBaseService.getKnowledgeBaseByIdV1(id);
  }

  @Override
  public Map<String, String> processWebSiteKB() {
    log.info("Processing website knowledge base");
    crawlerService.processWebSiteKB();
    return Map.of(PromptConstants.RESULT, "done");
  }

  /**
   * Saves a new document to the knowledge base.
   *
   * @param documentDto The document data and file to save
   * @return Map containing the result of the save operation
   */
  @Override
  public Map<String, String> saveDocument(DocumentRequestDto documentDto) {
    log.info("Inside Saving KnowledgeBase document");
    return knowledgeBaseService.saveDocument(documentDto);
  }

  @Override
  public Map<String, String> saveWebSite(DocumentRequestDto documentDto) {
    log.info("Inside Saving KnowledgeBase document type website");
    return knowledgeBaseService.saveWebSite(documentDto);
  }

  /**
   * Updates an existing document in the knowledge base.
   *
   * @param nanoId The ID of the document to update
   * @param documentDto The updated document data and file
   * @return Map containing the result of the update operation
   * @throws JsonProcessingException if there's an error processing JSON data
   */
  @Override
  public Map<String, String> updateDocument(String nanoId, DocumentRequestDto documentDto)
      throws JsonProcessingException {
    log.info("Updating document with ID: {}", nanoId);
    return knowledgeBaseService.updateDocument(nanoId, documentDto);
  }

  /**
   * Counts knowledge base entries matching the optional filter.
   *
   * @param filter Optional filter criteria
   * @return Total count of matching entries
   */
  @Override
  public Long count(String filter) {
    log.info("Inside Counting knowledge base entries");
    return knowledgeBaseService.count(filter);
  }

  /**
   * Searches knowledge base entries with pagination and sorting.
   *
   * @param filter Optional filter criteria
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching knowledge base entries
   */
  @Override
  public List<KnowledgeBaseResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.info("Searching knowledge base entries");
    return knowledgeBaseService.search(filter, offset, size, orderBy, orderType);
  }

  /**
   * Soft deletes a knowledge base entry by ID.
   *
   * @param id The ID of the entry to delete
   * @return Map containing the result of the delete operation
   */
  @Override
  public Map<String, String> deleteById(String id) {
    log.info("Soft deleting knowledge base entry with ID: {}", id);
    return knowledgeBaseService.softDelete(id);
  }

  /**
   * Retrieves multiple knowledge base entries by their IDs.
   *
   * @param ids List of knowledge base IDs to retrieve
   * @return List of matching knowledge base entries in SDK format
   */
  @Override
  public List<KnowledgeBaseSdkDto> getKnowledgeBaseByIds(List<String> ids) {
    log.info("Retrieving knowledge base entries for IDs: {}", ids);
    return knowledgeBaseService.getKnowledgeBaseByIds(ids);
  }

  /**
   * Updates tags for a knowledge base entry by ID.
   *
   * @param id The ID of the knowledge base entry
   * @param tags The tags to update
   * @return Map containing the result of the update operation
   */
  @Override
  public Map<String, String> updateTagById(String id, Map<String, String> tags) {
    log.info("Updating tags for knowledge base entry with ID: {}", id);
    return knowledgeBaseService.updateTagById(id, tags);
  }

  /**
   * Retrieves the names of tables associated with a given name.
   *
   * @param requestBody The request body containing the name
   * @return List of table names
   */
  @Override
  public List<String> getTablesName(@RequestBody Map<String, String> requestBody) {
    log.debug("Retrieving table names for the provided name");
    String name = requestBody.get("name");
    return knowledgeBaseService.getTablesName(name);
  }

  /**
   * Checks if a website URL exists in the knowledge base.
   *
   * @param requestMap The request map containing the website URL
   * @return Map containing the result of the existence check
   */
  @Override
  public Map<String, Object> existsWebsiteUrl(Map<String, String> requestMap) {
    log.debug("Checking if website URL exists in the knowledge base");
    String webSiteUrl = requestMap.get("webSiteUrl");
    return knowledgeBaseService.existsWebsiteUrl(webSiteUrl);
  }

  /**
   * Saves content to the knowledge base.
   *
   * @param documentDto The document data to save
   * @return Map containing the result of the save operation
   */
  @Override
  public Map<String, String> saveContent(DocumentRequestDto documentDto) {
    log.info("Inside Content Saving KnowledgeBase document");
    return knowledgeBaseService.saveContent(documentDto);
  }

  /**
   * Imports data from a CSV file into the Milvus vector database.
   *
   * @param file The CSV file to import
   * @return A response entity with the result of the import operation
   */
  @Override
  public ResponseEntity<String> milvusImportCsv(MultipartFile file) {
    log.debug("Importing CSV file into Milvus vector database");
    return knowledgeBaseService.milvusImportCsv(file);
  }

  /**
   * Generates an answer to a user question using the specified knowledge base.
   *
   * @param requestMap Map containing the knowledge base name and user question
   * @return Map containing the generated answer
   */
  @Override
  public Map<String, String> getAnswer(Map<String, String> requestMap) {
    log.info("Search inside knowledge base and generating answer for user question");
    String knowledgeBaseName = requestMap.get("knowledgeBaseName");
    String userQuestion = requestMap.get("userQuestion");
    return knowledgeBaseService.getAnswer(knowledgeBaseName, userQuestion);
  }

  /**
   * Exports selected knowledge bases to a downloadable resource.
   *
   * @param ids The list of knowledge base IDs to export
   * @return ResponseEntity containing the exported data as a resource
   */
  @Override
  public ResponseEntity<Resource> exportKnowledgeBases(List<String> ids) {
    log.info("Exporting knowledge bases with IDs: {}", ids);
    return knowledgeBaseService.exportKnowledgeBases(ids);
  }

  /**
   * Imports knowledge bases from an uploaded file.
   *
   * @param file The file containing knowledge base data to import
   * @return A response entity with the result of the import operation
   */
  @Override
  public ResponseEntity<Resource> importKnowledgeBases(MultipartFile file) {
    log.info("Importing knowledge bases from uploaded file");
    return knowledgeBaseService.importKnowledgeBases(file);
  }

  /**
   * Updates the return direct flag for a knowledge base entry.
   *
   * @param requestMap Map containing the ID and value of the return direct flag
   * @return Map containing the result of the update operation
   */
  @Override
  public Map<String, String> updateReturnDirect(Map<String, String> requestMap) {
    log.info("Updating return direct flag for knowledge base entry");
    String id = requestMap.get("id");
    Boolean returnDirectValue = Boolean.parseBoolean(requestMap.get("value"));
    return knowledgeBaseService.updateReturnDirect(id, returnDirectValue);
  }

  @Override
  public Map<String, Boolean> existsKnowledgeBaseName(String name) {
    log.info("Checking if knowledge base name exists: {}", name);
    return knowledgeBaseService.existsKnowledgeBaseName(name);
  }

  @Override
  public byte[] downloadFileFromS3(String fileName) {
    log.info("Inside @class KnowledgeBaseRestImpl @method downloadFileFromS3 : {}", fileName);
    return knowledgeBaseService.downloadFileFromS3(fileName);
  }
}
