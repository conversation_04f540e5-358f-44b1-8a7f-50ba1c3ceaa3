/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.model.PromptAudit;
import java.util.List;
import java.util.Map;

/**
 * Utility class for converting audit-related objects. Provides methods to convert between different
 * audit representations.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class AuditConvertor {

  /** Private constructor to prevent instantiation */
  private AuditConvertor() {
    // Utility class should not be instantiated
  }

  /**
   * Converts a list of PromptAuditDto objects to a new list with all fields copied.
   *
   * @param promptAuditList the list of PromptAuditDto objects to convert
   * @return a new list of PromptAuditDto objects with all fields copied from the source
   */
  public static List<PromptAuditDto> getPromptAuditDtoList(List<PromptAudit> promptAuditList) {
    return promptAuditList.stream().map(AuditConvertor::convertToDto).toList();
  }

  private static PromptAuditDto convertToDto(PromptAudit promptAudit) {
    if (promptAudit == null) {
      return null;
    }
    return PromptAuditDto.builder()
        .id(promptAudit.getAuditId())
        .model(promptAudit.getModel())
        .responseText(promptAudit.getResponseText())
        .totalToken(promptAudit.getTotalToken())
        .promptToken(promptAudit.getPromptToken())
        .responseTime(promptAudit.getResponseTime())
        .generationTokens(promptAudit.getGenerationTokens())
        .applicationName(promptAudit.getApplicationName())
        .category(promptAudit.getCategory())
        .metaTags(
            promptAudit.getMetaTags() == null
                ? null
                : JsonUtils.convertJsonToObject(promptAudit.getMetaTags(), Map.class))
        .chatOptions(
            promptAudit.getChatOptions() == null
                ? null
                : JsonUtils.convertJsonToObject(promptAudit.getChatOptions(), Map.class))
        .promptId(promptAudit.getPromptId())
        .functionArgs(promptAudit.getFunctionArgs())
        .auditId(promptAudit.getAuditId())
        .promptName(promptAudit.getPromptName())
        .httpStatus(promptAudit.getHttpStatus())
        .requestText(promptAudit.getRequestText())
        .status(promptAudit.getStatus())
        .errorMessage(promptAudit.getErrorMessage())
        .startTime(promptAudit.getStartTime())
        .endTime(promptAudit.getEndTime())
        .provider(promptAudit.getProvider())
        .creationTime(promptAudit.getCreationTime())
        .agentName(promptAudit.getAgentName())
        .totalCost(promptAudit.getTotalCost())
        .build();
  }
}
