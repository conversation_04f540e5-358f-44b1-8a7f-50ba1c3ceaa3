/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.dto.assertion.AssertionRequestDto;
import com.enttribe.promptanalyzer.dto.assertion.AssertionResponseDto;
import com.enttribe.promptanalyzer.model.Assertion;
import java.util.Date;

public class AssertionUtil {

  private AssertionUtil() {
    // Private constructor to prevent instantiation
  }

  /**
   * Converts an AssertionRequestDto to an Assertion entity. Only sets fields if the corresponding
   * value in the DTO is not null.
   *
   * @param assertionRequest the assertion request DTO to convert
   * @param assertion the assertion entity to update
   * @return the updated Assertion entity
   */
  public static Assertion convertToAssertion(
      AssertionRequestDto assertionRequest, Assertion assertion) {

    if (assertionRequest.getAssertionType() != null) {
      assertion.setAssertionType(assertionRequest.getAssertionType());
    }
    if (assertionRequest.getMatchStrategy() != null) {
      assertion.setMatchStrategy(assertionRequest.getMatchStrategy());
    }
    if (assertionRequest.getDescription() != null) {
      assertion.setDescription(assertionRequest.getDescription());
    }

    assertion.setModifiedTime(new Date());
    return assertion;
  }

  /**
   * Converts an Assertion entity to an AssertionDto.
   *
   * @param assertion the assertion entity to convert
   * @return the corresponding AssertionDto
   */
  public static AssertionResponseDto convertToAssertionDto(Assertion assertion) {
    AssertionResponseDto assertionDto = new AssertionResponseDto();
    assertionDto.setId(assertion.getNanoId());
    assertionDto.setAssertionType(assertion.getAssertionType());
    assertionDto.setMatchStrategy(assertion.getMatchStrategy());
    assertionDto.setDescription(assertion.getDescription());
    assertionDto.setDeleted(assertion.getDeleted());
    assertionDto.setCreatedTime(assertion.getCreatedTime());
    assertionDto.setModifiedTime(assertion.getModifiedTime());
    return assertionDto;
  }
}
