/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util.test_case;

import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.LlmApiService;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;

public class KnowledgeBaseReturnDirect
    implements Function<KnowledgeBaseReturnDirect.Request, KnowledgeBaseReturnDirect.Response> {

  private static final Logger log = LoggerFactory.getLogger(KnowledgeBaseReturnDirect.class);
  private final VectorStore vectorStore;
  private final LlmApiService llmApiService;
  private final String filterExpression;
  private SearchRequest searchRequest;

  private static final String PROMPT_ID =
      "CONVERSATION_AI-KnowledgeBaseTool-Knowledge_Base_Tool_prompt_v-1";

  public KnowledgeBaseReturnDirect(
      VectorStore vectorStore,
      KnowledgeBase knowledgeBase,
      LlmApiService llmApiService,
      String filterExpression) {
    String metaData = knowledgeBase.getVectorMetaData();
    log.info("initializing KnowledgeBaseReturnDirect tool. metaData : {}", metaData);
    this.vectorStore = vectorStore;
    this.filterExpression = filterExpression;
    this.searchRequest =
        SearchRequest.builder()
            .similarityThreshold(knowledgeBase.getSimilarityThreshold())
            .topK(knowledgeBase.getTopK())
            .build();
    this.llmApiService = llmApiService;
    log.info(
        "KnowledgeBaseReturnDirect tool initialized with filterExpression : {}", filterExpression);
  }

  @Override
  public Response apply(Request request) {
    log.info("inside function KnowledgeBase. request : {}", request.question());
    if (filterExpression != null) {
      searchRequest =
          SearchRequest.from(searchRequest)
              .query(request.question())
              .filterExpression(filterExpression)
              .build();
    }
    List<Document> documents = vectorStore.similaritySearch(searchRequest);
    String context = prepareContext(documents);

    BeanOutputConverter<Response> converter = new BeanOutputConverter<>(Response.class);

    String response =
        llmApiService.executePromptV1(
            PROMPT_ID,
            Map.of(
                "context",
                context,
                "userMessage",
                request.question(),
                "format",
                converter.getFormat()));
    return converter.convert(response);
  }

  public record Request(@JsonProperty("question") String question) {}

  public record Response(
      @JsonPropertyDescription("answer of the question") String content,
      @JsonPropertyDescription("metadata of the document used for answering")
          Map<String, Object> metadata,
      @JsonPropertyDescription("exact line the answer is taken from") String contextLine) {}

  private String prepareContext(List<Document> documents) {
    StringBuilder context = new StringBuilder();
    for (Document document : documents) {
      context.append("content : ").append(document.getText()).append("\n");
      context
          .append("metadata : ")
          .append(JsonUtils.convertToJSON(document.getMetadata()))
          .append("\n-------------");
    }
    return context.toString();
  }
}
