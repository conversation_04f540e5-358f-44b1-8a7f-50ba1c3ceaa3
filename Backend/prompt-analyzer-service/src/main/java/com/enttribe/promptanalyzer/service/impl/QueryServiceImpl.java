/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.Query;
import com.enttribe.promptanalyzer.service.QueryService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.QueryConverter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Implementation of the {@link QueryService} interface. This class provides the actual business
 * logic for managing Query. for a specific application. It interacts with the data access layer to
 * fetch and modify Query data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QueryServiceImpl implements QueryService {

  private final CustomFilter customFilter;

  @Override
  public List<QueryResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    try {
      log.debug(
          "Searching queries with filter: {}, offset: {}, size: {}, orderBy: {}, orderType: {}",
          filter,
          offset,
          size,
          orderBy,
          orderType);
      List<Query> queries =
          customFilter.searchByFilter(Query.class, filter, orderBy, orderType, offset, size);
      log.debug("Found {} queries matching search criteria", queries.size());
      List<QueryResponseDto> responseDtos = QueryConverter.getQueryDtoList(queries);
      log.info("Successfully converted {} queries to DTOs", responseDtos.size());
      return responseDtos;
    } catch (Exception e) {
      log.error("Error while searching for queries with filter {}: {}", filter, e.getMessage(), e);
      throw new BusinessException("Failed to search queries");
    }
  }

  @Override
  public Long count(String filter) {
    log.debug("Inside @method count queries");
    try {
      Long count = customFilter.countByFilter(Query.class, filter);
      log.debug("Found {} queries matching", count);
      return count;
    } catch (Exception e) {
      log.error("Error while counting queries with filter {}: {}", filter, e.getMessage(), e);
      throw new BusinessException("Failed to count queries");
    }
  }
}
