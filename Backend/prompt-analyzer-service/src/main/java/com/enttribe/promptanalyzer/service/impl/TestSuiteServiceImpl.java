/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.AgentTestCaseDao;
import com.enttribe.promptanalyzer.dao.TestSuiteDao;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.assertion.AssertionRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteRequestDto;
import com.enttribe.promptanalyzer.dto.testsuite.TestSuiteResponseDto;
import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.AgentTestCase;
import com.enttribe.promptanalyzer.model.AgentTestCaseAssertion;
import com.enttribe.promptanalyzer.model.Assertion;
import com.enttribe.promptanalyzer.model.TestSuite;
import com.enttribe.promptanalyzer.service.AgentTestCaseService;
import com.enttribe.promptanalyzer.service.TestSuiteService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.TestSuiteUtils;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Valid;
import jakarta.validation.Validator;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
public class TestSuiteServiceImpl implements TestSuiteService {

  public static final int MAX_RECORDS_TO_IMPORT = 1000;
  private final TestSuiteDao testSuiteDao;
  private final CustomFilter customFilter;
  private final AgentTestCaseDao agentTestCaseDao;
  private final AgentTestCaseService agentTestCaseService;
  private final Validator validator;
  private final CustomerInfo customerInfo;

  // CSV columns for import/export
  private static final List<String> testCaseCsvColumns =
      List.of(
          "Name",
          "Advisors",
          "UserQuestion",
          "Description",
          "History",
          "AssertionType",
          "ToolName",
          "JsonPointer",
          "MatchStrategy",
          "ExpectedValue",
          "VariableMap");

  @Override
  public Map<String, String> create(TestSuiteRequestDto requestDto) {
    log.info("Creating TestSuite");
    try {
      TestSuite testSuite = new TestSuite();
      TestSuiteUtils.mapToEntity(requestDto, testSuite);

      if (requestDto.getAgentTestCase() != null) {
        createAgentTestCases(requestDto.getAgentTestCase(), testSuite, false);
      }

      testSuite.setNanoId(NanoIdUtils.randomNanoId());
      testSuite.setCreatedTime(new Date());
      testSuite.setModifiedTime(new Date());
      testSuite.setDeleted(false);

      testSuiteDao.save(testSuite);
      log.info("TestSuite created with nanoId: {}", testSuite.getNanoId());

      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error creating TestSuite: {}", e.getMessage(), e);
      throw new BusinessException("Unable to create TestSuite");
    }
  }

  @Override
  public Map<String, String> update(TestSuiteRequestDto requestDto) {
    log.info("Updating TestSuite with nanoId: {}", requestDto.getId());
    TestSuite testSuite =
        testSuiteDao
            .findByNanoId(requestDto.getId())
            .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TESTSUITE_NOT_FOUND));
    try {
      TestSuiteUtils.mapToEntity(requestDto, testSuite);

      if (requestDto.getAgentTestCase() != null) {
        createAgentTestCases(requestDto.getAgentTestCase(), testSuite, false);
      }

      testSuite.setModifiedTime(new Date());
      testSuiteDao.save(testSuite);
      log.info("TestSuite updated with nanoId: {}", testSuite.getNanoId());
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (Exception e) {
      log.error("Error updating TestSuite: {}", e.getMessage(), e);
      throw new BusinessException("Unable to update TestSuite");
    }
  }

  @Override
  public List<TestSuiteResponseDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Searching TestSuites");
    try {
      List<TestSuite> results =
          customFilter.searchByFilter(TestSuite.class, filter, orderBy, orderType, offset, size);
      return TestSuiteUtils.getTestSuiteDtoList(results);
    } catch (Exception e) {
      log.error("Error searching TestSuites: {}", e.getMessage(), e);
      throw new BusinessException("Failed to search TestSuites");
    }
  }

  @Override
  public Long count(String filter) {
    try {
      log.info("Counting TestSuites");
      return customFilter.countByFilter(TestSuite.class, filter);
    } catch (Exception e) {
      log.error("Error counting TestSuites: {}", e.getMessage(), e);
      throw new BusinessException("Failed to count TestSuites");
    }
  }

  @Override
  public Map<String, String> deleteById(String nanoId) {
    log.info("Deleting TestSuite by nanoId: {}", nanoId);
    TestSuite testSuite =
        testSuiteDao
            .findByNanoId(nanoId)
            .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TESTSUITE_NOT_FOUND));
    try {
      // Enforce creator-only soft delete
      customFilter.verifyCreator(customerInfo.getUserId(), testSuite.getCreator());
      testSuite.setDeleted(true);
      testSuite.setModifiedTime(new Date());
      testSuiteDao.save(testSuite);
      log.info("TestSuite marked as deleted: {}", nanoId);
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (Exception e) {
      log.error("Error deleting TestSuite: {}", e.getMessage(), e);
      throw new BusinessException("Unable to delete TestSuite");
    }
  }

  @Override
  public Map<String, String> addAgentTestCasesToSuite(TestSuiteRequestDto requestDto) {
    log.info("Adding AgentTestCases to TestSuite: {}", requestDto.getId());

    TestSuite testSuite =
        testSuiteDao
            .findByNanoId(requestDto.getId())
            .orElseThrow(() -> new ResourceNotFoundException("TestSuite not found"));

    if (requestDto.getAgentTestCase() != null) {
      createAgentTestCases(requestDto.getAgentTestCase(), testSuite, requestDto.isForImport());
    }

    testSuiteDao.save(testSuite);
    log.info("Successfully added AgentTestCases to TestSuite ID: {}", testSuite.getNanoId());

    return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
  }

  private void createAgentTestCases(
      List<AgentTestCaseRequestDto> testCaseDtos, TestSuite testSuite, boolean forImport) {
    log.debug("Creating AgentTestCases for TestSuite: {}", testSuite.getNanoId());
    for (AgentTestCaseRequestDto testCaseDto : testCaseDtos) {
      AgentTestCase agentTestCase = createOrUpdateAgentTestCase(testCaseDto, forImport);

      if (testSuite.getAgentTestCases() == null) {
        testSuite.setAgentTestCases(new HashSet<>());
      }

      testSuite.getAgentTestCases().add(agentTestCase);
      agentTestCase.getTestSuites().add(testSuite);
    }
  }

  private AgentTestCase createOrUpdateAgentTestCase(
      @Valid AgentTestCaseRequestDto dto, boolean forImport) {
    Map<String, String> result;
    if (dto.getName() != null && agentTestCaseDao.findByName(dto.getName()).isPresent()) {
      // Update case
      log.debug("updating AgentTestCase with name: {}", dto.getName());
      result = agentTestCaseService.update(dto, forImport);
    } else {
      // Create new
      log.debug("creating AgentTestCase with name: {}", dto.getName());
      result = agentTestCaseService.create(dto);
    }

    String nanoId = result.get("id");
    if (nanoId == null) {
      throw new BusinessException("Failed to create or update AgentTestCase: " + dto.getName());
    }
    log.info("AgentTestCase processed with nanoId: {}", nanoId);
    return agentTestCaseDao
        .findByNanoId(nanoId)
        .orElseThrow(
            () -> new ResourceNotFoundException("AgentTestCase not found with ID: " + nanoId));
  }

  private List<AgentTestCase> getAgentTestCases(String id) {
    log.debug("Fetching AgentTestCases for TestSuite nanoId: {}", id);
    TestSuite testSuite =
        testSuiteDao
            .findByNanoId(id)
            .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TESTSUITE_NOT_FOUND));

    log.debug("TestSuite found with nanoId: {}", testSuite.getNanoId());

    List<String> agentTestCaseNanoIds =
        testSuite.getAgentTestCases().stream().map(AgentTestCase::getNanoId).toList();

    log.debug("AgentTestCase nanoIds found: {}", agentTestCaseNanoIds);

    List<AgentTestCase> agentTestCases = agentTestCaseDao.findByNanoIdIn(agentTestCaseNanoIds);
    log.debug("Successfully fetched AgentTestCases for TestSuite id: {}", id);
    return agentTestCases;
  }

  @Override
  public ResponseEntity<Resource> exportTestCasesBySuiteId(String suiteId) {
    List<AgentTestCase> testCases = getAgentTestCases(suiteId);
    List<TestCaseCsvRow> rows = new ArrayList<>();
    for (AgentTestCase testCase : testCases) {
      Set<AgentTestCaseAssertion> assertions = testCase.getAgentTestCaseAssertions();
      if (assertions == null || assertions.isEmpty()) {
        // Export row with empty assertion fields
        rows.add(TestCaseCsvRow.from(testCase, null, null));
      } else {
        for (AgentTestCaseAssertion assertion : assertions) {
          Assertion assertionEntity = assertion.getAssertion();
          rows.add(TestCaseCsvRow.from(testCase, assertion, assertionEntity));
        }
      }
    }
    // Use CSVUtils to export
    List<Function<TestCaseCsvRow, Object>> extractors =
        List.of(
            TestCaseCsvRow::getName,
            TestCaseCsvRow::getAdvisors,
            TestCaseCsvRow::getUserQuestion,
            TestCaseCsvRow::getDescription,
            TestCaseCsvRow::getHistory,
            TestCaseCsvRow::getAssertionType,
            TestCaseCsvRow::getToolName,
            TestCaseCsvRow::getJsonPointer,
            TestCaseCsvRow::getMatchStrategy,
            TestCaseCsvRow::getExpectedValue,
            TestCaseCsvRow::getVariableMap);
    return CSVUtils.exportCSV(rows, testCaseCsvColumns, "testcases", extractors);
  }

  /**
   * Import test cases from CSV and add them to a suite using addAgentTestCasesToSuite. Only sets a
   * dummy id in TestSuiteRequestDto as per requirements.
   */
  @Override
  public ResponseEntity<Resource> importTestCasesToSuite(String suiteId, MultipartFile file)
      throws IOException {
    Function<CSVRecord, TestSuiteRequestDto> recordTransformer =
        csvRecord -> {
          TestSuite testSuite =
              testSuiteDao
                  .findByNanoId(suiteId)
                  .orElseThrow(
                      () -> new ResourceNotFoundException(PromptConstants.TESTSUITE_NOT_FOUND));
          // Build AssertionRequestDto
          AssertionRequestDto assertionDto = new AssertionRequestDto();
          assertionDto.setExpectedValue(csvRecord.get("ExpectedValue"));
          assertionDto.setJsonPointer(csvRecord.get("JsonPointer"));
          assertionDto.setToolName(csvRecord.get("ToolName"));
          String assertionTypeStr = csvRecord.get("AssertionType");
          String matchStrategyStr = csvRecord.get("MatchStrategy");
          String variableMap = csvRecord.get("VariableMap");
          if (assertionTypeStr != null && !assertionTypeStr.isBlank()) {
            assertionDto.setAssertionType(
                AssertionType.valueOf(assertionTypeStr.trim().toUpperCase()));
          }
          if (matchStrategyStr != null && !matchStrategyStr.isBlank()) {
            assertionDto.setMatchStrategy(
                MatchStrategy.valueOf(matchStrategyStr.trim().toUpperCase()));
          }
          // Build AgentTestCaseRequestDto
          AgentTestCaseRequestDto agentTestCaseDto =
              AgentTestCaseRequestDto.builder()
                  .name(csvRecord.get("Name"))
                  .description(csvRecord.get("Description"))
                  .history(csvRecord.get("History"))
                  .advisors(csvRecord.get("Advisors"))
                  .userQuestion(csvRecord.get("UserQuestion"))
                  .agentId(testSuite.getAgentId())
                  .agentName(testSuite.getAgentName())
                  .agentType(testSuite.getAgentType())
                  .assertions(List.of(assertionDto))
                  .variableMap(JsonUtils.convertJsonToObject(variableMap, Map.class))
                  .build();

          TestSuiteRequestDto requestDto =
              TestSuiteRequestDto.builder()
                  .id(suiteId)
                  .agentTestCase(List.of(agentTestCaseDto))
                  .forImport(true)
                  .build();

          processTestCase(requestDto);
          return requestDto;
        };
    // Use CSVUtils to import and call addAgentTestCasesToSuite for each row
    return CSVUtils.importCSV(
        file,
        testCaseCsvColumns,
        recordTransformer,
        this::addAgentTestCasesToSuite,
        "testcase_import_result",
        MAX_RECORDS_TO_IMPORT);
  }

  public void processTestCase(TestSuiteRequestDto dto) {
    Set<ConstraintViolation<TestSuiteRequestDto>> violations = validator.validate(dto);

    if (!violations.isEmpty()) {
      String errors =
          violations.stream()
              .map(ConstraintViolation::getMessage)
              .collect(Collectors.joining(", "));
      throw new IllegalArgumentException(errors);
    }
  }

  /** Helper class for CSV row mapping for test case import/export. */
  @Getter
  public static class TestCaseCsvRow {
    private String name;
    private String description;
    private String history;
    private String advisors;
    private String expectedValue;
    private String jsonPointer;
    private String toolName;
    private String assertionType;
    private String matchStrategy;
    private String userQuestion;
    private String variableMap;

    public static TestCaseCsvRow from(
        AgentTestCase testCase, AgentTestCaseAssertion assertion, Assertion assertionEntity) {
      TestCaseCsvRow row = new TestCaseCsvRow();
      row.name = testCase.getName();
      row.description = testCase.getDescription();
      row.history = testCase.getHistory();
      row.advisors = testCase.getAdvisors();
      row.userQuestion = testCase.getUserQuestion();
      row.variableMap =
          testCase.getVariableMap() != null
              ? JsonUtils.convertToJSON(testCase.getVariableMap())
              : "{}";
      if (assertion != null) {
        row.expectedValue = assertion.getExpectedValue();
        row.jsonPointer = assertion.getParameterName();
        row.toolName = assertion.getToolName();
      }
      if (assertionEntity != null) {
        row.assertionType =
            assertionEntity.getAssertionType() != null
                ? assertionEntity.getAssertionType().name()
                : null;
        row.matchStrategy =
            assertionEntity.getMatchStrategy() != null
                ? assertionEntity.getMatchStrategy().name()
                : null;
      }
      return row;
    }

    private static String extractJsonKey(String expectedValue, String key) {
      try {
        JsonNode node = JsonUtils.toJsonNode(expectedValue);
        if (node.has(key)) {
          return node.get(key).asText();
        }
      } catch (Exception e) {
        log.error("error in extraction : {}", e.getMessage());
      }
      return null;
    }
  }
}
