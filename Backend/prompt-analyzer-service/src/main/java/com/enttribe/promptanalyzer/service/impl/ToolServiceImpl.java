/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.config.McpToolService;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dao.ToolDao;
import com.enttribe.promptanalyzer.dto.InputTypeSchemaWrapper;
import com.enttribe.promptanalyzer.dto.microintent.MicroIntentResponseDto;
import com.enttribe.promptanalyzer.dto.tool.ApiToolDto;
import com.enttribe.promptanalyzer.dto.tool.GenerateToolDto;
import com.enttribe.promptanalyzer.dto.tool.SwaggerDto;
import com.enttribe.promptanalyzer.dto.tool.ToolAuthDto;
import com.enttribe.promptanalyzer.dto.tool.ToolConvertorDto;
import com.enttribe.promptanalyzer.dto.tool.ToolCreateDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.dto.tool.ToolDtoSdk;
import com.enttribe.promptanalyzer.dto.tool.ToolVersionDetailsDto;
import com.enttribe.promptanalyzer.dto.tool.ToolWorkflowDto;
import com.enttribe.promptanalyzer.exception.AccessDeniedException;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.Tool;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.service.ToolService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.tools.Diagnostic;
import javax.tools.DiagnosticCollector;
import javax.tools.JavaCompiler;
import javax.tools.JavaFileObject;
import javax.tools.ToolProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

/**
 * Implementation of the {@link ToolService} interface. This class provides the actual business
 * logic for managing Tool for a specific application. It interacts with the data access layer to
 * fetch and modify Tool data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ToolServiceImpl implements ToolService {

  public static final String X_101_TYPE = "x101";
  private final ToolDao toolDao;
  private final CustomFilter customFilter;
  private final PromptDao promptDao;
  private final McpToolService mcpToolService;
  private final CustomerInfo customerInfo;
  private final ApiService apiService;

  private static final int MAX_IMPORT_RECORDS = 50;

  @Value("classpath:template/api_tool.st")
  private Resource apiTool;

  @Value("classpath:template/connector_tool.st")
  private Resource connectorTool;

  @Value("classpath:template/x101_tool.st")
  private Resource x101Tool;

  private static final List<String> csvColumnHeader =
      List.of(
          "Application Name",
          "Tool Name",
          "Display Name",
          "Category",
          "Version",
          "Type",
          "Tool ID",
          "Description",
          "Class Name",
          "Request Type",
          "Tags",
          "Byte Code Map",
          "Source Code",
          "Tool JSON",
          "Tool Image",
          PromptConstants.PROMPT_ID,
          "Api Tool Info",
          PromptConstants.AGENT_ID,
          PromptConstants.RETURN_DIRECT);

  @Override
  public Map<String, String> createTool(ToolDto toolDto) {
    log.debug("Creating new tool with name: {}", toolDto.getName());
    Map<String, String> result = new HashMap<>();
    try {
      List<ToolVersionDetailsDto> versionsOfTool =
          getVersionsOfTool(
              toolDto.getApplicationName(),
              toolDto.getName(),
              toolDto.getCategory(),
              toolDto.getStatus());
      List<String> versionList =
          versionsOfTool.stream().map(ToolVersionDetailsDto::getVersion).toList();
      String nextVersion = getNextVersion(versionList, toolDto.getStatus());
      log.debug("Next version for tool: {}", nextVersion);
      toolDto.setVersion(nextVersion);
      Tool tool = getToolEntity(toolDto);
      tool.setToolName(toolDto.getName());
      tool.setNanoId(NanoIdUtils.randomNanoId());
      String toolId =
          newToolId(
              toolDto.getApplicationName(), toolDto.getName(), toolDto.getCategory(), nextVersion);
      tool.setToolId(toolId);
      tool.setAgentId(toolDto.getAgentId());

      switch (toolDto.getType()) {
        case "API" -> {
          ApiToolDto apiToolDto = toolDto.getApiTool();
          log.debug(
              "Processing API tool with method: {}, url: {}",
              apiToolDto.getMethod(),
              apiToolDto.getUrl());
          Map<String, Object> variableMap =
              Map.of(
                  PromptConstants.DESCRIPTION,
                  toolDto.getDescription(),
                  "method",
                  apiToolDto.getMethod(),
                  "url",
                  apiToolDto.getUrl(),
                  "requestBody",
                  apiToolDto.getRequestBody().replace("\"", "\\\""),
                  "headers",
                  apiToolDto.getHeaders().replace("\"", "\\\""));

          String sourceCode = TemplateUtils.getResolvedPrompt(apiTool, variableMap);
          Map<String, byte[]> byteCode =
              compileSourceCode(
                  sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
          tool.setByteCodeMap(byteCode);
          tool.setSourceCode(AESUtils.encryptString(sourceCode));
          tool.setClassName(PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
          tool.setType("API");
          tool.setApiToolInfo(JsonUtils.convertToJSON(apiToolDto));
        }
        case PromptConstants.CONNECTOR -> {
          String operationSpecification = toolDto.getOperationConfig();
          String requestFields = extractVariableKeys(operationSpecification);
          log.debug("Processing connector tool with request fields: {}", requestFields);
          Map<String, Object> variableMap =
              Map.of(
                  PromptConstants.DESCRIPTION,
                  toolDto.getDescription(),
                  "requestFields",
                  requestFields,
                  "name",
                  toolDto.getConnectorName());

          String sourceCode = TemplateUtils.getResolvedPrompt(connectorTool, variableMap);
          Map<String, byte[]> byteCode =
              compileSourceCode(
                  sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
          tool.setByteCodeMap(byteCode);
          tool.setSourceCode(AESUtils.encryptString(sourceCode));
          tool.setClassName(PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
          tool.setType(PromptConstants.CONNECTOR);
        }
        case X_101_TYPE -> {
          log.debug(
              "Processing X101 tool with entity: {}, intent: {}, name: {}",
              toolDto.getEntityName(),
              toolDto.getIntentName(),
              toolDto.getName());

          Map<String, Object> variableMap =
              Map.of(
                  "toolName", toolDto.getName(),
                  "entityName", toolDto.getEntityName(),
                  "intentName", toolDto.getIntentName(),
                  "description", toolDto.getDescription());

          String sourceCode = TemplateUtils.getResolvedPrompt(x101Tool, variableMap);
          String className = "com.enttribe.promptanalyzer.function." + toolDto.getName();
          Map<String, byte[]> byteCode = compileSourceCode(sourceCode, className);
          tool.setByteCodeMap(byteCode);
          tool.setSourceCode(AESUtils.encryptString(sourceCode));
          tool.setClassName(className);
          tool.setType("X101");
          tool.setEntityName(toolDto.getEntityName());
          tool.setIntentName(toolDto.getIntentName());
        }
        default -> {
          log.debug("Processing custom tool with class name: {}", toolDto.getClassName());
          String decryptedSourceCode = AESUtils.decryptString(toolDto.getSourceCode());
          Map<String, byte[]> byteCode =
              compileSourceCode(decryptedSourceCode, toolDto.getClassName());
          tool.setByteCodeMap(byteCode);
          tool.setSourceCode(toolDto.getSourceCode());
        }
      }
      tool.setTags(toolDto.getTags());
      tool.setToolImage(toolDto.getToolImage());
      tool.setReturnDirect(toolDto.getReturnDirect());
      tool.setDeleted(false);
      log.debug("Saving tool with ID: {}", toolId);
      Tool savedTool = toolDao.save(tool);
      result.put("toolId", savedTool.getNanoId());
      result.put("toolName", savedTool.getToolName());
      result.put("displayName", savedTool.getDisplayName());
      result.put("description", savedTool.getDescription());
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      log.info("Successfully created tool with ID: {}", savedTool.getId());
      return result;
    } catch (Exception e) {
      log.error("Error while creating tool: {}", e.getMessage(), e);
      ExceptionUtil.handleCustomException(e);
      throw new BusinessException("Unable to create tool");
    }
  }

  private static String extractVariableKeys(String jsonString) {
    try {
      String unescapedJson = jsonString.replace("\\\"", "\"");

      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode rootNode = objectMapper.readTree(unescapedJson);

      StringBuilder result = new StringBuilder();
      Iterator<String> fieldNames = rootNode.fieldNames();

      while (fieldNames.hasNext()) {
        String key = fieldNames.next();
        String value = rootNode.get(key).asText();

        // Check if value starts with '${'
        if (value.startsWith("${")) {
          if (result.length() > 0) {
            result.append(", ");
          }
          result.append("String " + key);
        }
      }

      return result.toString();
    } catch (Exception e) {
      throw new BusinessException("failed to extract operationConfig from connector", e);
    }
  }

  @Override
  public Map<String, String> updateTool(ToolDto toolDto) {
    log.debug("Updating tool with ID: {}", toolDto.getId());
    Map<String, String> result = new HashMap<>();

    try {
      Tool existingToolOpt =
          toolDao
              .findByNanoId(toolDto.getId())
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TOOL_NOT_FOUND));
      Tool existingTool = mapDtoToTool(toolDto, existingToolOpt);

      if (toolDto.getType().equals("API")) {
        ApiToolDto apiToolDto = toolDto.getApiTool();
        log.debug(
            "Updating API tool with method: {}, url: {}",
            apiToolDto.getMethod(),
            apiToolDto.getUrl());
        Map<String, Object> variableMap =
            Map.of(
                PromptConstants.DESCRIPTION,
                toolDto.getDescription(),
                "method",
                apiToolDto.getMethod(),
                "url",
                apiToolDto.getUrl(),
                "requestBody",
                apiToolDto.getRequestBody().replace("\"", "\\\""),
                "headers",
                apiToolDto.getHeaders().replace("\"", "\\\""));

        String sourceCode = TemplateUtils.getResolvedPrompt(apiTool, variableMap);
        Map<String, byte[]> byteCode =
            compileSourceCode(
                sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
        existingTool.setByteCodeMap(byteCode);
        existingTool.setSourceCode(AESUtils.encryptString(sourceCode));
        existingTool.setClassName(PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_APITOOL);
        existingTool.setType("API");
        existingTool.setApiToolInfo(JsonUtils.convertToJSON(apiToolDto));
      } else if (toolDto.getType().equals(PromptConstants.CONNECTOR)) {
        String operationSpecification = toolDto.getOperationConfig();
        String requestFields = extractVariableKeys(operationSpecification);
        log.debug("Updating connector tool with request fields: {}", requestFields);
        Map<String, Object> variableMap =
            Map.of(
                PromptConstants.DESCRIPTION,
                toolDto.getDescription(),
                "requestFields",
                requestFields,
                "name",
                toolDto.getConnectorName());

        String sourceCode = TemplateUtils.getResolvedPrompt(connectorTool, variableMap);
        Map<String, byte[]> byteCode =
            compileSourceCode(
                sourceCode, PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
        existingTool.setByteCodeMap(byteCode);
        existingTool.setSourceCode(AESUtils.encryptString(sourceCode));
        existingTool.setClassName(
            PromptConstants.COM_ENTTRIBE_PROMPTANALYZER_FUNCTION_CONNECTORTOOL);
        existingTool.setType(PromptConstants.CONNECTOR);
      } else {
        log.debug("Updating custom tool with class name: {}", toolDto.getClassName());
        String decryptedSourceCode = AESUtils.decryptString(toolDto.getSourceCode());
        Map<String, byte[]> byteCode =
            compileSourceCode(decryptedSourceCode, toolDto.getClassName());
        existingTool.setByteCodeMap(byteCode);
        existingTool.setSourceCode(toolDto.getSourceCode());
      }

      extractedUpdateTool(toolDto, existingTool);

      ToolAuthDto toolAuthentication = toolDto.getToolAuthentication();
      if (toolAuthentication != null) {
        existingTool.setAuthType(toolAuthentication.getAuthType());
        existingTool.setAuthKey(toolAuthentication.getKey());
        existingTool.setAuthValue(toolAuthentication.getValue());
      }

      List<ToolVersionDetailsDto> versionsOfTool =
          getVersionsOfTool(
              toolDto.getApplicationName(),
              toolDto.getName(),
              toolDto.getCategory(),
              toolDto.getStatus());
      List<String> versionList =
          versionsOfTool.stream().map(ToolVersionDetailsDto::getVersion).toList();
      String nextVersion = getNextVersion(versionList, toolDto.getStatus());
      log.debug("Next version for tool update: {}", nextVersion);
      toolDto.setVersion(nextVersion);
      if (toolDto.getToolId() != null) {
        existingTool.setToolId(
            newToolId(
                toolDto.getApplicationName(),
                toolDto.getName(),
                toolDto.getCategory(),
                toolDto.getVersion()));
      }

      existingTool.setToolImage(toolDto.getToolImage());
      existingTool.setModifiedTime(new Date());
      log.debug("Saving updated tool with ID: {}", existingTool.getId());
      toolDao.save(existingTool);
      log.info("Successfully updated tool with ID: {}", existingTool.getId());

      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      return result;
    } catch (ResourceNotFoundException e) {
      log.warn("tool not found with id : {}", toolDto.getToolId());
      throw e;
    } catch (Exception e) {
      log.error("Error while updating tool: {}", e.getMessage(), e);
      ExceptionUtil.handleCustomException(e);
      throw new BusinessException("Unable to update tool");
    }
  }

  private static void extractedUpdateTool(ToolDto toolDto, Tool existingTool) {
    log.debug("Inside @method extractedUpdateTool");
    if (toolDto.getLanguage() != null) {
      existingTool.setLanguage(toolDto.getLanguage());
    }
    if (toolDto.getType() != null) {
      existingTool.setType(toolDto.getType());
    }
    if (toolDto.getRequestType() != null) {
      existingTool.setRequestType(toolDto.getRequestType());
    }
    if (toolDto.getStatus() != null) {
      existingTool.setStatus(toolDto.getStatus());
    }
    if (toolDto.getAgentId() != null) {
      existingTool.setAgentId(toolDto.getAgentId());
    }

    if (toolDto.getTags() != null) {
      existingTool.setTags(toolDto.getTags());
    }
    if (toolDto.getHttpMethod() != null) {
      existingTool.setHttpMethod(toolDto.getHttpMethod());
    }

    if (toolDto.getParameters() != null) {
      existingTool.setParameters(toolDto.getParameters());
    }
    if (toolDto.getToolJson() != null) {
      existingTool.setToolJson(toolDto.getToolJson());
    }
    if (toolDto.getUrl() != null) {
      existingTool.setUrl(toolDto.getUrl());
    }
    if (toolDto.getReturnDirect() != null) {
      existingTool.setReturnDirect(toolDto.getReturnDirect());
    }
    log.debug("Successfully updated existing tool");
  }

  private static Tool mapDtoToTool(ToolDto toolDto, Tool existingTool) {
    log.debug("Inside @method getExistingTool");
    // Update fields from the DTO
    if (toolDto.getName() != null) {
      existingTool.setToolName(toolDto.getName());
    }
    if (toolDto.getClassName() != null) {
      existingTool.setClassName(toolDto.getClassName());
    }
    if (toolDto.getDisplayName() != null) {
      existingTool.setDisplayName(toolDto.getDisplayName());
    }
    if (toolDto.getApplicationName() != null) {
      existingTool.setApplicationName(toolDto.getApplicationName());
    }
    if (toolDto.getCategory() != null) {
      existingTool.setCategory(toolDto.getCategory());
    }
    if (toolDto.getDescription() != null) {
      existingTool.setDescription(toolDto.getDescription());
    }

    if (toolDto.getType() == null) {
      throw new BusinessException("type is not provided");
    }
    log.debug("Successfully get existing tool");
    return existingTool;
  }

  @Override
  public Map<String, Integer> generateTools(SwaggerDto swaggerDto) {
    Map<String, Integer> result = new HashMap<>();
    int success = 0;
    int failed = 0;
    String hostname = swaggerDto.getHostname();
    String toolImage = swaggerDto.getToolImage();
    ToolAuthDto toolAuthentication = swaggerDto.getToolAuthentication();
    log.info(
        "Inside @method generateTools. @params : hostName {} authorizationType {}",
        hostname,
        toolAuthentication.getAuthorizationType());

    try {
      JSONObject swagger = new JSONObject(swaggerDto.getSwaggerJson());

      Map<String, JSONObject> definitionsMap = new HashMap<>();
      JSONObject definitions = swagger.optJSONObject("definitions");
      if (definitions != null) {
        for (String key : definitions.keySet()) {
          definitionsMap.put(key, definitions.getJSONObject(key));
        }
      }

      // Parse paths and generate classes
      JSONObject paths = swagger.getJSONObject("paths");
      for (String endpoint : paths.keySet()) {
        JSONObject methods = paths.getJSONObject(endpoint);
        for (String method : methods.keySet()) {
          boolean toolCreated =
              createTool(
                  new ToolCreateDto(
                      endpoint,
                      method,
                      methods.getJSONObject(method),
                      hostname,
                      toolAuthentication,
                      definitionsMap,
                      swaggerDto,
                      toolImage));

          if (toolCreated) {
            success++;
          } else {
            failed++;
          }
        }
      }
    } catch (Exception e) {
      log.error(
          "Error while generating tools from swagger : exception message : {}", e.getMessage(), e);
      throw new BusinessException("unable to create tool from swagger json");
    }

    result.put(PromptConstants.SUCCESS, success);
    result.put(PromptConstants.FAILED, failed);
    return result;
  }

  private boolean createTool(ToolCreateDto request) {
    try {
      Tool tool =
          generateApiClass(
              new GenerateToolDto(
                  request.getEndpoint(),
                  request.getMethod(),
                  request.getMethodObj(),
                  request.getHostname(),
                  request.getToolAuthentication().getAuthType(),
                  request.getToolAuthentication().getKey(),
                  request.getToolAuthentication().getValue(),
                  request.getDefinitionsMap(),
                  request.getSwaggerDto().getApplicationName(),
                  request.getSwaggerDto().getCategory()));

      SwaggerDto swaggerDto = request.getSwaggerDto();

      tool.setStatus(swaggerDto.getStatus());
      tool.setTags(swaggerDto.getTags());
      tool.setCreatedTime(new Date());
      tool.setModifiedTime(new Date());

      List<ToolVersionDetailsDto> versionsOfTool =
          getVersionsOfTool(
              swaggerDto.getApplicationName(),
              tool.getToolName(),
              swaggerDto.getCategory(),
              swaggerDto.getStatus());

      List<String> versionList =
          versionsOfTool.stream().map(ToolVersionDetailsDto::getVersion).toList();
      String nextVersion = getNextVersion(versionList, swaggerDto.getStatus());
      log.debug("generateTools with next version : {}", nextVersion);
      tool.setVersion(nextVersion);

      String toolId =
          newToolId(
              swaggerDto.getApplicationName(),
              tool.getToolName(),
              swaggerDto.getCategory(),
              nextVersion);

      tool.setToolId(toolId);
      tool.setToolJson(request.getMethodObj().toString());
      tool.setToolImage(request.getToolImage());
      tool.setReturnDirect(swaggerDto.getReturnDirect());
      tool.setNanoId(NanoIdUtils.randomNanoId());

      toolDao.save(tool);
      return true;
    } catch (Exception e) {
      log.error("error while creating tool : {}", e.getMessage(), e);
      return false;
    }
  }

  @Override
  public Map<String, String> changeToolStatus(String id, String status) {
    log.debug("Changing status for tool with ID: {} to: {}", id, status);

    try {
      Tool tool =
          toolDao
              .findByNanoId(id)
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TOOL_NOT_FOUND));
      tool.setStatus(status);
      toolDao.save(tool);
      log.info("Successfully changed status for tool with ID: {} to: {}", id, status);
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (ResourceNotFoundException e) {
      log.warn("Error @method changeToolStatus tool not found with id: {}", id);
      throw e;
    } catch (Exception e) {
      log.error("Error while changing tool status: {}", e.getMessage(), e);
      throw new BusinessException("Unable to change tool status");
    }
  }

  @Override
  public void generateToolsFromWorkflow(ToolWorkflowDto createToolWorkflow) {
    log.debug("Generating tools from workflow with name: {}", createToolWorkflow.getName());
    try {
      Tool tool = generateToolsFromWorkflowData(createToolWorkflow);

      tool.setStatus(createToolWorkflow.getStatus());
      tool.setTags(createToolWorkflow.getTags());
      tool.setCategory(createToolWorkflow.getCategory());
      tool.setApplicationName(createToolWorkflow.getApplicationName());
      tool.setCreatedTime(new Date());
      tool.setModifiedTime(new Date());

      List<ToolVersionDetailsDto> versionsOfTool =
          getVersionsOfTool(
              createToolWorkflow.getApplicationName(),
              createToolWorkflow.getName(),
              createToolWorkflow.getCategory(),
              createToolWorkflow.getStatus());
      List<String> versionList =
          versionsOfTool.stream().map(ToolVersionDetailsDto::getVersion).toList();
      String nextVersion = getNextVersion(versionList, createToolWorkflow.getStatus());
      log.debug("Next version for workflow tool: {}", nextVersion);
      tool.setVersion(nextVersion);
      String toolId =
          newToolId(
              createToolWorkflow.getApplicationName(),
              tool.getToolName(),
              createToolWorkflow.getCategory(),
              nextVersion);
      tool.setToolId(toolId);
      tool.setReturnDirect(createToolWorkflow.getReturnDirect());
      tool.setNanoId(NanoIdUtils.randomNanoId());
      log.debug("Saving workflow tool with ID: {}", toolId);
      toolDao.save(tool);
      log.info("Successfully generated tool from workflow with ID: {}", toolId);
    } catch (Exception e) {
      log.error("Error while generating tool from workflow: {}", e.getMessage(), e);
      throw new BusinessException("Unable to generate tool from workflow");
    }
  }

  @Override
  public ToolConvertorDto getToolById(String id) {
    log.debug("Retrieving tool with ID: {}", id);
    Assert.notNull(id, "ID is required");

    try {
      Tool tool =
          toolDao
              .findByNanoId(id)
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TOOL_NOT_FOUND));
      log.debug("Successfully retrieved tool with ID: {}", id);
      return ToolConverter.convertToToolDto(tool);
    } catch (ResourceNotFoundException e) {
      log.warn("Error @method getToolById tool not found with id: {}", id);
      throw e;
    } catch (Exception e) {
      log.error("Error while retrieving tool: {}", e.getMessage(), e);
      throw new BusinessException("Unable to retrieve tool");
    }
  }

  @Override
  public List<ToolDtoSdk> getToolsByIds(List<Integer> id) {
    log.debug("Retrieving tools for IDs: {}", id);
    try {
      List<Tool> tools = toolDao.findAllById(id);
      log.debug("Found {} tools for the provided IDs", tools.size());
      return SdkUtils.getToolDtoListSdk(tools);
    } catch (Exception e) {
      log.error("Error while retrieving tools by IDs: {}", e.getMessage(), e);
      throw new BusinessException("Unable to retrieve tools by IDs");
    }
  }

  @Override
  public List<ToolConvertorDto> getToolsByIdsV1(List<String> ids) {
    log.debug("Retrieving tools (V1) for IDs: {}", ids);
    try {
      List<Tool> tools = toolDao.findByNanoIds(ids);
      log.debug("Found {} tools for the provided IDs", tools.size());
      return ToolConverter.getToolDtoList(tools);
    } catch (Exception e) {
      log.error("Error while retrieving tools (V1) by IDs: {}", e.getMessage(), e);
      throw new BusinessException("Unable to retrieve tools (V1) by IDs");
    }
  }

  @Override
  public Map<String, Object> checkCompilation(String sourceCode, String className) {
    log.debug("Checking compilation for class: {}", className);
    try {
      String decryptedSourceCode = AESUtils.decryptString(sourceCode);
      log.debug("Successfully decrypted source code");
      compileSourceCode(decryptedSourceCode, className);
      log.debug("Compilation successful for class: {}", className);
      return Map.of(PromptConstants.RESULT, true);
    } catch (Exception e) {
      log.error("Compilation failed for class {}: {}", className, e.getMessage(), e);
      return Map.of(PromptConstants.RESULT, false, PromptConstants.ERROR, e.getMessage());
    }
  }

  @Override
  public ResponseEntity<Resource> exportTool(String appName) {
    log.debug("Exporting tools for application: {}", appName);
    try {
      List<Tool> toolList =
          toolDao.getToolsByApplication(appName).stream()
              .filter(tool -> "sourceCode".equals(tool.getType()))
              .toList();

      List<Function<Tool, Object>> fieldExtractors =
          List.of(
              Tool::getApplicationName,
              Tool::getToolName,
              Tool::getDisplayName,
              Tool::getCategory,
              Tool::getVersion,
              Tool::getType,
              Tool::getToolId,
              Tool::getDescription,
              Tool::getClassName,
              tool -> tool.getRequestType() == null ? "NULL" : tool.getRequestType(),
              tool -> tool.getTags() == null ? "NULL" : tool.getTags(),
              ToolServiceImpl::getByteCode,
              tool -> tool.getSourceCode() == null ? "NULL" : tool.getSourceCode(),
              tool -> tool.getToolJson() == null ? "NULL" : tool.getToolJson(),
              tool -> tool.getToolImage() == null ? "NULL" : tool.getToolImage(),
              tool -> Optional.ofNullable(tool.getPrompt()).map(Prompt::getPromptId).orElse(null),
              tool -> tool.getApiToolInfo() == null ? "NULL" : tool.getApiToolInfo(),
              tool -> tool.getAgentId() == null ? "NULL" : tool.getAgentId(),
              Tool::getReturnDirect);
      log.debug("Found {} tools to export", toolList.size());
      return CSVUtils.exportCSV(toolList, csvColumnHeader, appName, fieldExtractors);
    } catch (Exception e) {
      log.error("Error while exporting tools: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(new ByteArrayResource(("Export failed: " + e.getMessage()).getBytes()));
    }
  }

  private static String getByteCode(Tool tool) {
    try {
      return JsonUtils.convertToJSON(tool.getByteCodeMap());
    } catch (Exception e) {
      return "{}";
    }
  }

  @Override
  public ResponseEntity<Resource> importTool(MultipartFile file) {
    log.debug("Importing tools from file: {}", file.getOriginalFilename());
    int maxRecords = MAX_IMPORT_RECORDS;
    try {
      Function<CSVRecord, Tool> recordTransformer =
          csvRecord -> {
            Tool tool = new Tool();
            mapToolFields(csvRecord, tool);
            return tool;
          };

      String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
      log.debug("Processing import for file: {}", exportedFileName);
      return CSVUtils.importCSV(
          file,
          csvColumnHeader,
          recordTransformer,
          this::saveOrUpdateTool,
          exportedFileName,
          maxRecords);
    } catch (Exception e) {
      log.error("Error while importing tools: {}", e.getMessage(), e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .body(new ByteArrayResource(("Import failed: " + e.getMessage()).getBytes()));
    }
  }

  @Override
  public Map<String, Boolean> existsTool(String toolName) {
    log.debug("Checking existence of tool with name: {}", toolName);
    try {
      boolean exists = toolDao.existsByToolName(toolName);
      log.debug("Tool existence check result for '{}': {}", toolName, exists);
      return Map.of(PromptConstants.RESULT, exists);
    } catch (Exception e) {
      log.error("Error while checking tool existence: {}", e.getMessage(), e);
      throw new BusinessException("Unable to check tool existence: " + e.getMessage(), e);
    }
  }

  private void mapToolFields(CSVRecord csvRecord, Tool tool) {
    log.debug("Going to map CSVRecord to Tool");
    tool.setApplicationName(csvRecord.get("Application Name"));
    tool.setToolName(csvRecord.get("Tool Name"));
    tool.setDisplayName(csvRecord.get("Display Name"));
    tool.setCategory(csvRecord.get("Category"));
    tool.setVersion(csvRecord.get("Version"));
    tool.setType(csvRecord.get("Type"));
    tool.setToolId(csvRecord.get("Tool ID"));
    tool.setDescription(csvRecord.get("Description"));
    tool.setClassName(csvRecord.get("Class Name"));
    tool.setRequestType(csvRecord.get("Request Type"));
    tool.setTags(csvRecord.get("Tags"));
    tool.setByteCodeMap(getByteCode(csvRecord.get("Byte Code Map")));
    tool.setSourceCode(csvRecord.get("Source Code"));
    tool.setToolJson(csvRecord.get("Tool JSON"));
    tool.setToolImage(csvRecord.get("Tool Image"));

    if (csvRecord.get(PromptConstants.AGENT_ID) != null
        && !csvRecord.get(PromptConstants.AGENT_ID).isEmpty()) {
      tool.setAgentId(Long.valueOf(csvRecord.get(PromptConstants.AGENT_ID)));
    }

    if (csvRecord.get(PromptConstants.PROMPT_ID) != null
        && !csvRecord.get(PromptConstants.PROMPT_ID).isEmpty()) {
      String promptId = csvRecord.get(PromptConstants.PROMPT_ID);
      Prompt prompt = promptDao.findByPromptId(promptId);
      tool.setPrompt(prompt);
    }
    tool.setApiToolInfo(csvRecord.get("Api Tool Info"));

    if (csvRecord.get(PromptConstants.RETURN_DIRECT) != null
        && !csvRecord.get(PromptConstants.RETURN_DIRECT).isEmpty()) {
      tool.setReturnDirect(Boolean.valueOf(csvRecord.get(PromptConstants.RETURN_DIRECT)));
    }

    tool.setNanoId(NanoIdUtils.randomNanoId());

    log.debug("successfully map CSVRecord to Tool");
  }

  private Map<String, byte[]> getByteCode(String byteCode) {
    try {
      byteCode = byteCode.replace("\"\"", "\"");
      JSONObject jsonObject = new JSONObject(byteCode);
      Map<String, byte[]> resultMap = new HashMap<>();
      Iterator<String> keys = jsonObject.keys();
      while (keys.hasNext()) {
        String key = keys.next();
        String value = jsonObject.getString(key);
        byte[] bytes = Base64.getDecoder().decode(value);
        resultMap.put(key, bytes);
      }
      log.debug("successfully generate bytecode");
      return resultMap;
    } catch (JSONException e) {
      log.warn("Error Occurred  while parsing json");
      throw new BusinessException(e.getMessage());
    }
  }

  private void saveOrUpdateTool(Tool tool) {
    Tool existingTool = toolDao.findByToolName(tool.getToolName());
    if (existingTool != null) {
      mapTool(tool, existingTool);
      existingTool.setModifiedTime(new Date());
      toolDao.save(existingTool);
    } else {
      tool.setStatus("PUBLISH");
      tool.setDeleted(false);
      tool.setModifiedTime(new Date());
      tool.setCreatedTime(new Date());
      toolDao.save(tool);
    }
  }

  private void mapTool(Tool source, Tool target) {
    copyBasicToolFields(source, target);
    copyAdditionalToolFields(source, target);
    log.debug("successfully map inside Tool");
  }

  private void copyBasicToolFields(Tool source, Tool target) {
    try {
      log.debug("Going to copy basic tool fields");
      if (source.getApplicationName() != null) {
        target.setApplicationName(source.getApplicationName());
      }
      if (source.getToolName() != null) {
        target.setToolName(source.getToolName());
      }
      if (source.getDisplayName() != null) {
        target.setDisplayName(source.getDisplayName());
      }
      if (source.getCategory() != null) {
        target.setCategory(source.getCategory());
      }
      if (source.getVersion() != null) {
        target.setVersion(source.getVersion());
      }
      if (source.getType() != null) {
        target.setType(source.getType());
      }
      if (source.getToolId() != null) {
        target.setToolId(source.getToolId());
      }
      if (source.getDescription() != null) {
        target.setDescription(source.getDescription());
      }
      if (source.getClassName() != null) {
        target.setClassName(source.getClassName());
      }
      if (source.getRequestType() != null) {
        target.setRequestType(source.getRequestType());
      }
      log.debug("successfully copy basic tool fields");
    } catch (Exception e) {
      log.error("Unable to copy tool fields", e);
      throw new BusinessException("unable to copy basic tool fields");
    }
  }

  private void copyAdditionalToolFields(Tool source, Tool target) {
    try {
      log.debug("Going to copy additional tool fields");
      if (source.getTags() != null) {
        target.setTags(source.getTags());
      }
      if (source.getByteCodeMap() != null) {
        target.setByteCodeMap(source.getByteCodeMap());
      }
      if (source.getSourceCode() != null) {
        target.setSourceCode(source.getSourceCode());
      }
      if (source.getToolJson() != null) {
        target.setToolJson(source.getToolJson());
      }
      if (source.getToolImage() != null) {
        target.setToolImage(source.getToolImage());
      }
      if (source.getAgentId() != null) {
        target.setAgentId(source.getAgentId());
      }
      if (source.getPrompt() != null) {
        target.setPrompt(source.getPrompt());
      }
      log.debug("successfully copy additional tool fields");
    } catch (Exception e) {
      log.error("unable to copy additional tool fields", e);
      throw new BusinessException("unable to copy additional tool fields");
    }
  }

  private Tool generateToolsFromWorkflowData(ToolWorkflowDto toolWorkflowDto) {
    log.debug(
        "Generating tools from workflow data for display name: {}",
        toolWorkflowDto.getDisplayName());
    Tool tool = new Tool();
    try {
      StringBuilder classCode = new StringBuilder();
      String className = getClassName(toolWorkflowDto.getName());
      String toolImage = toolWorkflowDto.getToolImage();

      // Create class boilerplate
      classCode.append(
          """
                            package com.enttribe.promptanalyzer.function;
                            import com.enttribe.commons.ai.aspect.Body;
                            import com.enttribe.commons.ai.aspect.ValidateParametersAspect;
                            import java.io.IOException;
                            import java.lang.reflect.RecordComponent;
                            import java.net.URI;
                            import java.net.http.HttpClient;
                            import org.slf4j.Logger;
                            import org.slf4j.LoggerFactory;
                            import java.net.http.HttpRequest;
                            import java.net.http.HttpResponse;
                            import java.util.HashMap;
                            import java.util.Map;
                            import java.util.function.Function;
                            import org.springframework.beans.factory.annotation.Autowired;
                            import org.springframework.context.annotation.Description;
                            import org.json.JSONObject;
                            """);

      ToolAuthDto toolAuthentication = toolWorkflowDto.getToolAuthentication();

      String description = toolWorkflowDto.getDescription();
      classCode.append("@Description(\"\"\"\n").append(description).append("\n\"\"\")\n");
      classCode
          .append("public class ")
          .append(className)
          .append(" implements Function<")
          .append(className)
          .append(".Request, ")
          .append(className)
          .append(".Response> {\n\n");
      classCode
          .append(PromptConstants.STATIC_METHOD_DECLARATION)
          .append("POST")
          .append(PromptConstants.ENDING);
      classCode
          .append(PromptConstants.STATIC_HOSTNAME_DECLARATION)
          .append(toolWorkflowDto.getHostName())
          .append(PromptConstants.ENDING);
      classCode
          .append("private static final Logger log = LoggerFactory.getLogger(")
          .append(className)
          .append(".class);\n");
      classCode
          .append(PromptConstants.AUTH_TYPE_DECLARATION)
          .append(toolAuthentication.getAuthType())
          .append(PromptConstants.ENDING);
      classCode
          .append(PromptConstants.AUTH_VALUE_DECLARATION)
          .append(toolAuthentication.getValue())
          .append(PromptConstants.ENDING);
      classCode.append(
          """
                            @Autowired
                            private ValidateParametersAspect validateParametersAspect;

                            @Override
                            public Response apply(Request request) {
                            try {
                                String validated = validateParametersAspect.validateParameters(request);
                                if (validated != null) {
                                    return new Response(validated);
                                }
                            } catch (Throwable e) {
                                log.error("Error while validation parameters, message : {}", e.getMessage(), e);
                            }
                            try {
                                URI uri = URI.create(HOSTNAME);
                                HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                                        .uri(uri)
                                        .header("Content-Type", "application/json");
                                if (!AUTHORIZATION_TYPE.equalsIgnoreCase("NONE")) {
                                    requestBuilder.header("Authorization", AUTHORIZATION_TYPE + " " + AUTHORIZATION_VALUE);
                                }

                                HttpClient httpClient = HttpClient.newHttpClient();
                                String payload = extractRequestBody(request);
                                    HttpRequest httpRequest = requestBuilder
                                            .POST(HttpRequest.BodyPublishers.ofString(payload))
                                            .build();

                                HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());
                                    return new Response(response.body());

                                } catch (IOException | InterruptedException e) {
                                    e.printStackTrace();
                                }
                                return null;
                            }
                            private String extractRequestBody(Request request) {
                                JSONObject inputJSON = new JSONObject();
                                JSONObject finalJSON=new JSONObject();
                                try {
                                    RecordComponent[] components = request.getClass().getRecordComponents();
                                    for (RecordComponent component : components) {
                                        if (component.getAnnotation(Body.class) == null) {
                                            Object value = component.getAccessor().invoke(request);
                                            if (value != null) {
                                                inputJSON.put(component.getName(), value);
                                            }
                                        }
                                    }

                                    finalJSON.put("inputs",inputJSON);
                                    finalJSON.put("response_mode","blocking");
                                    finalJSON.put("user","abc-123");
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                return finalJSON.toString();
                            }
                            """);

      // Add Request and Response records
      generateRequestRecord(classCode, toolWorkflowDto.getRequiredParameters());
      classCode.append("  public record Response(String data) {}\n");
      classCode.append("}\n");

      tool.setDisplayName(toolWorkflowDto.getDisplayName());
      tool.setDescription(description);
      tool.setSourceCode(AESUtils.encryptString(classCode.toString()));
      tool.setToolName(className);
      String packageName = "com.enttribe.promptanalyzer.function";
      tool.setClassName(packageName + "." + className);

      tool.setAuthType(toolAuthentication.getAuthorizationType());
      tool.setAuthKey(toolAuthentication.getKey());
      tool.setAuthValue(toolAuthentication.getValue());
      tool.setParameters(toolWorkflowDto.getRequiredParameters() + "");
      tool.setHttpMethod("POST");
      tool.setUrl(toolWorkflowDto.getHostName());
      tool.setType("workflow");
      tool.setToolImage(toolImage);
      tool.setDeleted(false);
      log.debug("Compiling source code for class: {}", className);
      Map<String, byte[]> byteCode = compileSourceCode(classCode.toString(), className);
      tool.setByteCodeMap(byteCode);
    } catch (Exception e) {
      if (e instanceof BusinessException ex) {
        throw new BusinessException(ex.getMessage(), e);
      }
      log.error(
          "Error while creating tool from workflow. exception message : {}", e.getMessage(), e);
      throw new BusinessException("unable to save tool from workflow");
    }
    return tool;
  }

  private String getClassName(String input) {
    input = input.trim();
    if (input.isEmpty()) {
      throw new IllegalArgumentException("Input cannot be empty");
    }
    // Use a StringBuilder for efficient string manipulation
    StringBuilder result = new StringBuilder();

    // Split the input into words based on spaces and capitalize each word
    for (String word : input.split("\\s+")) {
      if (!word.isEmpty()) {
        result
            .append(Character.toUpperCase(word.charAt(0)))
            .append(word.substring(1).toLowerCase());
      }
    }

    // Ensure the result is a valid Java identifier
    if (!Character.isJavaIdentifierStart(result.charAt(0))) {
      result.insert(0, '_');
    }

    for (int i = 1; i < result.length(); i++) {
      if (!Character.isJavaIdentifierPart(result.charAt(i))) {
        result.setCharAt(i, '_');
      }
    }

    log.debug("Converted class name: {}", result);
    return result.toString();
  }

  private void generateRequestRecord(
      StringBuilder classCode, List<Map<String, String>> requiredParameters) {
    log.debug("Generating request record with parameters: {}", requiredParameters);
    if (requiredParameters == null) {
      throw new BusinessException("Required parameters are not provided");
    }
    classCode.append(" public record Request(");
    boolean first = true;
    for (Map<String, String> requiredParameter : requiredParameters) {
      String paramName = requiredParameter.get("name");
      String paramType = requiredParameter.get("type");

      // Capitalize the first letter of the type (e.g., "string" -> "String")
      if (paramType != null && !paramType.isEmpty()) {
        paramType = paramType.substring(0, 1).toUpperCase() + paramType.substring(1);
      }
      if (!first) {
        classCode.append(", ");
      }

      // Append the corrected type and parameter name
      classCode.append(paramType).append(" ").append(paramName);
      first = false;
    }

    // Close the record definition
    classCode.append(") {}\n");
    log.debug("Generated request record: {}", classCode);
  }

  private List<ToolVersionDetailsDto> getVersionsOfTool(
      String applicationName, String toolName, String category, String status) {
    log.debug(
        "Retrieving versions of tool for application: {}, tool: {}, category: {}, status: {}",
        applicationName,
        toolName,
        category,
        status);
    return toolDao.getVersionsOfTool(applicationName, toolName, category, status);
  }

  private String getNextVersion(List<String> versions, String status) {
    log.debug("Determining next version for status: {}", status);
    if (status.equalsIgnoreCase("DRAFT")) {
      return "v-0";
    }
    if (versions == null || versions.isEmpty()) {
      // Default to v-1 if no versions are provided
      return "v-1";
    }

    int maxVersion =
        versions.stream()
            .filter(
                // Only consider valid "v-<number>" strings
                version -> version.matches("v-\\d+"))
            // Remove the prefix "v-"
            .map(version -> version.replace("v-", ""))
            // Parse the numeric part
            .mapToInt(Integer::parseInt)
            // Find the max value
            .max()
            // Default to 1 if no valid versions
            .orElse(1);
    // Increment and return the next version
    String nextVersion = "v-" + (maxVersion + 1);
    log.debug("Next version determined: {}", nextVersion);
    return nextVersion;
  }

  @Override
  public List<ToolConvertorDto> search(
      String filter, Integer offset, Integer size, String orderBy, String orderType) {
    log.debug("Inside @method search. @param : filter -> {}", filter);
    try {
      List<Tool> tools =
          customFilter.searchByFilter(Tool.class, filter, orderBy, orderType, offset, size);
      return ToolConverter.getToolDtoList(tools);
    } catch (Exception e) {
      log.error("Error message : {}", e.getMessage(), e);
      throw new BusinessException(e.getMessage());
    }
  }

  public Map<String, byte[]> compileSourceCode(String sourceCode, String className) {
    JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
    Assert.notNull(
        compiler, "No Java compiler is available. Ensure you are running with a JDK, not a JRE.");
    log.debug("@class ToolServiceImpl @method compileSourceCode class compiler success");
    Map<String, byte[]> byteCodeMap = null;
    // 1. Create a DiagnosticCollector to capture errors
    DiagnosticCollector<JavaFileObject> diagnostics = new DiagnosticCollector<>();
    try {
      InMemoryClassFileManager fileManager =
          new InMemoryClassFileManager(compiler.getStandardFileManager(null, null, null));
      log.debug("@class ToolServiceImpl @method compileSourceCode fileManager success");

      InMemoryJavaFile sourceFile = new InMemoryJavaFile(className, sourceCode);
      log.debug("@class ToolServiceImpl @method compileSourceCode sourceFile success");
      Iterable<JavaFileObject> compilationUnits = List.of(sourceFile);

      // 2. Pass diagnostics to the compiler
      JavaCompiler.CompilationTask task =
          compiler.getTask(null, fileManager, diagnostics, null, null, compilationUnits);

      if (!Boolean.TRUE.equals(task.call())) {
        // 3. Collect error messages
        StringBuilder errorMsg = new StringBuilder("Compilation failed:\n");
        for (Diagnostic<? extends JavaFileObject> diagnostic : diagnostics.getDiagnostics()) {
          if (diagnostic.getLineNumber() < 0) {
            continue;
          }
          errorMsg
              .append("Line ")
              .append(diagnostic.getLineNumber())
              .append(": ")
              .append(diagnostic.getMessage(null))
              .append("\n");
        }
        // 4. Throw with detailed error for UI
        throw new BusinessException(errorMsg.toString());
      }
      byteCodeMap = fileManager.getAllClassBytes();
    } catch (BusinessException e) {
      // Already handled, just rethrow
      throw e;
    } catch (Exception e) {
      log.error("error during source code compilation. Exception message : {}", e.getMessage(), e);
      throw new BusinessException("An unexpected error occurred while compiling the source code.");
    }
    return byteCodeMap;
  }

  private Tool getToolEntity(ToolDto dto) {
    if (dto == null) {
      return null;
    }
    Tool tool = new Tool();
    tool.setApplicationName(dto.getApplicationName());
    tool.setCategory(dto.getCategory());
    tool.setToolName(dto.getName());
    tool.setDescription(dto.getDescription());
    tool.setLanguage(dto.getLanguage());
    tool.setSourceCode(dto.getSourceCode());
    tool.setToolId(dto.getToolId());
    tool.setType(dto.getType());
    tool.setStatus(dto.getStatus());
    tool.setVersion(dto.getVersion());
    tool.setClassName(dto.getClassName());
    tool.setDisplayName(dto.getDisplayName());
    tool.setRequestType(dto.getRequestType());
    tool.setCreatedTime(new Date());
    tool.setModifiedTime(new Date());
    tool.setDeleted(false);
    return tool;
  }

  private String newToolId(String application, String name, String category, String version) {
    return (application + "-" + category + "-" + name + "-" + version).trim().replace(" ", "_");
  }

  @Override
  public Long count(String filter) {
    return customFilter.countByFilter(Tool.class, filter);
  }

  @Override
  public Map<String, String> softDelete(String id) {
    log.debug("Inside @method softDelete. @Param: id -> {}", id);
    Map<String, String> result = new HashMap<>();
    Tool tool =
        toolDao
            .findByNanoId(id)
            .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TOOL_NOT_FOUND));

    try {
      // Ensure the current user is the creator of the tool
      customFilter.verifyCreator(customerInfo.getUserId(), tool.getCreator());

      tool.setDeleted(true);
      toolDao.save(tool);
      result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
      log.debug("Tool {} soft-deleted successfully", id);
    } catch (AccessDeniedException ade) {
      throw ade; // Let global handler return 403
    } catch (ResourceNotFoundException ex) {
      log.warn("Tool not found with nanoId: {} - {}", id, ex.getMessage());
      result.put(PromptConstants.RESULT, PromptConstants.FAILED);
    } catch (UnsupportedOperationException | BusinessException ex) {
      log.warn("Authorization or user context issue for tool {}: {}", id, ex.getMessage());
      throw ex;
    } catch (Exception ex) {
      log.error("Unexpected error while soft deleting tool with ID: {}", id, ex);
      throw new BusinessException("Failed to delete tool");
    }
    return result;
  }

  private Tool generateApiClass(GenerateToolDto dto) {
    log.debug(
        "Generating API class for endpoint: {}, method: {}", dto.getEndpoint(), dto.getMethod());

    String className = toClassName(dto.getEndpoint(), dto.getMethod());
    log.debug(
        "generateApiClass method className : {}, endpoint : {}, method : {}",
        className,
        dto.getEndpoint(),
        dto.getMethod());

    StringBuilder classCode = new StringBuilder();
    String description = generateDescription(dto.getMethodDetails(), dto.getDefinitionsMap());

    // boilerplate + dynamic class content (use dto.get... instead of parameters)
    classCode.append(
        """
                        package com.enttribe.promptanalyzer.function;
                        import com.enttribe.commons.ai.aspect.Body;
                        import com.enttribe.commons.ai.aspect.ValidateParametersAspect;
                        import java.io.IOException;
                        import java.lang.reflect.RecordComponent;
                        import java.net.URI;
                        import java.net.http.HttpClient;
                        import java.net.http.HttpRequest;
                        import java.net.http.HttpResponse;
                        import org.slf4j.Logger;
                        import org.slf4j.LoggerFactory;
                        import java.util.HashMap;
                        import java.util.Map;
                        import java.util.function.Function;
                        import org.springframework.beans.factory.annotation.Autowired;
                        import org.springframework.context.annotation.Description;
                        """);

    classCode.append("@Description(\"\"\"\n").append(description).append("\n\"\"\")\n");
    classCode
        .append("public class ")
        .append(className)
        .append(" implements Function<")
        .append(className)
        .append(".Request, ")
        .append(className)
        .append(".Response> {\n");
    classCode
        .append("private static final String ENDPOINT = \"")
        .append(dto.getEndpoint())
        .append("\";\n");
    classCode
        .append(PromptConstants.STATIC_METHOD_DECLARATION)
        .append(dto.getMethod().toUpperCase())
        .append(PromptConstants.SEMICOLON_NEWLINES);
    classCode
        .append(PromptConstants.STATIC_HOSTNAME_DECLARATION)
        .append(dto.getHostName())
        .append(PromptConstants.SEMICOLON_NEWLINES);
    classCode
        .append("private static final String ENDPOINT = \"")
        .append(dto.getEndpoint())
        .append(PromptConstants.ENDING);
    classCode
        .append(PromptConstants.STATIC_METHOD_DECLARATION)
        .append(dto.getMethod().toUpperCase())
        .append(PromptConstants.SEMICOLON_NEWLINES);
    classCode
        .append(PromptConstants.STATIC_HOSTNAME_DECLARATION)
        .append(dto.getHostName())
        .append(PromptConstants.SEMICOLON_NEWLINES);
    classCode
        .append("private static final Logger log = LoggerFactory.getLogger(")
        .append(className)
        .append(".class);\n");
    classCode
        .append(PromptConstants.AUTH_TYPE_DECLARATION)
        .append(dto.getAuthorizationType())
        .append(PromptConstants.SEMICOLON_NEWLINES);
    classCode
        .append(PromptConstants.AUTH_VALUE_DECLARATION)
        .append(dto.getAuthorizationKey())
        .append(PromptConstants.SEMICOLON_NEWLINES);

    // Continue rest of your logic unchanged, using `dto.get...()` wherever needed

    generateRequestRecord(classCode, dto.getMethodDetails(), dto.getDefinitionsMap());
    classCode
        .append(PromptConstants.AUTH_TYPE_DECLARATION)
        .append(dto.getAuthorizationType())
        .append(PromptConstants.SEMICOLON_NEWLINES);
    classCode
        .append(PromptConstants.AUTH_VALUE_DECLARATION)
        .append(dto.getAuthorizationKey())
        .append(PromptConstants.SEMICOLON_NEWLINES);
    classCode.append(
        """
                        @Autowired
                        private ValidateParametersAspect validateParametersAspect;

                        @Override
                        public Response apply(Request request) {
                        try {
                            String validated = validateParametersAspect.validateParameters(request);
                            if (validated != null) {
                                return new Response(validated);
                            }
                        } catch (Throwable e) {
                            log.error("Error while validation parameters, message : {}", e.getMessage(), e);
                        }

                        String endpoint = replacePathParams(ENDPOINT, request);
                        String urlString = HOSTNAME + endpoint;
                        try {
                            URI uri = URI.create(urlString);
                            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                                    .uri(uri)
                                    .header("Content-Type", "application/json");
                            if (!AUTHORIZATION_TYPE.equalsIgnoreCase("NONE")) {
                                requestBuilder.header("Authorization", AUTHORIZATION_TYPE + " " + AUTHORIZATION_VALUE);
                            }

                            HttpClient httpClient = HttpClient.newHttpClient();

                            if (METHOD.equalsIgnoreCase("GET")) {
                                HttpRequest httpRequest = requestBuilder.GET().build();

                                // Send the HTTP request
                                HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());

                                return new Response(response.body());
                            }
                            if (METHOD.equalsIgnoreCase("POST")) {
                                String payload = extractRequestBody(request);
                                HttpRequest httpRequest = requestBuilder
                                        .POST(HttpRequest.BodyPublishers.ofString(payload))
                                        .build();

                                HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());
                                return new Response(response.body());
                            }
                        } catch (IOException | InterruptedException e) {
                            e.printStackTrace();
                        }
                            return null;
                        }


                        private String extractRequestBody(Request request) {
                            try {
                                RecordComponent[] components = request.getClass().getRecordComponents();
                                for (RecordComponent component : components) {
                                    if (component.getAnnotation(Body.class) != null) {
                                        Object authValue = component.getAccessor().invoke(request);
                                        if (authValue != null) {
                                            return authValue.toString();
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            return "";
                        }

                        private Map<String, String> extractPathParams(Request request) {
                            Map<String, String> pathParams = new HashMap<>();
                            try {
                                RecordComponent[] components = request.getClass().getRecordComponents();
                                for (RecordComponent component : components) {
                                    if (component.getAnnotation(Body.class) == null) {
                                        Object authValue = component.getAccessor().invoke(request);
                                        if (authValue != null) {
                                            pathParams.put(component.getName(), authValue.toString());
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            return pathParams;
                        }

                        private String replacePathParams(String endpoint, Request request) {
                            Map<String, String> pathParams = extractPathParams(request);
                            for (Map.Entry<String, String> entry : pathParams.entrySet()) {
                                String placeholder = "{" + entry.getKey() + "}";
                                if (endpoint.contains(placeholder)) {
                                    endpoint = endpoint.replace(placeholder, entry.getValue());
                                }
                            }
                            return endpoint;
                        }
                        """);

    // Add Request and Response records
    generateRequestRecord(classCode, dto.getMethodDetails(), dto.getDefinitionsMap());
    generateResponseRecord(classCode);
    classCode.append("}\n");

    Tool tool = new Tool();
    tool.setApplicationName(dto.getApplicationName());
    tool.setCategory(dto.getCategory());
    tool.setToolName(className);
    tool.setClassName("com.enttribe.promptanalyzer.function." + className);
    tool.setDisplayName(className);
    tool.setHttpMethod(dto.getMethod());
    tool.setAuthType(dto.getAuthorizationType());
    tool.setAuthKey(dto.getAuthorizationKey());
    tool.setAuthValue(dto.getAuthValue());
    tool.setDescription(description);
    tool.setSourceCode(AESUtils.encryptString(classCode.toString()));
    tool.setUrl(dto.getEndpoint());
    tool.setType("swaggerJson");
    tool.setDeleted(false);
    log.debug("Generated API class code: {}", classCode);

    Map<String, byte[]> byteCode = compileSourceCode(classCode.toString(), className);
    tool.setByteCodeMap(byteCode);

    return tool;
  }

  private static String toClassName(String endpoint, String method) {
    String[] parts = endpoint.split("/");
    StringBuilder className =
        new StringBuilder(method.substring(0, 1).toUpperCase() + method.substring(1).toLowerCase());
    for (String part : parts) {
      if (!part.isEmpty() && !part.contains("{")) {
        className.append(part.substring(0, 1).toUpperCase()).append(part.substring(1));
      }
    }
    return className.toString();
  }

  private String generateDescription(
      JSONObject methodDetails, Map<String, JSONObject> definitionsMap) {
    StringBuilder description = new StringBuilder();
    description
        .append("API Description: ")
        .append(methodDetails.optString("summary", "No description available"))
        .append("\n");

    JSONArray parameters = methodDetails.optJSONArray("parameters");
    if (parameters != null) {
      description.append("Parameters:\n");
      Iterator<Object> params = parameters.iterator();
      while (params.hasNext()) {
        JSONObject param = (JSONObject) params.next();
        String paramName = param.optString("name");
        JSONObject schema = param.optJSONObject(PromptConstants.SCHEMA);
        String paramType = mapSwaggerTypeToJava(param.optString("type"));
        description.append("- ").append(paramName).append(": ").append(paramType).append("\n");
        if (schema != null) {
          JSONObject schemaObject = resolveSchema(schema, definitionsMap);
          schemaObject.remove("xml");
          description.append("  - Schema: ").append(schemaObject.toString()).append("\n");
        }
      }
    }

    return description.toString();
  }

  private void generateRequestRecord(
      StringBuilder classCode, JSONObject methodDetails, Map<String, JSONObject> definitionsMap) {
    classCode.append("    public record Request(");

    // Dynamically generate fields for the Request record based on Swagger JSON
    JSONArray parameters = methodDetails.optJSONArray("parameters");

    if (parameters != null) {
      Iterator<Object> params = parameters.iterator();
      boolean first = true;

      while (params.hasNext()) {
        JSONObject param = (JSONObject) params.next();
        String paramName = "";
        String paramType = "";
        if (param.has(PromptConstants.SCHEMA)) {
          JSONObject schema = param.getJSONObject(PromptConstants.SCHEMA);
          JSONObject resolvedSchema = resolveSchema(schema, definitionsMap);
          resolvedSchema.remove("xml");
          paramName = param.optString("name");
          paramType = mapSwaggerTypeToJava(param.optString("type"));
          classCode
              .append("@Body(value=\"")
              .append(resolvedSchema.toString().replace("\"", "\\\""))
              .append("\") ")
              .append(paramType)
              .append(" ")
              .append(paramName);
        } else {
          paramName = param.optString("name");
          paramType = mapSwaggerTypeToJava(param.optString("type"));
          classCode.append(paramType).append(" ").append(paramName);
        }

        if (!first) {
          classCode.append(", ");
        }

        first = false;
      }
    }

    classCode.append(") {}\n");
  }

  private void generateResponseRecord(StringBuilder classCode) {
    classCode.append("    public record Response(String data) {}\n");
  }

  private String mapSwaggerTypeToJava(String swaggerType) {
    return switch (swaggerType) {
      case "string" -> "String";
      case "integer" -> "Integer";
      case "boolean" -> "Boolean";
      case "array" -> "List";
      case "object" -> "Object";
        // Default type
      default -> "String";
    };
  }

  private JSONObject resolveSchema(JSONObject schema, Map<String, JSONObject> definitionsMap) {
    if (schema.has("$ref")) {
      String ref = schema.getString("$ref");
      String definitionKey = ref.replace("#/definitions/", "");
      JSONObject definition = definitionsMap.get(definitionKey);
      if (definition != null) {
        return definition;
      }
    }
    return schema;
  }

  @Override
  public Map<String, String> updateTagById(String id, Map<String, String> tags) {
    String newTag = tags.get("tags");
    log.debug("inside @method updateTagById. @param  : id -> {} tags : {}", id, newTag);

    try {
      Tool tool =
          toolDao
              .findByNanoId(id)
              .orElseThrow(() -> new ResourceNotFoundException(PromptConstants.TOOL_NOT_FOUND));
      tool.setTags(newTag);
      toolDao.save(tool);
      return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
    } catch (ResourceNotFoundException e) {
      log.warn("Error @method updateTagById tool not found with id: {}", id);
      throw e;
    } catch (Exception e) {
      log.error("error while updating tag of tool : {}", e.getMessage(), e);
      throw new BusinessException("Error while updating tag of tool");
    }
  }

  @Override
  public List<HashMap<String, String>> getToolCallbackProvider() {
    log.info("Starting getToolCallbackProvider method");
    try {
      // Create a new MethodToolCallbackProvider directly
      MethodToolCallbackProvider provider =
          MethodToolCallbackProvider.builder().toolObjects(mcpToolService).build();

      ToolCallback[] callbacks = provider.getToolCallbacks();
      log.info("Found {} tool callbacks", callbacks.length);

      if (callbacks.length == 0) {
        log.warn("No tool callbacks found. Checking method annotations...");
        // Log all methods with @Tool annotation
        Arrays.stream(this.getClass().getDeclaredMethods())
            .filter(
                method ->
                    method.isAnnotationPresent(org.springframework.ai.tool.annotation.Tool.class))
            .forEach(
                method -> {
                  org.springframework.ai.tool.annotation.Tool tool =
                      method.getAnnotation(org.springframework.ai.tool.annotation.Tool.class);
                  log.info(
                      "Found @Tool method: {} - {} - {}",
                      method.getName(),
                      tool.description(),
                      tool.returnDirect());
                });
      }

      return Arrays.stream(callbacks)
          .map(
              tool -> {
                HashMap<String, String> map = new HashMap<>();
                map.put(PromptConstants.DESCRIPTION, tool.getToolDefinition().description());
                map.put("name", tool.getToolDefinition().name());
                InputTypeSchemaWrapper inputTypeSchemaWrapper =
                    new InputTypeSchemaWrapper(tool.getToolDefinition().inputSchema());
                map.put(PromptConstants.SCHEMA, inputTypeSchemaWrapper.getSchema());
                map.put("type", inputTypeSchemaWrapper.getType());
                map.put("properties", inputTypeSchemaWrapper.getProperties());
                map.put("required", inputTypeSchemaWrapper.getRequired());
                map.put("additionalProperties", inputTypeSchemaWrapper.isAdditionalProperties());
                return map;
              })
          .toList();
    } catch (Exception e) {
      log.error("Error in getToolCallbackProvider: {} ", e, e.getMessage());
      throw e;
    }
  }

  @Override
  public ToolDto getToolByName(String toolName) {
    log.debug("Searching for tool with name: {}", toolName);

    return Optional.ofNullable(toolDao.findByToolName(toolName))
        .map(ToolConverter::mapToToolDto)
        .orElseThrow(
            () -> new ResourceNotFoundException("Tool with name '" + toolName + "' not found."));
  }

  @Override
  public List<MicroIntentResponseDto> getX101ToolList() {
    try {
      List<MicroIntentResponseDto> microIntentDetails = apiService.getMicroIntentDetails();
      List<Tool> x101Tools = toolDao.findByType(X_101_TYPE);

      // Index tools by (entityName, intentName) for quick lookup
      Map<String, Tool> toolLookup =
          x101Tools.stream()
              .collect(
                  Collectors.toMap(
                      tool -> tool.getEntityName() + "::" + tool.getIntentName(),
                      Function.identity(),
                      (a, b) -> a // handle duplicates by keeping first
                      ));

      for (MicroIntentResponseDto dto : microIntentDetails) {
        String key = dto.getEntityName() + "::" + dto.getIntentName();
        Tool matchedTool = toolLookup.get(key);
        if (matchedTool != null) {
          dto.setId(matchedTool.getNanoId()); // set the ID if match found
          dto.setName(matchedTool.getToolName());
          dto.setDescription(matchedTool.getDescription());
          dto.setDisplayName(matchedTool.getDisplayName());
        }
      }

      return microIntentDetails;

    } catch (Exception e) {
      log.error("Failed to get micro intent list", e);
      throw new BusinessException("Failed to get micro intent list");
    }
  }

  @Override
  public List<ToolDto> findToolsByIds(List<Integer> ids) {
    try {
      List<Tool> tools = toolDao.findAllById(ids);
      log.debug("Found {} tools for IDs: {}", tools.size(), ids);
      return ToolConverter.mapToToolDtoList(tools);
    } catch (Exception e) {
      log.error("Failed to get tool", e);
      throw new BusinessException("Failed to get tool");
    }
  }
}
