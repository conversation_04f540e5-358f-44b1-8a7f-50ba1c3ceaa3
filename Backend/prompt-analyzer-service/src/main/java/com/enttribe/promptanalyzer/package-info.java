/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Root package for the Prompt Analyzer application.
 *
 * <p>This package contains the main classes, configuration, and entry points for the
 * Prompt Analyzer application. It serves as the root for all submodules and features.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Application entry point and configuration</li>
 *   <li>Integration of all modules and features</li>
 *   <li>Centralized application management</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>This package is used as the root for all application logic, configuration, and
 * integration of submodules.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer;
