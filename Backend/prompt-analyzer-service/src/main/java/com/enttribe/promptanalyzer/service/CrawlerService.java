/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service;

/**
 * Service interface for managing web crawling operations. This interface defines methods for
 * processing websites. Author: VisionWaves Version: 1.0
 */
public interface CrawlerService {

  /**
   * Processes website-type knowledge bases by crawling and extracting relevant data. This method
   * triggers the crawling and processing of websites to populate the knowledge base.
   */
  void processWebSiteKB();
}
