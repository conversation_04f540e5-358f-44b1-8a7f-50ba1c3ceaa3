/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.commons.ai.aspect;

import java.util.Map;

/**
 * Interface for executing dynamic variable retrieval based on a user ID. Implementations should
 * provide logic to return a map of dynamic variables.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface DynamicVariables {
  Map<String, String> execute(String userId);
}
