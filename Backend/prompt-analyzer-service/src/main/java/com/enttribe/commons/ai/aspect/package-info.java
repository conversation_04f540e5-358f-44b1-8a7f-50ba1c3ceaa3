/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * AI Aspect package for the Commons module.
 *
 * <p>This package contains aspect-oriented classes and annotations for AI-related
 * features in the Commons module. These aspects provide cross-cutting concerns such as
 * validation, dynamic variable handling, and request body processing.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Aspect-oriented programming for AI features</li>
 *   <li>Support for validation and dynamic variables</li>
 *   <li>Enhancement of request/response processing</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>AI aspect classes are used by the Commons module to provide reusable, cross-cutting
 * features for AI-related operations.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.commons.ai.aspect;
