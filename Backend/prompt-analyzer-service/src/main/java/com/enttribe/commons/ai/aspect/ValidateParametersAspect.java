/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.commons.ai.aspect;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.RecordComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Aspect for validating parameters of request objects. Ensures that required fields in JSON
 * payloads are present and non-empty.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class ValidateParametersAspect {

  private static final Logger logger = LoggerFactory.getLogger(ValidateParametersAspect.class);

  private final ObjectMapper objectMapper = new ObjectMapper();

  /**
   * Validates the parameters of a given request object. Checks for required fields in JSON payloads
   * and logs missing values.
   *
   * @param request the request object to validate
   * @return a validation error message if validation fails, or null if validation passes
   * @throws Throwable if an error occurs during validation
   */
  public String validateParameters(Object request)
      throws InvocationTargetException, IllegalAccessException {

    logger.info("Aspect executed");
    RecordComponent[] components = request.getClass().getRecordComponents();
    for (RecordComponent component : components) {
      Object value = component.getAccessor().invoke(request);

      Body bodyAnnotation = component.getAnnotation(Body.class);
      if (bodyAnnotation != null) {
        String validationError = validateJsonBody(component, value, bodyAnnotation);
        if (validationError != null) {
          return validationError;
        }
      } else {
        String validationError = validateNonBodyParameter(component, value);
        if (validationError != null) {
          return validationError;
        }
      }
    }
    return null;
  }

  /**
   * Validates the JSON body of a parameter.
   *
   * @param component the record component being validated
   * @param value the value of the parameter
   * @param bodyAnnotation the annotation containing the JSON schema
   * @return a validation error message if validation fails, or null if validation passes
   */
  private String validateJsonBody(RecordComponent component, Object value, Body bodyAnnotation) {
    if (value == null || !(value instanceof String jsonString)) {
      return "Invalid or null JSON payload for parameter: " + component.getName();
    }

    try {
      logger.info("jsonString is {} ", jsonString);
      JsonNode jsonPayload = objectMapper.readTree(jsonString);
      JsonNode schema = objectMapper.readTree(bodyAnnotation.value());
      return checkRequiredFields(jsonPayload, schema);
    } catch (Exception e) {
      return "Failed to parse JSON payload for parameter: " + component.getName();
    }
  }

  /**
   * Checks for required fields in the JSON payload against the provided schema.
   *
   * @param jsonPayload the JSON payload to validate
   * @param schema the JSON schema defining required fields
   * @return a validation error message if required fields are missing, or null if validation passes
   */
  private String checkRequiredFields(JsonNode jsonPayload, JsonNode schema) {
    JsonNode requiredFields = schema.get("required");
    if (requiredFields != null && requiredFields.isArray()) {
      for (JsonNode fieldNode : requiredFields) {
        String fieldName = fieldNode.asText();
        logger.info("fieldName is : {} , jsonPayload : {}", fieldName, jsonPayload.get(fieldName));
        if (!jsonPayload.has(fieldName)
            || jsonPayload.get(fieldName).isNull()
            || jsonPayload.get(fieldName).toString().isEmpty()) {
          return "Missing or empty required field in JSON: " + fieldName;
        }
      }
    }
    return null;
  }

  /**
   * Validates non-body parameters of a request object.
   *
   * @param component the record component being validated
   * @param value the value of the parameter
   * @return a validation error message if validation fails, or null if validation passes
   */
  private String validateNonBodyParameter(RecordComponent component, Object value) {
    if (value == null || value.toString().isEmpty()) {
      logger.info("Missing value for parameter: {}", component.getName());
      return "Missing value for parameter: " + component.getName();
    }
    return null;
  }
}
