/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Utility package for the Conversational AI module.
 *
 * <p>This package contains utility and helper classes that provide common functionality
 * and reusable code for the Conversational AI module. Utilities support various
 * operations such as HTTP communication, data formatting, and other cross-cutting concerns.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Reusable utility methods and classes</li>
 *   <li>Support for HTTP and data formatting</li>
 *   <li>Common helpers for module-wide use</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Utility classes are used throughout the Conversational AI module to avoid code duplication and
 * to provide standardized solutions for common programming tasks.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.conversationalai.util;
