/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Server Transport package for the application.
 *
 * <p>This package contains classes and interfaces for server transport mechanisms
 * and protocols within the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Server transport configuration and management</li>
 *   <li>Integration with server-side protocols</li>
 *   <li>Support for scalable and efficient communication</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Server transport classes are used by the application to manage and implement
 * communication protocols and server-side transport features.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package io.modelcontextprotocol.server.transport;
