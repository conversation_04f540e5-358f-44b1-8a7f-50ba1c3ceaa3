/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package org.springframework.ai.model.tool;

import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.util.JsonUtils;
import io.micrometer.observation.ObservationRegistry;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.ToolResponseMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.ai.tool.execution.DefaultToolExecutionExceptionProcessor;
import org.springframework.ai.tool.execution.ToolExecutionException;
import org.springframework.ai.tool.execution.ToolExecutionExceptionProcessor;
import org.springframework.ai.tool.observation.DefaultToolCallingObservationConvention;
import org.springframework.ai.tool.observation.ToolCallingObservationContext;
import org.springframework.ai.tool.observation.ToolCallingObservationConvention;
import org.springframework.ai.tool.observation.ToolCallingObservationDocumentation;
import org.springframework.ai.tool.resolution.DelegatingToolCallbackResolver;
import org.springframework.ai.tool.resolution.ToolCallbackResolver;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * Default implementation of {@link ToolCallingManager}.
 *
 * <AUTHOR> Vitale
 * @since 1.0.0
 */
public final class DefaultToolCallingManager implements ToolCallingManager {

  private static final Logger logger = LoggerFactory.getLogger(DefaultToolCallingManager.class);

  // @formatter:off

  private static final ObservationRegistry DEFAULT_OBSERVATION_REGISTRY = ObservationRegistry.NOOP;

  private static final ToolCallingObservationConvention DEFAULT_OBSERVATION_CONVENTION =
      new DefaultToolCallingObservationConvention();

  private static final ToolCallbackResolver DEFAULT_TOOL_CALLBACK_RESOLVER =
      new DelegatingToolCallbackResolver(List.of());

  private static final ToolExecutionExceptionProcessor DEFAULT_TOOL_EXECUTION_EXCEPTION_PROCESSOR =
      DefaultToolExecutionExceptionProcessor.builder().build();

  // @formatter:on

  private final ObservationRegistry observationRegistry;

  private final ToolCallbackResolver toolCallbackResolver;

  private final ToolExecutionExceptionProcessor toolExecutionExceptionProcessor;

  private ToolCallingObservationConvention observationConvention = DEFAULT_OBSERVATION_CONVENTION;

  public DefaultToolCallingManager(
      ObservationRegistry observationRegistry,
      ToolCallbackResolver toolCallbackResolver,
      ToolExecutionExceptionProcessor toolExecutionExceptionProcessor) {
    Assert.notNull(observationRegistry, "observationRegistry cannot be null");
    Assert.notNull(toolCallbackResolver, "toolCallbackResolver cannot be null");
    Assert.notNull(toolExecutionExceptionProcessor, "toolCallExceptionConverter cannot be null");

    this.observationRegistry = observationRegistry;
    this.toolCallbackResolver = toolCallbackResolver;
    this.toolExecutionExceptionProcessor = toolExecutionExceptionProcessor;
  }

  @Override
  public List<ToolDefinition> resolveToolDefinitions(ToolCallingChatOptions chatOptions) {
    Assert.notNull(chatOptions, "chatOptions cannot be null");

    List<ToolCallback> toolCallbacks = new ArrayList<>(chatOptions.getToolCallbacks());
    for (String toolName : chatOptions.getToolNames()) {
      // Skip the tool if it is already present in the request toolCallbacks.
      // That might happen if a tool is defined in the options
      // both as a ToolCallback and as a tool name.
      if (chatOptions.getToolCallbacks().stream()
          .anyMatch(tool -> tool.getToolDefinition().name().equals(toolName))) {
        continue;
      }
      ToolCallback toolCallback = this.toolCallbackResolver.resolve(toolName);
      if (toolCallback == null) {
        throw new IllegalStateException("No ToolCallback found for tool name: " + toolName);
      }
      toolCallbacks.add(toolCallback);
    }

    return toolCallbacks.stream().map(ToolCallback::getToolDefinition).toList();
  }

  @Override
  public ToolExecutionResult executeToolCalls(Prompt prompt, ChatResponse chatResponse) {
    Assert.notNull(prompt, "prompt cannot be null");
    Assert.notNull(chatResponse, "chatResponse cannot be null");

    Optional<Generation> toolCallGeneration =
        chatResponse.getResults().stream()
            .filter(g -> !CollectionUtils.isEmpty(g.getOutput().getToolCalls()))
            .findFirst();

    if (toolCallGeneration.isEmpty()) {
      throw new IllegalStateException("No tool call requested by the chat model");
    }

    AssistantMessage assistantMessage = toolCallGeneration.get().getOutput();

    ToolContext toolContext = buildToolContext(prompt, assistantMessage);

    boolean shouldExecuteTool = false;
    if (toolContext.getContext() != null) {
      shouldExecuteTool = (boolean) toolContext.getContext().get("shouldExecuteTool");
    }
    if (!shouldExecuteTool) {
      return getDefaultToolExecutionResult(prompt, assistantMessage);
    }

    InternalToolExecutionResult internalToolExecutionResult =
        executeToolCall(prompt, assistantMessage, toolContext);

    List<Message> conversationHistory =
        buildConversationHistoryAfterToolExecution(
            prompt.getInstructions(),
            assistantMessage,
            internalToolExecutionResult.toolResponseMessage());

    return ToolExecutionResult.builder()
        .conversationHistory(conversationHistory)
        .returnDirect(internalToolExecutionResult.returnDirect())
        .build();
  }

  private DefaultToolExecutionResult getDefaultToolExecutionResult(
      Prompt prompt, AssistantMessage assistantMessage) {
    InternalToolExecutionResult internalToolExecutionResult = null;
    try {
      ToolResponseMessage.ToolResponse toolResponse =
          new ToolResponseMessage.ToolResponse(
              "abc", "toolName", JsonUtils.convertToJSON(assistantMessage.getToolCalls()));
      internalToolExecutionResult =
          new InternalToolExecutionResult(
              new ToolResponseMessage(List.of(toolResponse), Map.of()), true);
    } catch (Exception e) {
      logger.error("error inside @method executeToolCalls : {}", e.getMessage(), e);
      throw new BusinessException("Unable to Execute Tool Calls");
    }

    List<Message> conversationHistory =
        buildConversationHistoryAfterToolExecution(
            prompt.getInstructions(),
            assistantMessage,
            internalToolExecutionResult.toolResponseMessage());

    return ToolExecutionResult.builder()
        .conversationHistory(conversationHistory)
        .returnDirect(true)
        .build();
  }

  private static ToolContext buildToolContext(Prompt prompt, AssistantMessage assistantMessage) {
    Map<String, Object> toolContextMap = Map.of();

    if (prompt.getOptions() instanceof ToolCallingChatOptions toolCallingChatOptions
        && !CollectionUtils.isEmpty(toolCallingChatOptions.getToolContext())) {
      toolContextMap = new HashMap<>(toolCallingChatOptions.getToolContext());

      List<Message> messageHistory = new ArrayList<>(prompt.copy().getInstructions());
      String content = Objects.requireNonNullElse(assistantMessage.getText(), "-");
      messageHistory.add(
          new AssistantMessage(
              content, assistantMessage.getMetadata(), assistantMessage.getToolCalls()));

      toolContextMap.put(
          ToolContext.TOOL_CALL_HISTORY,
          buildConversationHistoryBeforeToolExecution(prompt, assistantMessage));
    }

    return new ToolContext(toolContextMap);
  }

  private static List<Message> buildConversationHistoryBeforeToolExecution(
      Prompt prompt, AssistantMessage assistantMessage) {
    List<Message> messageHistory = new ArrayList<>(prompt.copy().getInstructions());
    String content = Objects.requireNonNullElse(assistantMessage.getText(), "-");
    messageHistory.add(
        new AssistantMessage(
            content, assistantMessage.getMetadata(), assistantMessage.getToolCalls()));
    return messageHistory;
  }

  /** Execute the tool call and return the response message. */
  private InternalToolExecutionResult executeToolCall(
      Prompt prompt, AssistantMessage assistantMessage, ToolContext toolContext) {
    List<ToolCallback> toolCallbacks = List.of();
    if (prompt.getOptions() instanceof ToolCallingChatOptions toolCallingChatOptions) {
      toolCallbacks = toolCallingChatOptions.getToolCallbacks();
    }

    List<ToolResponseMessage.ToolResponse> toolResponses = new ArrayList<>();

    Boolean returnDirect = null;

    for (AssistantMessage.ToolCall toolCall : assistantMessage.getToolCalls()) {

      logger.debug("Executing tool call: {}", toolCall.name());

      String toolName = toolCall.name();
      String toolInputArguments = toolCall.arguments();

      ToolCallback toolCallback =
          toolCallbacks.stream()
              .filter(tool -> toolName.equals(tool.getToolDefinition().name()))
              .findFirst()
              .orElseGet(() -> this.toolCallbackResolver.resolve(toolName));

      if (toolCallback == null) {
        throw new IllegalStateException("No ToolCallback found for tool name: " + toolName);
      }

      if (returnDirect == null) {
        returnDirect = toolCallback.getToolMetadata().returnDirect();
      } else {
        returnDirect = returnDirect && toolCallback.getToolMetadata().returnDirect();
      }

      ToolCallingObservationContext observationContext =
          ToolCallingObservationContext.builder()
              .toolDefinition(toolCallback.getToolDefinition())
              .toolMetadata(toolCallback.getToolMetadata())
              .toolCallArguments(toolInputArguments)
              .build();

      String toolCallResult =
          ToolCallingObservationDocumentation.TOOL_CALL
              .observation(
                  this.observationConvention,
                  DEFAULT_OBSERVATION_CONVENTION,
                  () -> observationContext,
                  this.observationRegistry)
              .observe(
                  () -> {
                    String toolResult;
                    try {
                      toolResult = toolCallback.call(toolInputArguments, toolContext);
                    } catch (ToolExecutionException ex) {
                      toolResult = this.toolExecutionExceptionProcessor.process(ex);
                    }
                    observationContext.setToolCallResult(toolResult);
                    return toolResult;
                  });

      toolResponses.add(
          new ToolResponseMessage.ToolResponse(
              toolCall.id(), toolName, toolCallResult != null ? toolCallResult : ""));
    }

    return new InternalToolExecutionResult(
        new ToolResponseMessage(toolResponses, Map.of()), returnDirect);
  }

  private List<Message> buildConversationHistoryAfterToolExecution(
      List<Message> previousMessages,
      AssistantMessage assistantMessage,
      ToolResponseMessage toolResponseMessage) {
    List<Message> messages = new ArrayList<>(previousMessages);
    messages.add(assistantMessage);
    messages.add(toolResponseMessage);
    return messages;
  }

  public void setObservationConvention(ToolCallingObservationConvention observationConvention) {
    this.observationConvention = observationConvention;
  }

  public static Builder builder() {
    return new Builder();
  }

  private record InternalToolExecutionResult(
      ToolResponseMessage toolResponseMessage, boolean returnDirect) {}

  public static final class Builder {

    private ObservationRegistry observationRegistry = DEFAULT_OBSERVATION_REGISTRY;

    private ToolCallbackResolver toolCallbackResolver = DEFAULT_TOOL_CALLBACK_RESOLVER;

    private ToolExecutionExceptionProcessor toolExecutionExceptionProcessor =
        DEFAULT_TOOL_EXECUTION_EXCEPTION_PROCESSOR;

    private Builder() {}

    public Builder observationRegistry(ObservationRegistry observationRegistry) {
      this.observationRegistry = observationRegistry;
      return this;
    }

    public Builder toolCallbackResolver(ToolCallbackResolver toolCallbackResolver) {
      this.toolCallbackResolver = toolCallbackResolver;
      return this;
    }

    public Builder toolExecutionExceptionProcessor(
        ToolExecutionExceptionProcessor toolExecutionExceptionProcessor) {
      this.toolExecutionExceptionProcessor = toolExecutionExceptionProcessor;
      return this;
    }

    public DefaultToolCallingManager build() {
      return new DefaultToolCallingManager(
          this.observationRegistry,
          this.toolCallbackResolver,
          this.toolExecutionExceptionProcessor);
    }
  }
}
