/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * OpenAI API package for the application.
 *
 * <p>This package contains classes and interfaces for integrating with the OpenAI API
 * within the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>OpenAI API integration and configuration</li>
 *   <li>Support for OpenAI model operations</li>
 *   <li>Facilitation of AI-driven features</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>OpenAI API classes are used by AI model and service components to interact with
 * OpenAI services and models.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package org.springframework.ai.openai.api;
