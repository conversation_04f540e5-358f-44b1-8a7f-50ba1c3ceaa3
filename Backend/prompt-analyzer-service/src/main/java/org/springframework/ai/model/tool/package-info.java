/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * AI Model Tool package for the application.
 *
 * <p>This package contains classes and interfaces related to tools and utilities for AI models
 * within the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>AI model tool utilities and helpers</li>
 *   <li>Support for model tool integration</li>
 *   <li>Facilitation of AI model operations</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>AI model tool classes are used by AI model components and services to provide
 * additional functionality and integration support.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package org.springframework.ai.model.tool;
