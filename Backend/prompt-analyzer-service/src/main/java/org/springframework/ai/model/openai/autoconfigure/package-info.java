/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * OpenAI Model Autoconfigure package for the application.
 *
 * <p>This package contains classes and configuration for automatic setup and integration
 * of OpenAI models within the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Autoconfiguration of OpenAI models</li>
 *   <li>Integration with application context</li>
 *   <li>Support for seamless OpenAI model setup</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Autoconfiguration classes are used by the application to automatically configure
 * and integrate OpenAI models and related features.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package org.springframework.ai.model.openai.autoconfigure;
