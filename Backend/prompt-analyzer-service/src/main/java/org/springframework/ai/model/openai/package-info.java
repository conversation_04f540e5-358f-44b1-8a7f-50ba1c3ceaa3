/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * OpenAI Model package for the application.
 *
 * <p>This package contains classes and interfaces for integrating and configuring
 * OpenAI models within the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>OpenAI model configuration and management</li>
 *   <li>Integration with OpenAI services</li>
 *   <li>Support for model customization and extension</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>OpenAI model classes are used by services and controllers to interact with and manage
 * OpenAI models and related features.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package org.springframework.ai.model.openai;
