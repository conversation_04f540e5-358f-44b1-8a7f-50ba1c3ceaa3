/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * AI Reader package for the application.
 *
 * <p>This package contains classes and interfaces for reading, extracting, and formatting
 * data for AI processing within the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Data extraction and formatting for AI</li>
 *   <li>Support for various data sources</li>
 *   <li>Facilitation of AI data ingestion</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>AI reader classes are used by AI model and service components to ingest and process
 * data for AI-driven features.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package org.springframework.ai.reader;
