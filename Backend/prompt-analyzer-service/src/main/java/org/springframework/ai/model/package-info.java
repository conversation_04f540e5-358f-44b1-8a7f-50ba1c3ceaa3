/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * AI Model package for the application.
 *
 * <p>This package contains classes and interfaces related to AI model configuration,
 * management, and integration within the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>AI model configuration and management</li>
 *   <li>Integration with AI services</li>
 *   <li>Support for model customization and extension</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>AI model classes are used by services and controllers to interact with and manage
 * AI models and related features.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package org.springframework.ai.model;
