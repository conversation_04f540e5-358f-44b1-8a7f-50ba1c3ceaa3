#!/bin/bash

set -e  # Exit immediately on error

source /vault/secrets/secrets.env

# Create SSL directory
mkdir -p /opt/visionwaves/sql_ssl
chmod 700 /opt/visionwaves/sql_ssl

# Extract and save SSL certificates if provided through environment variables
if [[ -n "$MYSQL_KEYSTORE" ]]; then
    echo "$MYSQL_KEYSTORE" | base64 -d > /opt/visionwaves/sql_ssl/keystore.p12 || { echo "Error decoding MYSQL_KEYSTORE"; exit 1; }
    chmod 600 /opt/visionwaves/sql_ssl/keystore.p12
fi

if [[ -n "$MYSQL_TRUSTSTORE" ]]; then
    echo "$MYSQL_TRUSTSTORE" | base64 -d > /opt/visionwaves/sql_ssl/truststore.p12 || { echo "Error decoding MYSQL_TRUSTSTORE"; exit 1; }
    chmod 600 /opt/visionwaves/sql_ssl/truststore.p12
fi

if [[ -n "$MILVUS_SECRET" ]]; then
    echo "$MILVUS_SECRET" | base64 -d > /opt/visionwaves/sql_ssl/milvus_secret.pem || { echo "Error decoding MILVUS_SECRET"; exit 1; }
    chmod 600 /opt/visionwaves/sql_ssl/milvus_secret.pem
fi

if [[ -n "$REDIS_CA_CERT" ]]; then
    echo "$REDIS_CA_CERT" | base64 -d > /opt/visionwaves/sql_ssl/redis_ca.pem || { echo "Error decoding REDIS_CA_CERT"; exit 1; }
    chmod 600 /opt/visionwaves/sql_ssl/redis_ca.pem
fi


JAR="prompt-analyzer-service-1.2.0.jar"
TMP_DIR="./tmp-jar"
MAIN_CLASS="com.enttribe.promptanalyzer.PromptAnalyzerApplication"

# Ensure the JAR file exists
if [[ ! -f "$JAR" ]]; then
    echo "Error: JAR file '$JAR' not found!"
    exit 1
fi

# Cleanup on exit (removes tmp dir)
trap 'rm -rf "$TMP_DIR"' EXIT

# 1. Create a temporary directory and unpack the JAR
mkdir -p "$TMP_DIR"
cd "$TMP_DIR"

if ! jar xf "../$JAR"; then
    echo "Error extracting JAR file!"
    exit 1
fi

rm -rf "../$JAR"

# Ensure BOOT-INF/classes exists
if [[ ! -d "BOOT-INF/classes" ]]; then
    echo "Error: BOOT-INF/classes not found after unpacking!"
    exit 1
fi

# 2. Run the app with unpacked contents
exec java --add-opens java.base/java.lang=ALL-UNNAMED \
     -XX:TieredStopAtLevel=1 -noverify -Xverify:none \
     -XX:MaxMetaspaceSize=512m -Xms1500m -Xmx2000m \
     -XX:MaxDirectMemorySize=512m -Xss512k \
     -XX:+UseG1GC -XX:+UnlockDiagnosticVMOptions -XX:+PrintNMTStatistics \
     -cp "BOOT-INF/classes:BOOT-INF/lib/*" "$MAIN_CLASS"
