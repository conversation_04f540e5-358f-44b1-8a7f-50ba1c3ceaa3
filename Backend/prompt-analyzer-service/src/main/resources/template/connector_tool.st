package com.enttribe.promptanalyzer.function;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Description;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Description("{description}")
public class ConnectorTool implements Function<ConnectorTool.Request, ConnectorTool.Response> \{

    private static final Logger log = LoggerFactory.getLogger(ConnectorTool.class);


    @Override
    public Response apply(Request request) \{
        log.info("executing connector tool");
        String name = "{name}";
        String response;
        try \{
            response = sendHttpRequest(name, request);
        \} catch (JsonProcessingException e) \{
            log.error("error inside ConnectorTool : \{\}", e.getMessage(), e);
            response = "failed";
        \}
        return new Response(response);
    \}

    public String sendHttpRequest(String name, Request request) throws JsonProcessingException \{
        String url = "http://connectx-service/connectx/rest/execution/run";

        RestTemplate restTemplate = new RestTemplate();

        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        String payload = new ObjectMapper().writeValueAsString(request);
        map.put("operationSpecification", payload);

        try \{
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            HttpEntity<Map> requestEntity = new HttpEntity<>(map, headers);
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Map.class);

            log.info("response from the connector : \{\}", response.getBody());
            return (String) response.getBody().toString();
        \} catch (Exception e) \{
            log.error("Error in sending HTTP request: \{\}", e.getMessage(), e);
            return "Error: " + e.getMessage();
        \}
    \}

    public record Request(
            {requestFields}
    ) \{
    \}

    public record Response(String response) \{
    \}


\}
