package com.enttribe.promptanalyzer.function;
 
import org.springframework.ai.tool.annotation.ToolParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.Description;
import java.util.*;
import java.util.function.Function;
import org.springframework.ai.chat.model.ToolContext;
import java.util.function.BiFunction;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import org.json.JSONObject;
import java.lang.reflect.Method;
 

@Description("{description}")
public class {toolName} implements BiFunction<{toolName}.Request, ToolContext, {toolName}.Response> \{
 
    private static final Logger log = LogManager.getLogger({toolName}.class);
    private static final String ENTITY_NOT_FOUND = "ENTITY_NOT_FOUND";
    private static final String ENTITY_TYPE = "{entityName}";
    private static final String INTENT_TYPE = "{intentName}";
    private static final String USER_CONTEXT_CLASS = "com.enttribe.conversationalai.utils.UserContext";
    private static final String HTTP_UTILS_CLASS = "com.enttribe.conversationalai.util.HttpUtils";
    private static final String UTILS_CLASS = "com.enttribe.conversationalai.utils.Utils";
 
    private String authToken;
    private String baseUrl;
 
    /**
     * Request record containing user question and entity ID
     */
    public record Request(
        @ToolParam(
            description = "The complete and grammatically correct user question with all pronouns and references resolved. " +
                         "Resolve all pronouns and references (e.g., he, she, it, him, them) to the actual entity/person " +
                         "mentioned earlier in the conversation."
        )
        String userQuestion,
        String entityId
    ) \{\}
 
    /**
     * Response record containing the operation result
     */
    public record Response(String response) \{\}
 
    /**
     * Processes \{entityName\} \{intentName\} requests and returns appropriate response
     * @param request The \{entityName\} \{intentName\} request containing user question and entity ID
     * @param toolContext The context containing authentication and configuration details
     * @return Response containing the result of the \{intentName\} operation
     */
    @Override
    public Response apply(Request request, ToolContext toolContext) \{
        log.info("Processing \{entityName\} \{intentName\} request: \{\}", request);
        initializeContext(toolContext);
 
        // Handle test mode
        if (isTestMode(toolContext)) \{
            return createTestResponse();
        \}
 
        String uuid = (String) toolContext.getContext().get("UUID");
        String chatId = generateChatId(uuid);
        String questionId = generateQuestionId(uuid);
        String entity = (String) toolContext.getContext().get("entity");
 
        // Process X101 response
        String x101Response = processX101Request(request, chatId, questionId);
        JSONObject returnResponse = createResponseObject(x101Response);
 
        // Send response to socket
        sendMessageToSocket(x101Response, uuid, questionId);
 
        return new Response(returnResponse.toString());
    \}
 
    /**
     * Initializes context with authentication and base URL
     */
    private void initializeContext(ToolContext toolContext) \{
        authToken = new String(Base64.getDecoder().decode((String) toolContext.getContext().get("token")));
        baseUrl = (String) toolContext.getContext().get("baseUrl");
    \}
 
    /**
     * Checks if system is running in test mode
     */
    private boolean isTestMode(ToolContext toolContext) \{
        String isTesting = (String) toolContext.getContext().get("TESTING");
        return isTesting != null && isTesting.equalsIgnoreCase("true");
    \}
 
    /**
     * Creates test mode response
     */
    private Response createTestResponse() \{
        JSONObject jsonInput = new JSONObject();
        jsonInput.put("entity", ENTITY_TYPE);
        jsonInput.put("intent", INTENT_TYPE);
        return new Response(jsonInput.toString());
    \}
 
    /**
     * Generates chat ID using reflection
     */
    private String generateChatId(String uuid) \{
        try \{
            Class<?> clazz = Class.forName(USER_CONTEXT_CLASS);
            Method getChatId = clazz.getMethod("getChatId", String.class);
            return (String) getChatId.invoke(null, uuid);
        \} catch (Exception e) \{
            log.error("Error generating chat ID: \{\}", e.getMessage());
            return "a_" + System.currentTimeMillis();
        \}
    \}
 
    /**
     * Generates question ID using reflection
     */
    private String generateQuestionId(String uuid) \{
        try \{
            Class<?> clazz = Class.forName(USER_CONTEXT_CLASS);
            Method getQuestionId = clazz.getMethod("getQuestionId", String.class);
            return (String) getQuestionId.invoke(null, uuid);
        \} catch (Exception e) \{
            log.error("Error generating question ID: \{\}", e.getMessage());
            return "a_" + System.currentTimeMillis() + "_" + System.currentTimeMillis();
        \}
    \}
 
    /**
     * Processes X101 request using reflection
     */
    private String processX101Request(Request request, String chatId, String questionId) \{
        try \{
            Class<?> clazz = Class.forName(HTTP_UTILS_CLASS);
            Method getX101Response = clazz.getMethod("getX101Response", String.class, String.class, String.class, 
                                                    String.class, String.class, String.class, String.class);
            String question = formatQuestion(request);
            return (String) getX101Response.invoke(null, question, chatId, questionId, 
                                                 ENTITY_TYPE, INTENT_TYPE, authToken, baseUrl);
        \} catch (Exception e) \{
            log.error("Error processing X101 request: \{\}", e.getMessage());
            return null;
        \}
    \}
 
    /**
     * Formats the question with entity ID if available
     */
    private String formatQuestion(Request request) \{
        return (request.entityId != null && !request.entityId.isBlank()) 
               ? request.userQuestion + " (EntityId : " + request.entityId + ")" 
               : request.userQuestion;
    \}
 
    /**
     * Creates response object based on X101 response
     */
    private JSONObject createResponseObject(String x101Response) \{
        JSONObject returnResponse = new JSONObject();
        try \{
            if (x101Response == null) \{
                returnResponse.put("message", getRandomErrorMessage());
            \} else \{
                JSONObject x101Json = new JSONObject(x101Response);
                String response = x101Json.getString("message");
                response = response.equalsIgnoreCase(ENTITY_NOT_FOUND) 
                          ? "Couldn't find what you're looking for. Double-check or contact support." 
                          : response;
                returnResponse.put("message", response.length() > 2 ? response : "Here is the information");
            \}
        \} catch (Exception e) \{
            log.error("Error creating response object: \{\}", e.getMessage());
            returnResponse.put("message", "I'm unable to process the request at this time.");
        \}
        return returnResponse;
    \}
 
    /**
     * Returns a random error message for better user experience
     */
    private String getRandomErrorMessage() \{
        String[] errorMessages = \{
            "Hmm... something's not right. Try again in a bit!",
            "Oops! Looks like there's a hiccup. Give it another shot later.",
            "We're facing some issues right now. Please try again soon!",
            "Uh-oh, something went wrong! Maybe wait a moment and try again?",
            "Yikes! That didn't work. Give it another go in a little while!"
        \};
        return errorMessages[new Random().nextInt(errorMessages.length)];
    \}
 
    /**
     * Sends message to socket using reflection
     */
    private void sendMessageToSocket(String response, String uuid, String questionId) \{
        try \{
            log.debug("Sending message to socket for UUID: \{\}", uuid);
            JSONObject jsonObject = new JSONObject(response);
            Class<?> clazz = Class.forName(UTILS_CLASS);
            Method method = clazz.getDeclaredMethod("setX101Response", String.class, String.class);
            method.setAccessible(true);
            method.invoke(null, questionId, jsonObject.toString());
        \} catch (Exception e) \{
            log.error("Error sending message to socket: \{\}", e.getMessage());
        \}
    \}
\} 