{"4463": "{\"name\": \"OnNewEmail\",\"description\": \"Triggered when an email is received.\",\"package\": \"com.enttribe.custom.trigger.OnNewEmail\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"email id of user to look for new mail\",\"required\":true}]}", "4460": "{\"name\": \"OnTicketAssign\",\"description\": \"Triggered when a ticket is assigned to someone.\",\"package\": \"com.enttribe.custom.trigger.OnTicketAssign\",\"properties\":[{\"parameterName\":\"Assignee\",\"parameterDescription\":\"Assignee of the ticket\",\"required\":true}]}", "4490": "{\"name\": \"OnProjectDelay\",\"description\": \"Triggered when a project delivery exceeds the specified end date.\",\"package\": \"com.enttribe.custom.trigger.OnProjectDelay\",\"properties\" : [{\"parameterName\":\"ACCESS TOKEN\",\"parameterDescription\":\"The API access token\",\"required\":true}]}", "4992": "{ \"name\": \"OnTicketDelay\", \"description\":\"Triggered when a ticket remains open for more than 3 days.\", \"package\":\"com.enttribe.custom.trigger.OnTicketDelay\" }", "4997": "{\"name\": \"OnTaskDelay\",\"description\":\"Triggered when a task remains incomplete beyond its due date.\",\"package\":\"com.enttribe.custom.trigger.OnTaskDelay\"}", "5000": "{\"name\": \"OnTicketResolve\",\"description\":\"Triggered when a ticket is marked as resolved.\",\"package\":\"com.enttribe.custom.trigger.OnTicketResolve\"}", "4450": "{\"name\": \"TicketCreate\",\"description\": \"Creates a ticket in system based on provided details\",\"package\":\"com.enttribe.custom.action.TicketCreate\"}", "4451": "{\"name\": \"LeadCreate\",\"description\": \"Creates a crm lead based on provided details\",\"description\": \"Create a Lead\",\"package\":\"com.enttribe.custom.action.LeadCreate\"}", "4454": "{\"name\": \"InvokeVoiceAgent\",\"description\": \"Invokes a phone call to user mobile number\",\"package\":\"com.enttribe.custom.action.InvokeVoiceAgent\",\"properties\" : [{\"parameterName\":\"Phone Number\",\"parameterDescription\":\"The phone number to call\",\"required\":true}]}", "4453": "{\"name\": \"InvokeAgent\",\"description\": \"Invokes a agent based on its name. This AI Agent is capable of scheduling event/meeting, send email etc\",\"package\":\"com.enttribe.custom.action.InvokeAgent\",\"properties\" : [{\"parameterName\":\"Agent Name\",\"parameterDescription\":\"The name of the agent to invoke\",\"required\":true}]}", "4461": "{\"name\": \"OnTaskCreate\",\"description\": \"The OnTaskCreate processor listens for new tasks being created in a system. It works like a server that waits for task creation events, processes details like task name, project, and priority, and then passes this information for further actions. It supports both HTTP and HTTPS (with security features like SSL) and helps in automating workflows based on new tasks.\",\"package\":\"com.enttribe.custom.trigger.OnTaskCreate\"}", "4462": "{\"name\": \"OnTaskAssign\",\"description\": \"The OnTaskAssign processor is triggered whenever a task is assigned to a trainee. It helps automate actions like notifications, tracking, and workflow updates based on task assignments.\",\"package\":\"com.enttribe.custom.trigger.OnTaskAssign\",\"properties\" : [{\"parameterName\":\"Assignee\",\"parameterDescription\":\"Assignee of the task\",\"required\":true}]}", "4465": "{\"name\": \"OnTicketCreate\",\"package\":\"com.enttribe.custom.trigger.OnTicketCreate\",\"description\": \"The OnTicketCreate processor is triggered whenever a new ticket is created. It helps automate actions like sending notifications, updating records, or assigning tasks whenever a ticket is generated.\"}", "4960": "{\"name\": \"TicketAssign\",\"package\":\"com.enttribe.custom.action.TicketAssign\",\"description\": \"Assign a ticket to a user\"}", "4970": "{\"name\": \"OutlookSendMail\",\"package\":\"com.enttribe.custom.action.OutlookSendMail\",\"description\": \"Sends emails using Microsoft Graph API for Outlook\",\"properties\" : [{\"parameterName\":\"From Address\",\"parameterDescription\":\"Email address to send from (must have appropriate permissions\",\"required\":true},{\"parameterName\":\"To Addresses\",\"parameterDescription\":\"Comma-separated list of recipient email addresses\",\"required\":true},{\"parameterName\":\"Email Subject\",\"parameterDescription\":\"Subject of the email\",\"required\":true},{\"parameterName\":\"Email Body\",\"parameterDescription\":\"Content of the email body\",\"required\":true},{\"parameterName\":\"Enable Attachment\",\"parameterDescription\":\"Enable/disable attachment processing from FlowFile\",\"required\":true}]}", "6124": "{\"name\":\"OnTicketUpdate\",\"package\":\"com.enttribe.custom.trigger.OnTicketUpdate\",\"description\":\"The OnTicketUpdate NiFi processor listens for HTTP requests when tickets are updated, filtering updates based on configurable properties like priority, severity, and assignee. It converts incoming requests into NiFi FlowFiles, supports SSL/TLS, provides a health check endpoint, and ensures proper session management, running only on the primary node in a clustered environment.\",\"properties\":[{\"parameterName\":\"Priority\",\"parameterDescription\":\"Priority of the flow\",\"required\":true},{\"parameterName\":\"Severity\",\"parameterDescription\":\"Severity of the flow\",\"required\":true},{\"parameterName\":\"Title\",\"parameterDescription\":\"Title of the flow\",\"required\":true},{\"parameterName\":\"Category\",\"parameterDescription\":\"Category of the flow\",\"required\":true}]}", "4470": "{\"name\":\"GoogleSearch\",\"package\":\"com.enttribe.custom.processor.GoogleSearch\",\"description\":\"The GoogleSearch NiFi processor performs a Google search using the Google Search API. It extracts a summary from the first search result and adds it as a FlowFile attribute.\",\"properties\":[{\"parameterName\":\"User question\",\"parameterDescription\":\"The search term for Google\",\"required\":true}]}", "4985": "{\"name\":\"OutlookFlagMail\",\"package\":\"com.enttribe.custom.action.OutlookFlagMail\",\"description\":\"Flags or unflags email messages based on specified email address and message ID\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email of the user whose calendar the event should be created in\",\"required\":true}]}", "5039": "{\"name\":\"OutlookDraftMailReply\",\"package\":\"com.enttribe.custom.action.OutlookDraftMailReply\",\"description\":\"Creates a draft reply email for an existing message with specified content\",\"properties\":[{\"parameterName\":\"Subject\",\"parameterDescription\":\"Email subject for creating a draft email\",\"required\":true}]}", "4986": "{\"name\":\"OutlookAcceptMeeting\",\"package\":\"com.enttribe.custom.action.OutlookAcceptMeeting\",\"description\":\"Accepts a calendar meeting invitation for a specified email address and event ID\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}", "4987": "{\"name\":\"OutlookDeclineMeeting\",\"package\":\"com.enttribe.custom.action.OutlookDeclineMeeting\",\"description\":\"Declines a calendar meeting invitation for a specified email address and event ID\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}", "5038": "{\"name\":\"OutlookDraftMail\",\"package\":\"com.enttribe.custom.action.OutlookDraftMail\",\"description\":\"Creates a draft email with specified recipient and content\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}", "4983": "{\"name\":\"OutlookReadEvent\",\"package\":\"com.enttribe.custom.trigger.OutlookReadEvent\",\"description\":\"The OutlookReadEvent processor reads event from Microsoft Outlook using Graph API with optional subject and body from a specified Outlook user’s calendar.\",\"properties\":[{\"parameterName\":\"User Email\",\"parameterDescription\":\"Email address of user whose event should be created \",\"required\":true}]}"}