# Use an Alpine-based image (must include glibc if DJL native libs require it)
FROM registry.visionwaves.com/alpine-fixed:3.20.3

# Install dependencies using apk
RUN apk add --no-cache \
    curl \
    vim \
    unzip \
    font-dejavu \
    bash \
    tzdata \
    libc6-compat

# Set environment variables
ENV LANG=en_US.UTF-8 \
    LANGUAGE=en_US:en \
    LC_ALL=en_US.UTF-8 \
    SSL_VAULT_PATH=/opt/visionwaves/sql_ssl \
    SERVICE_ARCHIVE=prompt-analyzer \
    SERVICE_PATH=/opt/visionwaves/prompt-analyzer \
    BASE_PATH=/opt/visionwaves/prompt-analyzer \
    TZ=UTC

# Set timezone
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create non-root user and group with specific UID and GID
RUN addgroup -g 1001 -S visionwaves && \
    adduser -u 1001 -S -G visionwaves -h /home/<USER>/bin/bash visionwaves && \
    mkdir -p /home/<USER>
    chown -R 1001:1001 /home/<USER>

# Create necessary directories
RUN mkdir -p "$BASE_PATH" "$SSL_VAULT_PATH" && \
    chown -R visionwaves:visionwaves "$BASE_PATH" "$SSL_VAULT_PATH"

# Copy and extract the service archive
COPY ./prompt-analyzer.tar $BASE_PATH/
RUN tar -xf "$BASE_PATH/$SERVICE_ARCHIVE.tar" -C "$BASE_PATH" && \
    rm "$BASE_PATH/$SERVICE_ARCHIVE.tar"

# Remove embedded Dockerfile from service archive (optional)
RUN rm -rf $SERVICE_PATH/Dockerfile

# Switch to non-root user
USER visionwaves

# Set working directory
WORKDIR $SERVICE_PATH

# Start the application
CMD ["bash", "run.sh", "start"]
