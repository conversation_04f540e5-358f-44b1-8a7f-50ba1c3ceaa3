--10 December
ALTER TABLE PROMPT ADD CONSTRAINT unique_application_category_name_status_version UNIQUE (APPLICATION, CATEGORY, NAME, STATUS, VERSION);
ALTER TABLE LLM_MODEL
ADD CONSTRAINT unique_inference_model_provider UNIQUE (INFERENCE, MODEL, PROVIDER);
ALTER TABLE LLM_MODEL
ADD COLUMN BASE_URL VARCHAR(255),
ADD COLUMN API_KEY VARCHAR(255);

--13 dec 2024
ALTER TABLE PROMPT_AUDIT ADD COLUMN META_TAGS VARCHAR(255);
ALTER TABLE PROMPT_AUDIT ADD COLUMN CHAT_OPTIONS VARCHAR(255);
ALTER TABLE PROMPT_AUDIT ADD COLUMN FUNCTION_ARGS VARCHAR(255);
ALTER TABLE PROMPT_AUDIT ADD COLUMN AUDIT_ID VARCHAR(255);

ALTER TABLE EXCEPTION_AUDIT ADD COLUMN AUDIT_ID VARCHAR(255);

ALTER TABLE LLM_MODEL ADD COLUMN TYPE VARCHAR(255);
ALTER TABLE LLM_MODEL ADD COLUMN LANGUAGE VARCHAR(255);
ALTER TABLE LLM_MODEL ADD COLUMN VOICE VARCHAR(255);
ALTER TABLE LLM_MODEL ADD COLUMN STATUS VARCHAR(255);

ALTER TABLE TOOL ADD CONSTRAINT unique_application_category_name_version UNIQUE (APPLICATION_NAME, CATEGORY, TOOL_NAME, VERSION);

ALTER TABLE TOOL ADD COLUMN TOOL_JSON text;
ALTER TABLE TOOL ADD COLUMN TOOL_AUTHENTICATION text;
ALTER TABLE TOOL ADD COLUMN TAGS VARCHAR(255);
ALTER TABLE TOOL ADD COLUMN STATUS ENUM('DRAFT', 'PUBLISH', 'ARCHIVE');
ALTER TABLE TOOL DROP COLUMN FUNCTION_NAME;

--23 December
ALTER TABLE TOOL ADD COLUMN `AUTH_TYPE` varchar(255) DEFAULT NULL;
ALTER TABLE TOOL ADD COLUMN `AUTH_VALUE` varchar(255) DEFAULT NULL;
ALTER TABLE TOOL ADD COLUMN `AUTH_KEY` varchar(255) DEFAULT NULL;
ALTER TABLE TOOL ADD COLUMN `TOOL_IMAGE` text DEFAULT NULL;

--26 December
ALTER TABLE PROMPT_AUDIT ADD COLUMN PROMPT_NAME VARCHAR(150),ADD COLUMN CREATION_TIME DATETIME, ADD COLUMN HTTP_STATUS VARCHAR(150), ADD COLUMN REQUEST_TEXT LONGTEXT, ADD COLUMN STATUS VARCHAR(50);

--27 December
ALTER TABLE PROMPT_AUDIT ADD INDEX idx_audit_id (AUDIT_ID);
ALTER TABLE PROMPT ADD CONSTRAINT UNIQUE_PROMPT_ID UNIQUE (PROMPT_ID);

--2 January
UPDATE LLM_MODEL SET TYPE = 'chat';
INSERT INTO LLM_MODEL (API_KEY, BASE_URL, INFERENCE, MODEL, PROVIDER, TYPE) VALUES ('********************************************************', 'https://api.groq.com/openai/', 'groq' , 'nomic-embed-text-v1_5-preview1', 'groq', 'embedding');
ALTER TABLE PROMPT ADD COLUMN `JSON_MODE` bit(1) DEFAULT NULL;
UPDATE PROMPT SET JSON_MODE = 0;

--7 January
ALTER TABLE TOOL ADD COLUMN AGENT_TOOL_ID INT DEFAULT NULL, ADD COLUMN VOICE_AGENT_ID INT DEFAULT NULL;

ALTER TABLE TOOL
ADD UNIQUE KEY `UK3uwf9ml4ycb5cp3yl6qvfn1g8` (`AGENT_TOOL_ID`),
ADD UNIQUE KEY `UK43iojurhl6dwou8lm376bunvc` (`VOICE_AGENT_ID`),
ADD CONSTRAINT `FK98ioqxtrybiiq9lipfq1nh7sb` FOREIGN KEY (`VOICE_AGENT_ID`) REFERENCES `VOICE_AGENT` (`ID`),
ADD CONSTRAINT `FKh20oi2k526wwqh27dsqk62mai` FOREIGN KEY (`AGENT_TOOL_ID`) REFERENCES `AGENT` (`ID`);

ALTER TABLE TOOL ADD CONSTRAINT UNIQUE_TOOL_NAME UNIQUE (TOOL_NAME);
ALTER TABLE TOOL ADD COLUMN AGENT_ID BIGINT DEFAULT NULL;

Alter TABLE PROMPT Add column LLM_GUARD BOOLEAN DEFAULT false;

--8 January

CREATE TABLE `TEXT_TO_SPEECH` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `GENDER` enum('MALE','FEMALE') DEFAULT NULL,
  `NAME` enum('GOOGLE','ON_PREMISE','VOICE','WEB_CALL') NOT NULL,
  `VOICE_NAME` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

CREATE TABLE `SPEECH_TO_TEXT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `LANGUAGE` varchar(255) DEFAULT NULL,
  `SERVICE` enum('GOOGLE','ON_PREMISE','VOICE','WEB_CALL') DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

CREATE TABLE `VOICE_AGENT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `STT_ID` int DEFAULT NULL,
  `TTS_ID` int DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `ICON` text,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `NAME` varchar(255) DEFAULT NULL,
  `PURPOSE` varchar(255) DEFAULT NULL,
  `STATUS` enum('ACTIVE','DEACTIVE','DRAFT','PUBLISH') DEFAULT NULL,
  `VARIABLES` text,
  `VAR_BYTE_CODE_MAP` longtext,
  `LLM_MODEL_ID` int DEFAULT NULL,
  `PROMPT_ID` int DEFAULT NULL,
  `VOICE_AGENT_TOOL` int NOT NULL,
  `TOOL_ID` int NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UKrlxakcyomt1sj5k0ucneeu20b` (`LLM_MODEL_ID`),
  UNIQUE KEY `UK4tabxt548c8kvl6mslaxcvxi1` (`PROMPT_ID`),
  KEY `FK35wmo6uoher6nq3sgvb6icyfo` (`STT_ID`),
  KEY `FKks3018i5j4onqn3mli2nxkhhm` (`TTS_ID`),
  KEY `FK20ex567k6o8d3pa9a2fgrhrj1` (`TOOL_ID`),
  KEY `FKsi20gfvuju8u7fx4fue8aqgn` (`VOICE_AGENT_TOOL`),
  CONSTRAINT `FK20ex567k6o8d3pa9a2fgrhrj1` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`),
  CONSTRAINT `FK35wmo6uoher6nq3sgvb6icyfo` FOREIGN KEY (`STT_ID`) REFERENCES `SPEECH_TO_TEXT` (`ID`),
  CONSTRAINT `FKeeurovuawskblpw6bt4bb76ii` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`),
  CONSTRAINT `FKks3018i5j4onqn3mli2nxkhhm` FOREIGN KEY (`TTS_ID`) REFERENCES `TEXT_TO_SPEECH` (`ID`),
  CONSTRAINT `FKqovdico1tqbsm60ra3duxk6ia` FOREIGN KEY (`LLM_MODEL_ID`) REFERENCES `LLM_MODEL` (`ID`),
  CONSTRAINT `FKsi20gfvuju8u7fx4fue8aqgn` FOREIGN KEY (`VOICE_AGENT_TOOL`) REFERENCES `VOICE_AGENT` (`ID`)
);

 CREATE TABLE `AGENT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `ICON` text,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `NAME` varchar(255) DEFAULT NULL,
  `PURPOSE` varchar(255) DEFAULT NULL,
  `STATUS` enum('ACTIVE','DEACTIVE','DRAFT','PUBLISH') DEFAULT NULL,
  `VARIABLES` text,
  `VAR_BYTE_CODE_MAP` longtext,
  `LLM_MODEL_ID` int DEFAULT NULL,
  `PROMPT_ID` int DEFAULT NULL,
  `AGENT_TOOL_ID` int NOT NULL,
  `TOOL_ID` int NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UKia9w87xglajqq0a2i65ge8uxh` (`LLM_MODEL_ID`),
  UNIQUE KEY `UK7at4uo6edvvasufccab2s4255` (`PROMPT_ID`),
  KEY `FKoph9v3ds8otxmathwiup0egua` (`TOOL_ID`),
  KEY `FKgklog5ee7fudx7hy66q0f24vm` (`AGENT_TOOL_ID`),
  CONSTRAINT `FK4sing926c1ulpl6yx1nb2nd0d` FOREIGN KEY (`LLM_MODEL_ID`) REFERENCES `LLM_MODEL` (`ID`),
  CONSTRAINT `FK8ymevynempvujq8g8gnl3ocg4` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`),
  CONSTRAINT `FKgklog5ee7fudx7hy66q0f24vm` FOREIGN KEY (`AGENT_TOOL_ID`) REFERENCES `AGENT` (`ID`),
  CONSTRAINT `FKoph9v3ds8otxmathwiup0egua` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`)
);

CREATE TABLE `VOICE_AGENT_TOOL` (
  `VOICE_AGENT_TOOL` int NOT NULL,
  `TOOL_ID` int NOT NULL,
  PRIMARY KEY (`VOICE_AGENT_TOOL`,`TOOL_ID`),
  KEY `FKt4fue43dalpwo5j1co77ot9t0` (`TOOL_ID`),
  CONSTRAINT `FKmtjj76ga17j035d6yqvqbd8r1` FOREIGN KEY (`VOICE_AGENT_TOOL`) REFERENCES `VOICE_AGENT` (`ID`),
  CONSTRAINT `FKt4fue43dalpwo5j1co77ot9t0` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`)
);

CREATE TABLE `AGENT_TOOL` (
  `AGENT_TOOL_ID` int NOT NULL,
  `TOOL_ID` int NOT NULL,
  PRIMARY KEY (`AGENT_TOOL_ID`,`TOOL_ID`),
  KEY `FK5qdfjv2dofpryk9j02942ukxf` (`TOOL_ID`),
  CONSTRAINT `FK5qdfjv2dofpryk9j02942ukxf` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`),
  CONSTRAINT `FKhuivkoct9id5f1ndwnp92ixsg` FOREIGN KEY (`AGENT_TOOL_ID`) REFERENCES `AGENT` (`ID`)
);

--- 10 January
ALTER TABLE TOOL MODIFY COLUMN `STATUS` ENUM('DRAFT', 'PUBLISH', 'ARCHIVE', 'ACTIVE', 'DEACTIVE') DEFAULT NULL;
ALTER TABLE AGENT ADD COLUMN PROMPT_ID INT DEFAULT NULL;

--13 January
CREATE TABLE `KNOWLEDGE_BASE` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `NAME` varchar(255) DEFAULT NULL,
  `TYPE` enum('SQL','API','DOCUMENT','CODE') NOT NULL,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `API_AUTH_TYPE` varchar(255) DEFAULT NULL,
  `API_AUTH_VALUE` varchar(255) DEFAULT NULL,
  `API_ENDPOINT` varchar(255) DEFAULT NULL,
  `API_TYPE` varchar(255) DEFAULT NULL,
  `DB_NAME` varchar(255) DEFAULT NULL,
  `DB_PASSWORD` varchar(255) DEFAULT NULL,
  `DB_USERNAME` varchar(255) DEFAULT NULL,
  `DOC_ID` varchar(255) DEFAULT NULL,
  `DOC_META_DATA` varchar(255) DEFAULT NULL,
  `DOC_TYPE` varchar(255) DEFAULT NULL,
  `DOCUMENT_VECTOR` longtext,
  `IS_CONTEXT` bit(1) DEFAULT NULL,
  `SIMILARITY_THRESHOLD` varchar(255) DEFAULT NULL,
  `SOURCE_CODE` text,
  `CLASS_NAME` varchar(255) DEFAULT NULL,
  `BYTE_CODE_MAP` longtext,
  `TOP_K` varchar(255) DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

--14 January
ALTER TABLE PROMPT ADD COLUMN `TYPE` enum('CONVERSATION','NORMAL') DEFAULT NULL;
update PROMPT SET TYPE = 'NORMAL';
INSERT INTO LLM_MODEL (API_KEY, BASE_URL, INFERENCE, MODEL, PROVIDER, TYPE) VALUES ('xdv6hDyIRvQapMeJ8WjKkoYA9787nkqdXq358jkh37iUlVnHhrHeBP10l3Fa7hwA3PJjlaZZeDXNe5W6DCXII0Sgkw78dDq7gzbM6vjuKgFNVNjWAiSPsYaYOMAGAm56', 'https://vllm-34b-alpha-19-3-21.allam.ai/', 'allam-34b' , 'allam', 'allam-34b', 'chat');
INSERT INTO LLM_MODEL (API_KEY, BASE_URL, INFERENCE, MODEL, PROVIDER, TYPE) VALUES ('89b25e5dfdf84c1696c1de4c72a42362', 'https://vwaves-south-gpt3.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview', 'azure' , 'gpt-4o', 'azure', 'chat');
INSERT INTO LLM_MODEL (API_KEY, BASE_URL, INFERENCE, MODEL, PROVIDER, TYPE) VALUES ('xdv6hDyIRvQapMeJ8WjKkoYA9787nkqdXq358jkh37iUlVnHhrHeBP10l3Fa7hwA3PJjlaZZeDXNe5W6DCXII0Sgkw78dDq7gzbM6vjuKgFNVNjWAiSPsYaYOMAGAm56', 'https://vllm-13b-v2-19-0-20.allam.ai/', 'allam-13b' , 'allam', 'allam-13b', 'chat');

---15 January
ALTER TABLE KNOWLEDGE_BASE MODIFY COLUMN TOP_K INT DEFAULT NULL;
ALTER TABLE KNOWLEDGE_BASE MODIFY COLUMN SIMILARITY_THRESHOLD DOUBLE DEFAULT NULL;

ALTER TABLE KNOWLEDGE_BASE ADD COLUMN VECTOR_META_DATA text DEFAULT NULL;
ALTER TABLE KNOWLEDGE_BASE DROP COLUMN DOCUMENT_VECTOR;
ALTER TABLE KNOWLEDGE_BASE ADD COLUMN DELETED BOOLEAN DEFAULT FALSE;
ALTER TABLE KNOWLEDGE_BASE ADD COLUMN FILE_NAME VARCHAR(150) DEFAULT NULL;

--- 23 january
CREATE TABLE `TICKET_TRIGGER` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `TRIGGER_META_DATA` longtext,
  `NAME` varchar(255) DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `DELETED` bit(1) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

---- 27 January
ALTER TABLE AGENT MODIFY COLUMN NAME VARCHAR(255) UNIQUE;

-- 28 january
CREATE TABLE `QUERY` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `QUESTION` longtext,
  `TYPE` varchar(50) DEFAULT NULL,
  `USER_ID` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

--- 29 january
ALTER TABLE QUERY ADD COLUMN DELETED BOOLEAN DEFAULT FALSE;




