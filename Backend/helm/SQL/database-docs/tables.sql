CREATE TABLE `AGENT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `ICON` text,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `NAME` varchar(255) DEFAULT NULL,
  `PURPOSE` varchar(255) DEFAULT NULL,
  `STATUS` enum('ACTIVE','DEACTIVE','DRAFT','PUBLISH') DEFAULT NULL,
  `VARIABLES` text,
  `VAR_BYTE_CODE_MAP` longtext,
  `LLM_MODEL_ID` int DEFAULT NULL,
  `PROMPT_ID` int DEFAULT NULL,
  `CUSTOMER_ID` int DEFAULT NULL,
  `CREATOR` int DEFAULT NULL,
  `LAST_MODIFIER` int DEFAULT NULL,
  <PERSON><PERSON><PERSON><PERSON> (`ID`),
  UNIQUE KEY `NAME` (`NAME`,`CUSTOMER_ID`),
  KEY `FKf89bw2f5hii7bu4ep3twgaih3` (`CREATOR`),
  KEY `FK1d5ma109bp31gr0j2xjvrr6ub` (`LAST_MODIFIER`),
  KEY `FK4sing926c1ulpl6yx1nb2nd0d` (`LLM_MODEL_ID`),
  KEY `FK8ymevynempvujq8g8gnl3ocg4` (`PROMPT_ID`),
  CONSTRAINT `FK4sing926c1ulpl6yx1nb2nd0d` FOREIGN KEY (`LLM_MODEL_ID`) REFERENCES `LLM_MODEL` (`ID`),
  CONSTRAINT `FK8ymevynempvujq8g8gnl3ocg4` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`)
);

CREATE TABLE `AGENT_TOOL` (
  `AGENT_TOOL_ID` int NOT NULL,
  `TOOL_ID` int NOT NULL,
  PRIMARY KEY (`AGENT_TOOL_ID`,`TOOL_ID`),
  KEY `FK5qdfjv2dofpryk9j02942ukxf` (`TOOL_ID`),
  CONSTRAINT `FK5qdfjv2dofpryk9j02942ukxf` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`),
  CONSTRAINT `FKhuivkoct9id5f1ndwnp92ixsg` FOREIGN KEY (`AGENT_TOOL_ID`) REFERENCES `AGENT` (`ID`)
);

CREATE TABLE `EXCEPTION_AUDIT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` varchar(255) DEFAULT NULL,
  `CATEGORY` varchar(255) DEFAULT NULL,
  `EXCEPTION_MESSAGE` varchar(2500) DEFAULT NULL,
  `EXCEPTION_TRACE` longtext,
  `IDENTIFIER` text,
  `METHOD_NAME` varchar(255) DEFAULT NULL,
  `METHOD_PARAMETERS` text,
  `PROMPT_ID` varchar(255) DEFAULT NULL,
  `TIMESTAMP` datetime(6) DEFAULT NULL,
  `AUDIT_ID` varchar(400) DEFAULT NULL,
  `CUSTOMER_ID` int DEFAULT NULL,
  `CREATOR` int DEFAULT NULL,
  `LAST_MODIFIER` int DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `FKf89bw2f5hii7bu4ep3twgaih3` (`CREATOR`),
  KEY `FK1d5ma109bp31gr0j2xjvrr6ub` (`LAST_MODIFIER`)
);

CREATE TABLE `KNOWLEDGE_BASE` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `NAME` varchar(255) DEFAULT NULL,
  `TYPE` enum('SQL','API','DOCUMENT','CODE','WEBSITE','COLLECTION') DEFAULT NULL,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `API_AUTH_TYPE` varchar(255) DEFAULT NULL,
  `API_AUTH_VALUE` varchar(255) DEFAULT NULL,
  `API_ENDPOINT` varchar(255) DEFAULT NULL,
  `API_TYPE` varchar(255) DEFAULT NULL,
  `DB_NAME` varchar(255) DEFAULT NULL,
  `DB_PASSWORD` varchar(255) DEFAULT NULL,
  `DB_USERNAME` varchar(255) DEFAULT NULL,
  `DOC_ID` varchar(2500) DEFAULT NULL,
  `DOC_META_DATA` varchar(255) DEFAULT NULL,
  `DOC_TYPE` varchar(255) DEFAULT NULL,
  `IS_CONTEXT` bit(1) DEFAULT NULL,
  `SIMILARITY_THRESHOLD` double DEFAULT NULL,
  `SOURCE_CODE` text,
  `CLASS_NAME` varchar(255) DEFAULT NULL,
  `BYTE_CODE_MAP` longtext,
  `TOP_K` int DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `VECTOR_META_DATA` text,
  `DELETED` tinyint(1) DEFAULT '0',
  `FILE_NAME` varchar(150) DEFAULT NULL,
  `FILTER` varchar(255) DEFAULT NULL,
  `WEBSITE_URL` varchar(500) DEFAULT NULL,
  `WEBSITE_TASK_STATUS` varchar(20) DEFAULT NULL,
  `COLLECTION_NAME` varchar(40) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

CREATE TABLE `LLM_MODEL` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `API_KEY` varchar(255) DEFAULT NULL,
  `BASE_URL` varchar(255) DEFAULT NULL,
  `INFERENCE` varchar(255) DEFAULT NULL,
  `MODEL` varchar(255) DEFAULT NULL,
  `PROVIDER` varchar(255) DEFAULT NULL,
  `LANGUAGE` varchar(255) DEFAULT NULL,
  `STATUS` varchar(255) DEFAULT NULL,
  `TYPE` varchar(255) DEFAULT NULL,
  `VOICE` varchar(255) DEFAULT NULL,
  `INPUT_COST` double DEFAULT '0',
  `OUTPUT_COST` double DEFAULT '0',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_inference_model_provider` (`INFERENCE`,`MODEL`,`PROVIDER`)
);

CREATE TABLE `MESSAGES` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `CONTENT` longtext,
  `ROLE` varchar(255) DEFAULT NULL,
  `PROMPT_ID` int DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `FKjnpi8tsn50ddp7vacf9bkmgcw` (`PROMPT_ID`),
  CONSTRAINT `FKjnpi8tsn50ddp7vacf9bkmgcw` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`)
);

CREATE TABLE `PROMPT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `APPLICATION` varchar(40) DEFAULT NULL,
  `ASSERTION_TEMPLATE` longtext,
  `CATEGORY` varchar(25) DEFAULT NULL,
  `DEFAULT_FORMAT` varchar(255) DEFAULT NULL,
  `DELETED` bit(1) DEFAULT NULL,
  `MAX_TOKEN` int DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `NAME` varchar(40) DEFAULT NULL,
  `PROMPT_ID` varchar(255) DEFAULT NULL,
  `STATUS` enum('DRAFT','PUBLISH','ARCHIVE') DEFAULT NULL,
  `TEMPERATURE` double DEFAULT NULL,
  `TOP_P` double DEFAULT NULL,
  `VERSION` varchar(10) NOT NULL,
  `LLM_MODEL_ID` int DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `JSON_MODE` bit(1) DEFAULT NULL,
  `LLM_GUARD` tinyint(1) DEFAULT '0',
  `TYPE` enum('CONVERSATION','NORMAL') DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_application_category_name_status_version` (`APPLICATION`,`CATEGORY`,`NAME`,`STATUS`,`VERSION`),
  UNIQUE KEY `UNIQUE_PROMPT_ID` (`PROMPT_ID`),
  KEY `FKtbl4gawdsuxwinemh9ksdcy8r` (`LLM_MODEL_ID`),
  CONSTRAINT `FKtbl4gawdsuxwinemh9ksdcy8r` FOREIGN KEY (`LLM_MODEL_ID`) REFERENCES `LLM_MODEL` (`ID`));

 CREATE TABLE `PROMPT_AUDIT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` varchar(255) DEFAULT NULL,
  `CATEGORY` varchar(255) DEFAULT NULL,
  `GENERATION_TOKENS` bigint DEFAULT NULL,
  `MODEL` varchar(255) DEFAULT NULL,
  `PROMPT_ID` varchar(255) DEFAULT NULL,
  `PROMPT_TOKEN` bigint DEFAULT NULL,
  `RESPONSE_TEXT` text,
  `RESPONSE_TIME` double DEFAULT NULL,
  `TOTAL_TOKEN` bigint DEFAULT NULL,
  `CHAT_OPTIONS` varchar(255) DEFAULT NULL,
  `META_TAGS` varchar(255) DEFAULT NULL,
  `FUNCTION_ARGS` varchar(255) DEFAULT NULL,
  `AUDIT_ID` varchar(400) DEFAULT NULL,
  `PROMPT_NAME` varchar(150) DEFAULT NULL,
  `CREATION_TIME` datetime DEFAULT NULL,
  `HTTP_STATUS` varchar(150) DEFAULT NULL,
  `REQUEST_TEXT` longtext,
  `STATUS` varchar(50) DEFAULT NULL,
  `TOTAL_COST` double DEFAULT '0',
  `PROVIDER` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `idx_audit_id` (`AUDIT_ID`));

 CREATE TABLE `QUERY` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `QUESTION` longtext,
  `TYPE` varchar(50) DEFAULT NULL,
  `USER_ID` varchar(255) DEFAULT NULL,
  `DELETED` tinyint(1) DEFAULT '0',
  `CUSTOMER_ID` int DEFAULT NULL,
  `CREATOR` int DEFAULT NULL,
  `LAST_MODIFIER` int DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `FKf89bw2f5hii7bu4ep3twgaih3` (`CREATOR`),
  KEY `FK1d5ma109bp31gr0j2xjvrr6ub` (`LAST_MODIFIER`));
  
 CREATE TABLE `SPEECH_TO_TEXT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `LANGUAGE` varchar(255) DEFAULT NULL,
  `SERVICE` enum('GOOGLE','ON_PREMISE','VOICE','WEB_CALL') DEFAULT NULL,
  PRIMARY KEY (`ID`));


 CREATE TABLE `TESTCASE` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `ASSERTIONS` text,
  `DELETED` bit(1) DEFAULT NULL,
  `INPUT_JSON` text,
  `REMARK` text,
  `TESTCASE_ID` varchar(255) DEFAULT NULL,
  `PROMPT_ID` int DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `FKelvpgny3uqkro7f8fb9bqenq2` (`PROMPT_ID`),
  CONSTRAINT `FKelvpgny3uqkro7f8fb9bqenq2` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`));
  

 CREATE TABLE `TEST_PLAN` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `ENABLED` bit(1) DEFAULT NULL,
  `NAME` varchar(255) NOT NULL,
  PRIMARY KEY (`ID`));



 CREATE TABLE `TEST_PLAN_TEST` (
  `TEST_ID` int NOT NULL,
  `TEST_PLAN_ID` int NOT NULL,
  PRIMARY KEY (`TEST_ID`,`TEST_PLAN_ID`),
  KEY `FKjobeyxxgg3akgy624bku7mi0r` (`TEST_PLAN_ID`),
  CONSTRAINT `FKeklkpab6kafriva22joqrklye` FOREIGN KEY (`TEST_ID`) REFERENCES `TESTCASE` (`ID`),
  CONSTRAINT `FKjobeyxxgg3akgy624bku7mi0r` FOREIGN KEY (`TEST_PLAN_ID`) REFERENCES `TEST_PLAN` (`ID`));

 CREATE TABLE `TEXT_TO_SPEECH` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `GENDER` enum('MALE','FEMALE') DEFAULT NULL,
  `NAME` enum('GOOGLE','ON_PREMISE','VOICE','WEB_CALL') NOT NULL,
  `VOICE_NAME` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`));


 CREATE TABLE `TICKET_TRIGGER` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DESCRIPTION` varchar(255) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `TRIGGER_META_DATA` longtext,
  `NAME` varchar(255) DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `DELETED` bit(1) DEFAULT NULL,
  `APPLICATION` varchar(255) DEFAULT NULL,
  `ENTITY` varchar(255) DEFAULT NULL,
  `TYPE` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`));
  
 CREATE TABLE `TOOL` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` varchar(40) DEFAULT NULL,
  `BYTE_CODE_MAP` longtext,
  `CATEGORY` varchar(20) DEFAULT NULL,
  `CLASS_NAME` varchar(100) DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `DELETED` bit(1) DEFAULT NULL,
  `DESCRIPTION` varchar(1200) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `LANGUAGE` varchar(40) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `REQUEST_TYPE` varchar(100) DEFAULT NULL,
  `SOURCE_CODE` text,
  `TOOL_ID` varchar(255) DEFAULT NULL,
  `TOOL_NAME` varchar(70) DEFAULT NULL,
  `TYPE` enum('AGENT','sourceCode','swaggerJson','workFlow','API') DEFAULT NULL,
  `VERSION` varchar(10) DEFAULT NULL,
  `PROMPT_ID` int DEFAULT NULL,
  `TOOL_JSON` text,
  `TOOL_AUTHENTICATION` text,
  `TAGS` varchar(255) DEFAULT NULL,
  `STATUS` enum('DRAFT','PUBLISH','ARCHIVE','ACTIVE','DEACTIVE') DEFAULT NULL,
  `HTTP_METHOD` varchar(10) DEFAULT NULL,
  `PARAMETERS` text,
  `URL` varchar(255) DEFAULT NULL,
  `AUTH_TYPE` varchar(255) DEFAULT NULL,
  `AUTH_VALUE` varchar(255) DEFAULT NULL,
  `AUTH_KEY` varchar(255) DEFAULT NULL,
  `TOOL_IMAGE` text,
  `AGENT_ID` bigint DEFAULT NULL,
  `API_TOOL_INFO` varchar(250) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_application_category_tool_version` (`APPLICATION_NAME`,`CATEGORY`,`TOOL_NAME`,`VERSION`),
  UNIQUE KEY `UNIQUE_TOOL_NAME` (`TOOL_NAME`),
  KEY `FK8sd03djcppki1xhqakyfms8q1` (`PROMPT_ID`),
  CONSTRAINT `FK8sd03djcppki1xhqakyfms8q1` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`));
  
  
 CREATE TABLE `USER` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the User',
  `USERNAME` varchar(50) NOT NULL COMMENT 'Username of the user',
  `EMAIL` varchar(50) NOT NULL COMMENT 'Email address of the user',
  `CUSTOMER_ID` int DEFAULT NULL COMMENT 'Customer ID associated with the user',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_username_customer_id` (`USERNAME`,`CUSTOMER_ID`),
  UNIQUE KEY `unique_email_customer_id` (`EMAIL`,`CUSTOMER_ID`));

 CREATE TABLE `VOICE_AGENT` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `STT_ID` int DEFAULT NULL,
  `TTS_ID` int DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `ICON` text,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `NAME` varchar(255) DEFAULT NULL,
  `PURPOSE` varchar(255) DEFAULT NULL,
  `STATUS` enum('ACTIVE','DEACTIVE','DRAFT','PUBLISH') DEFAULT NULL,
  `VARIABLES` text,
  `VAR_BYTE_CODE_MAP` longtext,
  `LLM_MODEL_ID` int DEFAULT NULL,
  `PROMPT_ID` int DEFAULT NULL,
  `CUSTOMER_ID` int DEFAULT NULL,
  `CREATOR` int DEFAULT NULL,
  `LAST_MODIFIER` int DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `NAME` (`NAME`,`CUSTOMER_ID`),
  KEY `FK35wmo6uoher6nq3sgvb6icyfo` (`STT_ID`),
  KEY `FKks3018i5j4onqn3mli2nxkhhm` (`TTS_ID`),
  KEY `FKf89bw2f5hii7bu4ep3twgaih3` (`CREATOR`),
  KEY `FK1d5ma109bp31gr0j2xjvrr6ub` (`LAST_MODIFIER`),
  KEY `FKeeurovuawskblpw6bt4bb76ii` (`PROMPT_ID`),
  KEY `FKqovdico1tqbsm60ra3duxk6ia` (`LLM_MODEL_ID`),
  CONSTRAINT `FK35wmo6uoher6nq3sgvb6icyfo` FOREIGN KEY (`STT_ID`) REFERENCES `SPEECH_TO_TEXT` (`ID`),
  CONSTRAINT `FKeeurovuawskblpw6bt4bb76ii` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`),
  CONSTRAINT `FKks3018i5j4onqn3mli2nxkhhm` FOREIGN KEY (`TTS_ID`) REFERENCES `TEXT_TO_SPEECH` (`ID`),
  CONSTRAINT `FKqovdico1tqbsm60ra3duxk6ia` FOREIGN KEY (`LLM_MODEL_ID`) REFERENCES `LLM_MODEL` (`ID`));


 CREATE TABLE `VOICE_AGENT_TOOL` (
  `VOICE_AGENT_TOOL` int NOT NULL,
  `TOOL_ID` int NOT NULL,
  PRIMARY KEY (`VOICE_AGENT_TOOL`,`TOOL_ID`),
  KEY `FKt4fue43dalpwo5j1co77ot9t0` (`TOOL_ID`),
  CONSTRAINT `FKmtjj76ga17j035d6yqvqbd8r1` FOREIGN KEY (`VOICE_AGENT_TOOL`) REFERENCES `VOICE_AGENT` (`ID`),
  CONSTRAINT `FKt4fue43dalpwo5j1co77ot9t0` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`)
);

CREATE TABLE `PROCESSOR` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `UNIQUE_KEY` varchar(500) DEFAULT NULL,
  `DISPLAY_NAME` varchar(255) DEFAULT NULL,
  `ICON` text,
  `STYLE_TYPE` varchar(50) DEFAULT NULL,
  `CATEGORY` varchar(50) DEFAULT NULL,
  `SUB_CATEGORY` varchar(50) DEFAULT NULL,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `DELETED` bit(1) DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_key` (`UNIQUE_KEY`)
);

