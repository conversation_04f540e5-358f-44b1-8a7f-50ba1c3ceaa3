DROP TABLE matches;
DROP TABLE prompt_matches;
DROP TABLE SPEECH_TO_TEXT;
DROP TABLE TEXT_TO_SPEECH;
ALTER TABLE TICKET_TRIGGER ADD COLUMN NANO_ID VARCHAR(50) UNIQUE COMMENT 'Unique identifier of the record';


CREATE TABLE `AGENT_TEST_CASES` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key for test case records.',
  `ADVISORS` varchar(500) DEFAULT NULL COMMENT 'Comma-separated list of advisors or agents involved in the test.',
  `DESCRIPTION` varchar(1500) DEFAULT NULL COMMENT 'Detailed description of the test case.',
  `HISTORY` longtext COMMENT 'Conversation or context history relevant to the test case.',
  `NAME` varchar(50) DEFAULT NULL COMMENT 'Short name or identifier for the test case.',
  `USER_QUESTION` text COMMENT 'User input or query that triggers the test case scenario.',
  `PROMPT_ID` int DEFAULT NULL COMMENT 'Foreign key reference to the associated prompt template.',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique identifier of the record',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test case was created.',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the test case.',
  `DELETED` bit(1) DEFAULT b'0' COMMENT 'Flag indicating if the test case is deleted.',
  `VARIABLE_MAP` text COMMENT 'Variables of system prompt',
  `AGENT_ID` varchar(50) DEFAULT NULL COMMENT 'AgentId identifier of the agent',
  `AGENT_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the agent',
  `AGENT_TYPE` varchar(50) DEFAULT NULL COMMENT 'Type of associated agent',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `NANO_ID` (`NANO_ID`),
  UNIQUE KEY `UNIQUE_NAME_AGENT_TEST_CASES` (`NAME`) COMMENT 'Ensures unique test case names for validation and display',
  KEY `FKqkkuuoegmvuuou5sjktgjuc1h` (`PROMPT_ID`),
  CONSTRAINT `FKqkkuuoegmvuuou5sjktgjuc1h` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`)
);

CREATE TABLE `TESTCASE_KNOWLEDGE_BASES` (
  `TESTCASE_ID` bigint NOT NULL COMMENT 'Foreign key to the associated test case.',
  `KNOWLEDGE_BASE_ID` int NOT NULL COMMENT 'Foreign key to the associated knowledge base.',
  PRIMARY KEY (`TESTCASE_ID`,`KNOWLEDGE_BASE_ID`),
  KEY `FKhil30wxbg1riuxvci6et5ob4v` (`KNOWLEDGE_BASE_ID`),
  CONSTRAINT `FKhil30wxbg1riuxvci6et5ob4v` FOREIGN KEY (`KNOWLEDGE_BASE_ID`) REFERENCES `KNOWLEDGE_BASE` (`ID`),
  CONSTRAINT `FKihdjyntl59sux9c8jyx7punfp` FOREIGN KEY (`TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`)
);


CREATE TABLE `TESTCASE_TOOLS` (
  `TESTCASE_ID` bigint NOT NULL COMMENT 'Foreign key to the associated test case.',
  `TOOL_ID` int NOT NULL COMMENT 'Foreign key to the associated tool.',
  PRIMARY KEY (`TESTCASE_ID`,`TOOL_ID`),
  KEY `FKhqdoqvqmigd7pr2uftfgyms9` (`TOOL_ID`),
  CONSTRAINT `FKhqdoqvqmigd7pr2uftfgyms9` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`),
  CONSTRAINT `FKi9qcnufaqydmnb6ev0lo3n14y` FOREIGN KEY (`TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`)
);

CREATE TABLE `ASSERTIONS` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key for assertion records.',
  `ASSERTION_TYPE` enum('LLM_RESPONSE','TOOL_CALLED','TOOL_PARAMETERS') NOT NULL COMMENT 'Type of assertion being tested (e.g., LLM response, tool usage).',
  `DESCRIPTION` varchar(1200) DEFAULT NULL COMMENT 'Detailed explanation or note about the assertion.',
  `MATCH_STRATEGY` enum('CONTAINS','EXACT','REGEX','EVALUATION') NOT NULL COMMENT 'Strategy used to compare actual and expected values.',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique identifier of the record',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the assertion was created.',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the assertion.',
  `DELETED` bit(1) DEFAULT b'0' COMMENT 'Flag indicating if the assertion is deleted.',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_ASSERTION` (`ASSERTION_TYPE`,`MATCH_STRATEGY`) COMMENT 'Ensures no duplicate assertion type + strategy combination',
  UNIQUE KEY `NANO_ID` (`NANO_ID`)
);

CREATE TABLE `TEST_SUITE` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test suite was created.',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the test suite is deleted.',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the test suite.',
  `NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the test suite.',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique identifier of the record',
  `AGENT_NAME` varchar(150) DEFAULT NULL COMMENT 'Name of the agent associated with the test suite',
  `AGENT_ID` varchar(50) DEFAULT NULL COMMENT 'ID of the agent associated with the test suite',
  `AGENT_TYPE` varchar(50) DEFAULT NULL COMMENT 'Type of associated agent',
  PRIMARY KEY (`ID`) COMMENT 'Primary key for test suite records.',
  UNIQUE KEY `UKmol3v5wadw8wduwjr26qjw8ak` (`NANO_ID`),
  UNIQUE KEY `UNIQUE_NANO_ID_TEST_SUITE` (`NANO_ID`),
  UNIQUE KEY `UNIQUE_TEST_SUITE_NAME` (`NAME`) COMMENT 'Constraint for unique test suite name'
);

CREATE TABLE `TESTCASES_AGENT_TESTCASES` (
  `TEST_SUITE_ID` int NOT NULL COMMENT 'Foreign key to the associated test suite.',
  `AGENT_TESTCASE_ID` bigint NOT NULL COMMENT 'Foreign key to the associated agent test case',
  PRIMARY KEY (`TEST_SUITE_ID`,`AGENT_TESTCASE_ID`),
  KEY `FK2a21al663vh7tjxiy8v6hoett` (`AGENT_TESTCASE_ID`),
  CONSTRAINT `FK2a21al663vh7tjxiy8v6hoett` FOREIGN KEY (`AGENT_TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`),
  CONSTRAINT `FKcu9b3myw6h01vvebfe14xsi8a` FOREIGN KEY (`TEST_SUITE_ID`) REFERENCES `TEST_SUITE` (`ID`)
);

CREATE TABLE `TEST_RESULT` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the test result',
  `ACCURACY` double DEFAULT NULL COMMENT 'Overall accuracy of the test suite execution',
  `CONSISTENCY` double DEFAULT NULL COMMENT 'Consistency score over multiple runs',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test result was created',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating soft delete status',
  `DESCRIPTION` varchar(1200) DEFAULT NULL COMMENT 'Human-readable description of the test execution',
  `EXECUTION_ID` varchar(50) DEFAULT NULL COMMENT 'External execution tracking identifier',
  `LATENCY` bigint DEFAULT NULL COMMENT 'Average latency across all test cases',
  `MODEL` varchar(50) DEFAULT NULL COMMENT 'Model used for the test execution (e.g., GPT-4)',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the record was last modified',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique nanoid for UI/client-level tracking',
  `NUMBER_OF_RUNS` int DEFAULT NULL COMMENT 'Number of times the test suite was executed',
  `TEST_CASES_FAILED` int DEFAULT NULL COMMENT 'Number of test cases that failed',
  `TEST_CASES_PASSED` int DEFAULT NULL COMMENT 'Number of test cases that passed',
  `TEST_SUITE_ID` int DEFAULT NULL COMMENT 'Foreign key referencing the test suite',
  `STATUS` varchar(20) DEFAULT NULL COMMENT 'Execution status (e.g., PASSED, FAILED)',
  `PROVIDER` varchar(30) DEFAULT NULL COMMENT 'Provider of the executed test',
  `PROMPT_TOKEN` int DEFAULT NULL COMMENT 'Aggregated prompt tokens used in the test result',
  `GENERATION_TOKENS` int DEFAULT NULL COMMENT 'Aggregated generation tokens used in the test result',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_NANO_ID_TEST_RESULT` (`NANO_ID`),
  KEY `FKsbcvlk0s5wfmirfqlqrxk98xg` (`TEST_SUITE_ID`),
  CONSTRAINT `FKsbcvlk0s5wfmirfqlqrxk98xg` FOREIGN KEY (`TEST_SUITE_ID`) REFERENCES `TEST_SUITE` (`ID`)
);

CREATE TABLE `TEST_CASE_RESULT` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the test case result',
  `ACCURACY` double DEFAULT NULL COMMENT 'Accuracy of the specific test case execution',
  `ASSERTIONS_FAILED` int DEFAULT NULL COMMENT 'Number of failed assertions in the test case',
  `ASSERTIONS_PASSED` int DEFAULT NULL COMMENT 'Number of passed assertions in the test case',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test case result was created',
  `IS_PASSED` bit(1) DEFAULT NULL COMMENT 'True if all assertions in the test case passed',
  `LATENCY` bigint DEFAULT NULL COMMENT 'Latency for this specific test case',
  `TEST_RESULT_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the test result',
  `TEST_CASE_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the original test case definition',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique identifier of the record',
  `FAILURE_REASON` text COMMENT 'Reason for failure of test-case in case of error',
  `IS_ERROR` bit(1) DEFAULT NULL COMMENT 'True if an error occurred in test case',
  `RUN_NUMBER` int DEFAULT NULL COMMENT 'Run number of test cases of a specific execution',
  `PROMPT_TOKEN` int DEFAULT NULL COMMENT 'Token count used for prompt',
  `GENERATION_TOKENS` int DEFAULT NULL COMMENT 'Token count used for generation',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `NANO_ID` (`NANO_ID`),
  KEY `FK6o46umacd00w4ym4i0ipjlt55` (`TEST_RESULT_ID`),
  KEY `FKgape278nx21va01o1njhu51gc` (`TEST_CASE_ID`),
  CONSTRAINT `FK6o46umacd00w4ym4i0ipjlt55` FOREIGN KEY (`TEST_RESULT_ID`) REFERENCES `TEST_RESULT` (`ID`),
  CONSTRAINT `FKgape278nx21va01o1njhu51gc` FOREIGN KEY (`TEST_CASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`)
);

CREATE TABLE `ASSERTION_RESULT` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the assertion result',
  `ACCURACY_SCORE` double DEFAULT NULL COMMENT 'Accuracy score of this specific assertion',
  `ACTUAL` text COMMENT 'Actual output received from the model',
  `ASSERTION_TYPE` varchar(30) DEFAULT NULL COMMENT 'Type of assertion (e.g., EXACT_MATCH, SEMANTIC)',
  `COMMENT` text COMMENT 'Reviewer or system comment for the assertion',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the assertion result was created',
  `EXPECTED` text COMMENT 'Expected output for the assertion',
  `IS_PASSED` bit(1) DEFAULT NULL COMMENT 'Whether the assertion passed',
  `MATCH_STRATEGY` varchar(30) DEFAULT NULL COMMENT 'Strategy used to match actual and expected outputs',
  `USER_QUESTION` text COMMENT 'Original user query or prompt',
  `TEST_CASE_RESULT_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the test case result',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique identifier of the record',
  `PARAMETER_NAME` varchar(50) DEFAULT NULL COMMENT 'Parameter name of the input schema of tool associated with the assertion',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `NANO_ID` (`NANO_ID`),
  KEY `FK53iu7xtygddsyoi3ansrh89l6` (`TEST_CASE_RESULT_ID`),
  CONSTRAINT `FK53iu7xtygddsyoi3ansrh89l6` FOREIGN KEY (`TEST_CASE_RESULT_ID`) REFERENCES `TEST_CASE_RESULT` (`ID`)
);

CREATE TABLE `AGENT_TESTCASE_ASSERTIONS` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `CREATED_TIME` datetime(6) DEFAULT NULL,
  `EXPECTED_VALUE` text,
  `MODIFIED_TIME` datetime(6) DEFAULT NULL,
  `AGENT_TESTCASE_ID` bigint DEFAULT NULL,
  `ASSERTION_ID` bigint DEFAULT NULL,
  `TOOL_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the tool associated with the assertion',
  `PARAMETER_NAME` varchar(50) DEFAULT NULL COMMENT 'Parameter name of the input schema of tool associated with the assertion',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `UNIQUE_AGENT_TESTCASE_ASSERTIONS` (`AGENT_TESTCASE_ID`,`ASSERTION_ID`,`PARAMETER_NAME`),
  KEY `FKimkyod15ovv91col9ch50hk59` (`AGENT_TESTCASE_ID`),
  KEY `FKtoo0m4yg3r00vgl3fkee3sq3b` (`ASSERTION_ID`),
  CONSTRAINT `FKimkyod15ovv91col9ch50hk59` FOREIGN KEY (`AGENT_TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`),
  CONSTRAINT `FKtoo0m4yg3r00vgl3fkee3sq3b` FOREIGN KEY (`ASSERTION_ID`) REFERENCES `ASSERTIONS` (`ID`)
);
