INSERT INTO ASSERTIONS (
  ASSERTION_TYPE, DESC<PERSON>PTION, MATCH_STRATEGY, NANO_ID, CREATED_TIME, MODIFIED_TIME, DELETED
) VALUES (
  'LLM_RESPONSE',
  'Ensure the response includes confirmation text.',
  'EVALUATION',
  'USgxlt5JAu7yxre0jeZuc',
  NOW(6),
  NOW(6),
  b'0'
);

INSERT INTO ASSERTIONS (
  ASSERTION_TYPE, DESCRIPTION, MATCH_STRATEGY, NANO_ID, CREATED_TIME, MODIFIED_TIME, DELETED
) VALUES (
  'TOOL_CALLED',
  'Check that the specific tool was invoked.',
  'EXACT',
  'USgxlt5JAu7yxre0jegu1',
  NOW(6),
  NOW(6),
  b'0'
);

INSERT INTO ASSERTIONS (
  ASSERTION_TYPE, DESCRIPTION, MATCH_STRATEGY, NANO_ID, CREATED_TIME, MODIFIED_TIME, DELETED
) VALUES (
  'TOOL_PARAMETERS',
  'Verify tool parameters structure exists.',
  'EVALUATION',
  'USgxlt5JAu7Hxre0jegu2',
  NOW(6),
  NOW(6),
  b'0'
);

INSERT INTO ASSERTIONS (
  ASSERTION_TYPE, DESCRIPTION, MATCH_STRATEGY, NANO_ID, CREATED_TIME, MODIFIED_TIME, DELETED
) VALUES (
  'TOOL_PARAMETERS',
  'Verify tool parameters structure exists.',
  'CONTAINS',
  'USgxlt5JAu7yxre0jegu2',
  NOW(6),
  NOW(6),
  b'0'
);

INSERT INTO ASSERTIONS (
  ASSERTION_TYPE, DESCRIPTION, MATCH_STRATEGY, NANO_ID, CREATED_TIME, MODIFIED_TIME, DELETED
) VALUES (
  'TOOL_PARAMETERS',
  'Verify tool parameters exactly matches expected phrase.',
  'EXACT',
  'USgxlt5JAu7yxre0jegw2',
  NOW(6),
  NOW(6),
  b'0'
);

INSERT INTO ASSERTIONS (
  ASSERTION_TYPE, DESCRIPTION, MATCH_STRATEGY, NANO_ID, CREATED_TIME, MODIFIED_TIME, DELETED
) VALUES (
  'TOOL_PARAMETERS',
  'Verify tool parameters response format using regex.',
  'REGEX',
  'USgxlt5JAu7yxre0jegw3',
  NOW(6),
  NOW(6),
  b'0'
);

