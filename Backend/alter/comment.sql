ALTER TABLE `AGENT`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for the agent records.',
MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the agent record was created.',
<PERSON><PERSON><PERSON>Y `DISPLAY_NAME` varchar(255) DEFAULT NULL COMMENT 'Display name of the agent.',
<PERSON><PERSON><PERSON><PERSON> `ICON` text COMMENT 'Icon for the agent.',
MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the agent.',
<PERSON><PERSON><PERSON>Y `NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the agent.',
M<PERSON><PERSON>Y `PURPOSE` varchar(255) DEFAULT NULL COMMENT 'Purpose of the agent.',
MODIFY `STATUS` enum('ACTIVE','DEACTIVE','DRAFT','PUBLISH') DEFAULT NULL COMMENT 'Current status of the agent.',
<PERSON><PERSON><PERSON>Y `VARIABLES` text COMMENT 'Variables associated with the agent.',
M<PERSON><PERSON>Y `VAR_BYTE_CODE_MAP` longtext COMMENT 'Map of the bytecode for the agent variables.',
MODIFY `LLM_MODEL_ID` int DEFAULT NULL COMMENT 'Foreign key to the LLM model used by the agent.',
MODIFY `PROMPT_ID` int DEFAULT NULL COMMENT 'Foreign key to the prompt associated with the agent.';

ALTER TABLE `AGENT_HISTORY`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for agent history records.',
MODIFY `AGENT_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the agent associated with this history.',
MODIFY `CURRENT_PLAN` text COMMENT 'Current plan or configuration of the agent.';

ALTER TABLE `AGENT_MESSAGE`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for agent message records.',
MODIFY `ASSISTANT_MESSAGE` varchar(6000) DEFAULT NULL COMMENT 'Message sent by the assistant.',
MODIFY `USER_MESSAGE` varchar(1500) DEFAULT NULL COMMENT 'Message sent by the user.',
MODIFY `AGENT_HISTORY_ID` int DEFAULT NULL COMMENT 'Foreign key linking to the agent history.',
MODIFY `TIME_STAMP` bigint DEFAULT NULL COMMENT 'Timestamp when the message was sent.';

ALTER TABLE `AGENT_TOOL`
MODIFY `AGENT_TOOL_ID` int NOT NULL COMMENT 'Foreign key linking to the agent.',
MODIFY `TOOL_ID` int NOT NULL COMMENT 'Foreign key linking to the tool associated with the agent.';

ALTER TABLE `EXCEPTION_AUDIT`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for exception audit records.',
MODIFY `APPLICATION_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the application where the exception occurred.',
MODIFY `CATEGORY` varchar(255) DEFAULT NULL COMMENT 'Category of the exception.',
MODIFY `EXCEPTION_MESSAGE` varchar(2500) DEFAULT NULL COMMENT 'Detailed message of the exception.',
MODIFY `EXCEPTION_TRACE` longtext COMMENT 'Stack trace of the exception.',
MODIFY `IDENTIFIER` text COMMENT 'Unique identifier for the exception.',
MODIFY `METHOD_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the method where the exception occurred.',
MODIFY `METHOD_PARAMETERS` text COMMENT 'Parameters passed to the method where the exception occurred.',
MODIFY `PROMPT_ID` varchar(255) DEFAULT NULL COMMENT 'ID of the associated prompt.',
MODIFY `TIMESTAMP` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the exception occurred.',
MODIFY `AUDIT_ID` varchar(400) DEFAULT NULL COMMENT 'Audit identifier for the exception.';

ALTER TABLE `KNOWLEDGE_BASE`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for knowledge base records.',
MODIFY `NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the knowledge base entry.',
MODIFY `TYPE` enum('SQL','API','DOCUMENT','CODE','WEBSITE','COLLECTION') DEFAULT NULL COMMENT 'Type of the knowledge base entry.',
MODIFY `DESCRIPTION` varchar(1200) DEFAULT NULL COMMENT 'Description of the knowledge base entry.',
MODIFY `API_AUTH_TYPE` varchar(255) DEFAULT NULL COMMENT 'API authentication type.',
MODIFY `API_AUTH_VALUE` varchar(255) DEFAULT NULL COMMENT 'API authentication value.',
MODIFY `API_ENDPOINT` varchar(255) DEFAULT NULL COMMENT 'API endpoint for the knowledge base entry.',
MODIFY `API_TYPE` varchar(255) DEFAULT NULL COMMENT 'Type of API used in the knowledge base entry.',
MODIFY `DB_NAME` varchar(255) DEFAULT NULL COMMENT 'Database name for the knowledge base entry.',
MODIFY `DB_PASSWORD` varchar(255) DEFAULT NULL COMMENT 'Database password for the knowledge base entry.',
MODIFY `DB_USERNAME` varchar(255) DEFAULT NULL COMMENT 'Database username for the knowledge base entry.',
MODIFY `DOC_ID` text COMMENT 'Document ID associated with the knowledge base entry.',
MODIFY `DOC_META_DATA` varchar(255) DEFAULT NULL COMMENT 'Metadata associated with the document.',
MODIFY `DOC_TYPE` varchar(255) DEFAULT NULL COMMENT 'Type of the document.',
MODIFY `IS_CONTEXT` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the knowledge base is used as context.',
MODIFY `SIMILARITY_THRESHOLD` double DEFAULT NULL COMMENT 'Threshold for similarity search.',
MODIFY `SOURCE_CODE` text COMMENT 'Source code associated with the knowledge base entry.',
MODIFY `CLASS_NAME` varchar(255) DEFAULT NULL COMMENT 'Class name for code-based knowledge base entries.',
MODIFY `BYTE_CODE_MAP` longtext COMMENT 'Bytecode map for code-based knowledge base entries.',
MODIFY `TOP_K` int DEFAULT NULL COMMENT 'Top K results to return in similarity search.',
MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the knowledge base entry was created.',
MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification.',
MODIFY `VECTOR_META_DATA` text COMMENT 'Metadata associated with vector representation of knowledge base.',
MODIFY `DELETED` tinyint(1) DEFAULT '0' COMMENT 'Flag indicating if the knowledge base entry is deleted.',
MODIFY `FILE_NAME` varchar(150) DEFAULT NULL COMMENT 'File name associated with the knowledge base entry.',
MODIFY `FILTER` varchar(255) DEFAULT NULL COMMENT 'Filter criteria for the knowledge base.',
MODIFY `WEBSITE_URL` varchar(500) DEFAULT NULL COMMENT 'URL of the website associated with the knowledge base entry.',
MODIFY `WEBSITE_TASK_STATUS` varchar(20) DEFAULT NULL COMMENT 'Status of the website task.',
MODIFY `COLLECTION_NAME` varchar(40) DEFAULT NULL COMMENT 'Name of the collection to which the knowledge base entry belongs.',
MODIFY `WEBSITE_TASK_ID` varchar(70) DEFAULT NULL COMMENT 'Task ID of the website task.',
MODIFY `WEBSITE_TASK_ERROR` varchar(1500) DEFAULT NULL COMMENT 'Error message from the website task.',
MODIFY `TAG` varchar(255) DEFAULT NULL COMMENT 'Tags associated with the knowledge base entry.',
MODIFY `INTEGRATION` varchar(255) DEFAULT NULL COMMENT 'Integration information for the knowledge base.',
MODIFY `TABLES` text COMMENT 'Tables associated with the knowledge base.',
MODIFY `RETURN_DIRECT` tinyint(1) DEFAULT '0' COMMENT 'Flag indicating if the knowledge base returns direct responses.';

ALTER TABLE `LLM_MODEL`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key, unique identifier for the LLM model',
MODIFY `API_KEY` varchar(255) DEFAULT NULL COMMENT 'API key for accessing the LLM model',
MODIFY `BASE_URL` varchar(255) DEFAULT NULL COMMENT 'Base URL for accessing the model API',
MODIFY `INFERENCE` varchar(255) DEFAULT NULL COMMENT 'Inference type for the model',
MODIFY `MODEL` varchar(255) DEFAULT NULL COMMENT 'The model name',
MODIFY `PROVIDER` varchar(255) DEFAULT NULL COMMENT 'The provider of the model',
MODIFY `LANGUAGE` varchar(255) DEFAULT NULL COMMENT 'The language the model supports',
MODIFY `STATUS` varchar(255) DEFAULT NULL COMMENT 'Current status of the model',
MODIFY `TYPE` varchar(255) DEFAULT NULL COMMENT 'Type of the model',
MODIFY `VOICE` varchar(255) DEFAULT NULL COMMENT 'Voice model name',
MODIFY `INPUT_COST` double DEFAULT '0' COMMENT 'Cost for input processing',
MODIFY `OUTPUT_COST` double DEFAULT '0' COMMENT 'Cost for output generation';

ALTER TABLE `MCP_SERVER`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for MCP server',
MODIFY `COMMAND_TO_RUN` text COMMENT 'The command to run on the server',
MODIFY `DELETED` bit(1) DEFAULT false COMMENT 'Indicates if the server is deleted',
MODIFY `DESCRIPTION` varchar(300) DEFAULT NULL COMMENT 'Description of the MCP server',
MODIFY `ICON` text COMMENT 'Icon for the server',
MODIFY `IS_CUSTOM_SERVER` tinyint(1) DEFAULT NULL COMMENT 'Indicates if the server is custom',
MODIFY `NAME` varchar(100) NOT NULL COMMENT 'Name of the MCP server',
MODIFY `TYPE` enum('API','JSON') DEFAULT NULL COMMENT 'Type of the MCP server',
MODIFY `URL` text COMMENT 'URL for the MCP server',
MODIFY `CREATED_TIME` datetime DEFAULT NULL COMMENT 'Creation time of the server',
MODIFY `MODIFIED_TIME` datetime DEFAULT NULL COMMENT 'Last modified time of the server';

ALTER TABLE `MESSAGES`
MODIFY `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key for the message',
MODIFY `CONTENT` longtext COMMENT 'The content of the message',
MODIFY `ROLE` varchar(255) DEFAULT NULL COMMENT 'Role of the message',
MODIFY `PROMPT_ID` int DEFAULT NULL COMMENT 'Foreign key to the PROMPT table';

ALTER TABLE `PROCESSOR`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for the processor',
MODIFY `UNIQUE_KEY` varchar(500) DEFAULT NULL COMMENT 'Unique key for identifying the processor',
MODIFY `DISPLAY_NAME` varchar(255) DEFAULT NULL COMMENT 'Display name for the processor',
MODIFY `ICON` text COMMENT 'Icon representing the processor',
MODIFY `STYLE_TYPE` varchar(50) DEFAULT NULL COMMENT 'Style type of the processor',
MODIFY `CATEGORY` varchar(50) DEFAULT NULL COMMENT 'Category of the processor',
MODIFY `SUB_CATEGORY` varchar(50) DEFAULT NULL COMMENT 'Sub-category of the processor',
MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Creation time of the processor',
MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Last modified time of the processor',
MODIFY `DELETED` bit(1) DEFAULT false COMMENT 'Indicates if the processor is deleted',
MODIFY `JSON_STRUCTURE` text COMMENT 'The JSON structure associated with the processor';

ALTER TABLE `PROMPT`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for the prompt',
MODIFY `APPLICATION` varchar(40) DEFAULT NULL COMMENT 'Application name associated with the prompt',
MODIFY `ASSERTION_TEMPLATE` longtext COMMENT 'Template used for assertions in the prompt',
MODIFY `CATEGORY` varchar(25) DEFAULT NULL COMMENT 'Category of the prompt',
MODIFY `DEFAULT_FORMAT` varchar(255) DEFAULT NULL COMMENT 'Default format for the prompt',
MODIFY `DELETED` bit(1) DEFAULT false COMMENT 'Indicates if the prompt is deleted',
MODIFY `MAX_TOKEN` int DEFAULT NULL COMMENT 'Maximum token count for the prompt',
MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Last modified time of the prompt',
MODIFY `NAME` varchar(40) DEFAULT NULL COMMENT 'Name of the prompt',
MODIFY `PROMPT_ID` varchar(255) DEFAULT NULL COMMENT 'Prompt ID for identification',
MODIFY `STATUS` enum('DRAFT','PUBLISH','ARCHIVE') DEFAULT NULL COMMENT 'Status of the prompt',
MODIFY `TEMPERATURE` double DEFAULT NULL COMMENT 'Temperature for prompt generation',
MODIFY `TOP_P` double DEFAULT NULL COMMENT 'Top-p for sampling in prompt generation',
MODIFY `VERSION` varchar(10) NOT NULL COMMENT 'Version of the prompt',
MODIFY `LLM_MODEL_ID` int DEFAULT NULL COMMENT 'Foreign key to LLM_MODEL table',
MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Creation time of the prompt',
MODIFY `JSON_MODE` bit(1) DEFAULT NULL COMMENT 'Indicates if JSON mode is enabled',
MODIFY `LLM_GUARD` tinyint(1) DEFAULT '0' COMMENT 'Indicates if LLM guard is enabled',
MODIFY `TYPE` enum('CONVERSATION','NORMAL') DEFAULT NULL COMMENT 'Type of the prompt',
MODIFY `TAG` varchar(255) DEFAULT NULL COMMENT 'Tags associated with the prompt';

ALTER TABLE `TESTCASE`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key for the test case records.',
MODIFY `ASSERTIONS` text COMMENT 'Assertions associated with the test case.',
MODIFY `DELETED` bit(1) DEFAULT false COMMENT 'Flag indicating if the test case is deleted.',
MODIFY `INPUT_JSON` text COMMENT 'Input JSON associated with the test case.',
MODIFY `REMARK` text COMMENT 'Remarks or additional notes for the test case.',
MODIFY `TESTCASE_ID` varchar(255) DEFAULT NULL COMMENT 'Unique identifier for the test case.',
MODIFY `PROMPT_ID` int DEFAULT NULL COMMENT 'Foreign key linking to the prompt associated with the test case.',
MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test case was created.',
MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the test case.';

ALTER TABLE `PROMPT_AUDIT`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the table',
    MODIFY `APPLICATION_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the application',
    MODIFY `CATEGORY` varchar(255) DEFAULT NULL COMMENT 'Category of the prompt audit',
    MODIFY `GENERATION_TOKENS` bigint DEFAULT NULL COMMENT 'Number of tokens generated',
    MODIFY `MODEL` varchar(255) DEFAULT NULL COMMENT 'Model used for the audit',
    MODIFY `PROMPT_ID` varchar(255) DEFAULT NULL COMMENT 'ID of the prompt',
    MODIFY `PROMPT_TOKEN` bigint DEFAULT NULL COMMENT 'Number of tokens in the prompt',
    MODIFY `RESPONSE_TEXT` text COMMENT 'Text of the response',
    MODIFY `RESPONSE_TIME` double DEFAULT NULL COMMENT 'Time taken for generating the response',
    MODIFY `TOTAL_TOKEN` bigint DEFAULT NULL COMMENT 'Total tokens used',
    MODIFY `CHAT_OPTIONS` varchar(255) DEFAULT NULL COMMENT 'Options used in chat',
    MODIFY `META_TAGS` varchar(255) DEFAULT NULL COMMENT 'Meta tags associated with the audit',
    MODIFY `FUNCTION_ARGS` varchar(255) DEFAULT NULL COMMENT 'Function arguments used',
    MODIFY `AUDIT_ID` varchar(400) DEFAULT NULL COMMENT 'Audit ID',
    MODIFY `PROMPT_NAME` varchar(150) DEFAULT NULL COMMENT 'Name of the prompt',
    MODIFY `CREATION_TIME` datetime DEFAULT NULL COMMENT 'Time when the audit was created',
    MODIFY `HTTP_STATUS` int DEFAULT NULL COMMENT 'HTTP Status of the request',
    MODIFY `REQUEST_TEXT` longtext COMMENT 'Text of the request',
    MODIFY `STATUS` varchar(50) DEFAULT NULL COMMENT 'Status of the prompt audit',
    MODIFY `TOTAL_COST` double DEFAULT '0' COMMENT 'Total cost of the audit',
    MODIFY `PROVIDER` varchar(250) DEFAULT NULL COMMENT 'Provider of the service',
    MODIFY `ERROR_MESSAGE` varchar(5000) DEFAULT NULL COMMENT 'Error message if any',
    MODIFY `AGENT_NAME` varchar(100) DEFAULT NULL COMMENT 'Agent name used';

ALTER TABLE `QUERY`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the query table',
    MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the query was created',
    MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the query was last modified',
    MODIFY `QUESTION` longtext COMMENT 'Text of the query',
    MODIFY `TYPE` varchar(50) DEFAULT NULL COMMENT 'Type of the query',
    MODIFY `USER_ID` varchar(255) DEFAULT NULL COMMENT 'ID of the user making the query',
    MODIFY `DELETED` tinyint(1) DEFAULT '0' COMMENT 'Flag to mark if the query is deleted';

ALTER TABLE `SPEECH_TO_TEXT`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the table',
    MODIFY `LANGUAGE` varchar(255) DEFAULT NULL COMMENT 'Language for speech-to-text conversion',
    MODIFY `SERVICE` enum('GOOGLE','ON_PREMISE','VOICE','WEB_CALL') DEFAULT NULL COMMENT 'Speech-to-text service used';

ALTER TABLE `TAG`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the tag',
    MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the tag was created',
    MODIFY `DELETED` bit(1) DEFAULT b'0' COMMENT 'Flag to mark if the tag is deleted',
    MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the tag was last modified',
    MODIFY `NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the tag',
    MODIFY `TYPE` varchar(255) DEFAULT NULL COMMENT 'Type of the tag';

ALTER TABLE `TEST_PLAN`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the test plan',
    MODIFY `ENABLED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the test plan is enabled',
    MODIFY `NAME` varchar(255) NOT NULL COMMENT 'Name of the test plan';

ALTER TABLE `TEST_PLAN_TEST`
    MODIFY `TEST_ID` int NOT NULL COMMENT 'ID of the test',
    MODIFY `TEST_PLAN_ID` int NOT NULL COMMENT 'ID of the test plan';

ALTER TABLE `TEXT_TO_SPEECH`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the text-to-speech table',
    MODIFY `GENDER` enum('MALE','FEMALE') DEFAULT NULL COMMENT 'Gender of the voice used for text-to-speech',
    MODIFY `NAME` enum('GOOGLE','ON_PREMISE','VOICE','WEB_CALL') NOT NULL COMMENT 'Service name for text-to-speech',
    MODIFY `VOICE_NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the voice used';

ALTER TABLE `TICKET_TRIGGER`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the ticket trigger table',
    MODIFY `DESCRIPTION` varchar(255) DEFAULT NULL COMMENT 'Description of the trigger',
    MODIFY `DISPLAY_NAME` varchar(255) DEFAULT NULL COMMENT 'Display name of the trigger',
    MODIFY `TRIGGER_META_DATA` longtext COMMENT 'Meta data related to the trigger',
    MODIFY `NAME` varchar(255) DEFAULT NULL COMMENT 'Name of the trigger',
    MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the trigger was created',
    MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the trigger was last modified',
    MODIFY `DELETED` bit(1) DEFAULT false COMMENT 'Flag to mark if the trigger is deleted',
    MODIFY `APPLICATION` varchar(255) DEFAULT NULL COMMENT 'Application associated with the trigger',
    MODIFY `ENTITY` varchar(255) DEFAULT NULL COMMENT 'Entity associated with the trigger',
    MODIFY `TYPE` varchar(255) DEFAULT NULL COMMENT 'Type of the trigger';

ALTER TABLE `TOOL`
    MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary Key of the tool',
    MODIFY `APPLICATION_NAME` varchar(40) DEFAULT NULL COMMENT 'Application name the tool belongs to',
    MODIFY `BYTE_CODE_MAP` longtext COMMENT 'Byte code map for the tool',
    MODIFY `CATEGORY` varchar(20) DEFAULT NULL COMMENT 'Category of the tool',
    MODIFY `CLASS_NAME` varchar(100) DEFAULT NULL COMMENT 'Class name of the tool',
    MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the tool was created',
    MODIFY `DELETED` bit(1) DEFAULT false COMMENT 'Flag to mark if the tool is deleted',
    MODIFY `DESCRIPTION` varchar(1200) DEFAULT NULL COMMENT 'Description of the tool',
    MODIFY `DISPLAY_NAME` varchar(255) DEFAULT NULL COMMENT 'Display name of the tool',
    MODIFY `LANGUAGE` varchar(40) DEFAULT NULL COMMENT 'Programming language the tool is written in',
    MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Time when the tool was last modified',
    MODIFY `REQUEST_TYPE` varchar(100) DEFAULT NULL COMMENT 'Type of request the tool handles',
    MODIFY `SOURCE_CODE` longtext COMMENT 'Source code of the tool',
    MODIFY `TOOL_ID` varchar(255) DEFAULT NULL COMMENT 'ID of the tool',
    MODIFY `TOOL_NAME` varchar(70) DEFAULT NULL COMMENT 'Name of the tool',
    MODIFY `TYPE` enum('AGENT','sourceCode','swaggerJson','workFlow','API','connector','agentBuilder') DEFAULT NULL COMMENT 'Type of the tool',
    MODIFY `VERSION` varchar(10) DEFAULT NULL COMMENT 'Version of the tool',
    MODIFY `PROMPT_ID` int DEFAULT NULL COMMENT 'ID of the prompt associated with the tool',
    MODIFY `TOOL_JSON` text COMMENT 'JSON configuration for the tool',
    MODIFY `TOOL_AUTHENTICATION` text COMMENT 'Authentication details for the tool',
    MODIFY `TAGS` varchar(255) DEFAULT NULL COMMENT 'Tags associated with the tool',
    MODIFY `STATUS` enum('DRAFT','PUBLISH','ARCHIVE','ACTIVE','DEACTIVE') DEFAULT NULL COMMENT 'Status of the tool',
    MODIFY `HTTP_METHOD` varchar(10) DEFAULT NULL COMMENT 'HTTP method supported by the tool',
    MODIFY `PARAMETERS` text COMMENT 'Parameters required by the tool',
    MODIFY `URL` varchar(255) DEFAULT NULL COMMENT 'URL for the tool',
    MODIFY `AUTH_TYPE` varchar(255) DEFAULT NULL COMMENT 'Authentication type for the tool',
    MODIFY `AUTH_VALUE` varchar(255) DEFAULT NULL COMMENT 'Authentication value for the tool',
    MODIFY `AUTH_KEY` varchar(255) DEFAULT NULL COMMENT 'Authentication key for the tool',
    MODIFY `TOOL_IMAGE` text COMMENT 'Image associated with the tool',
    MODIFY `AGENT_ID` bigint DEFAULT NULL COMMENT 'Agent ID associated with the tool',
    MODIFY `API_TOOL_INFO` text COMMENT 'API tool information',
    MODIFY `RETURN_DIRECT` tinyint(1) DEFAULT '0' COMMENT 'Flag to return directly';

ALTER TABLE `VOICE_AGENT_TOOL`
MODIFY `VOICE_AGENT_TOOL` int NOT NULL COMMENT 'Foreign key referencing VOICE_AGENT(ID)',
MODIFY `TOOL_ID` int NOT NULL COMMENT 'Foreign key referencing TOOL(ID)';

ALTER TABLE `VOICE_AGENT`
MODIFY `ID` int NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the voice agent',
MODIFY `STT_ID` int DEFAULT NULL COMMENT 'Foreign key to Speech-to-Text model',
MODIFY `TTS_ID` int DEFAULT NULL COMMENT 'Foreign key to Text-to-Speech model',
MODIFY `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Record creation timestamp',
MODIFY `DISPLAY_NAME` varchar(255) DEFAULT NULL COMMENT 'Display name of the voice agent',
MODIFY `ICON` text COMMENT 'Icon representing the voice agent',
MODIFY `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Record last modified timestamp',
MODIFY `NAME` varchar(255) DEFAULT NULL COMMENT 'Unique name of the voice agent',
MODIFY `PURPOSE` varchar(255) DEFAULT NULL COMMENT 'Purpose or description of the voice agent',
MODIFY `STATUS` enum('ACTIVE','DEACTIVE','DRAFT','PUBLISH') DEFAULT NULL COMMENT 'Current status of the voice agent',
MODIFY `VARIABLES` text COMMENT 'Variables used by the voice agent',
MODIFY `VAR_BYTE_CODE_MAP` longtext COMMENT 'Byte code mapping of the voice agent variables',
MODIFY `LLM_MODEL_ID` int DEFAULT NULL COMMENT 'Foreign key to associated LLM model',
MODIFY `PROMPT_ID` int DEFAULT NULL COMMENT 'Foreign key to associated prompt';

