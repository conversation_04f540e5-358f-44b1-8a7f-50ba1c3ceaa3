
CREATE TABLE `TESTCASE_KNOWLEDGE_BASES` (
  `TESTCASE_ID` bigint NOT NULL COMMENT 'Foreign key to the associated test case.',
  `KNOWLEDGE_BASE_ID` int NOT NULL COMMENT 'Foreign key to the associated knowledge base.',
  PRIMARY KEY (`TESTCASE_ID`,`KNOWLEDGE_BASE_ID`),
  CONSTRAINT `FKhil30wxbg1riuxvci6et5ob4v` FOREIGN KEY (`KNOWLEDGE_BASE_ID`) REFERENCES `KNOWLEDGE_BASE` (`ID`),
  CONSTRAINT `FKihdjyntl59sux9c8jyx7punfp` FOREIGN KEY (`TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`)
);


CREATE TABLE `TESTCASE_TOOLS` (
  `TESTCASE_ID` bigint NOT NULL COMMENT 'Foreign key to the associated test case.',
  `TOOL_ID` int NOT NULL COMMENT 'Foreign key to the associated tool.',
  <PERSON><PERSON><PERSON><PERSON> KEY (`TESTCASE_ID`,`TOOL_ID`),
  CONSTRAINT `FKhqdoqvqmigd7pr2uftfgyms9` FOREIGN KEY (`TOOL_ID`) REFERENCES `TOOL` (`ID`),
  CONSTRAINT `FKi9qcnufaqydmnb6ev0lo3n14y` FOREIGN KEY (`TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`)
);

CREATE TABLE `AGENT_TEST_CASES` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key for test case records.',
  `ADVISORS` varchar(500) DEFAULT NULL COMMENT 'Comma-separated list of advisors or agents involved in the test.',
  `DESCRIPTION` varchar(1500) COMMENT 'Detailed description of the test case.',
  `HISTORY` longtext COMMENT 'Conversation or context history relevant to the test case.',
  `NAME` varchar(50) DEFAULT NULL COMMENT 'Short name or identifier for the test case.',
  `USER_QUESTION` text COMMENT 'User input or query that triggers the test case scenario.',
  `PROMPT_ID` int DEFAULT NULL COMMENT 'Foreign key reference to the associated prompt template.',
  PRIMARY KEY (`ID`),
  KEY `FKqkkuuoegmvuuou5sjktgjuc1h` (`PROMPT_ID`),
  CONSTRAINT `FKqkkuuoegmvuuou5sjktgjuc1h` FOREIGN KEY (`PROMPT_ID`) REFERENCES `PROMPT` (`ID`)
);

CREATE TABLE `ASSERTIONS` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key for assertion records.',
  `ASSERTION_TYPE` enum('LLM_RESPONSE','TOOL_CALLED','TOOL_PARAMETERS') NOT NULL COMMENT 'Type of assertion being tested (e.g., LLM response, tool usage).',
  `DESCRIPTION` varchar(1200) COMMENT 'Detailed explanation or note about the assertion.',
  `EXPECTED_VALUE` text COMMENT 'The expected value to be matched against actual output.',
  `MATCH_STRATEGY` enum('CONTAINS','EXACT','REGEX') NOT NULL COMMENT 'Strategy used to compare actual and expected values.',
  `TESTCASE_ID` bigint NOT NULL COMMENT 'Foreign key reference to the related test case.',
  PRIMARY KEY (`ID`),
  KEY `FKew18w4dilss6dmpsuup0rce1k` (`TESTCASE_ID`),
  CONSTRAINT `FKew18w4dilss6dmpsuup0rce1k` FOREIGN KEY (`TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`)
);

ALTER TABLE ASSERTIONS ADD COLUMN NANO_ID VARCHAR(50) UNIQUE COMMENT 'Unique identifier of the record';
ALTER TABLE ASSERTIONS ADD COLUMN `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the assertion was created.';
ALTER TABLE ASSERTIONS ADD COLUMN `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the assertion.';
ALTER TABLE ASSERTIONS ADD COLUMN `DELETED` bit(1) DEFAULT b'0' COMMENT 'Flag indicating if the assertion is deleted.';


ALTER TABLE AGENT_TEST_CASES ADD COLUMN NANO_ID VARCHAR(50) UNIQUE COMMENT 'Unique identifier of the record';
ALTER TABLE AGENT_TEST_CASES ADD COLUMN `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test case was created.';
ALTER TABLE AGENT_TEST_CASES ADD COLUMN `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the test case.';
ALTER TABLE AGENT_TEST_CASES ADD COLUMN `DELETED` bit(1) DEFAULT b'0' COMMENT 'Flag indicating if the test case is deleted.';

CREATE TABLE `TEST_SUITE` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test suite was created.',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the test suite is deleted.',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the test suite.',
  `NAME` varchar(50) DEFAULT NULL COMMENT 'Name of the test suite.',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique identifier of the record',
  PRIMARY KEY (`ID`) COMMENT 'Primary key for test suite records.',
  UNIQUE KEY `UKmol3v5wadw8wduwjr26qjw8ak` (`NANO_ID`)
);

ALTER TABLE AGENT_TEST_CASES ADD COLUMN VARIABLE_MAP TEXT DEFAULT NULL COMMENT 'Variables of system prompt';
ALTER TABLE AGENT_TEST_CASES ADD COLUMN AGENT_ID VARCHAR(50) DEFAULT NULL COMMENT 'AgentId identifier of the agent';
ALTER TABLE AGENT_TEST_CASES ADD COLUMN AGENT_NAME VARCHAR(50) DEFAULT NULL COMMENT 'Name of the agent';

CREATE TABLE `TESTCASES_AGENT_TESTCASES` (
  `TEST_SUITE_ID` int NOT NULL COMMENT 'Foreign key to the associated test suite.',
  `AGENT_TESTCASE_ID` bigint NOT NULL COMMENT 'Foreign key to the associated agent test case',
  PRIMARY KEY (`TEST_SUITE_ID`,`AGENT_TESTCASE_ID`),
  UNIQUE KEY `UK2firg1w8uo64y9xt9ihqndp7j` (`AGENT_TESTCASE_ID`),
  CONSTRAINT `FK2a21al663vh7tjxiy8v6hoett` FOREIGN KEY (`AGENT_TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`),
  CONSTRAINT `FKcu9b3myw6h01vvebfe14xsi8a` FOREIGN KEY (`TEST_SUITE_ID`) REFERENCES `TEST_SUITE` (`ID`)
);

ALTER TABLE TEST_SUITE ADD COLUMN AGENT_NAME VARCHAR(50) DEFAULT NULL COMMENT 'Name of the agent associated with the test suite';
ALTER TABLE TEST_SUITE ADD COLUMN AGENT_ID VARCHAR(50) DEFAULT NULL COMMENT 'ID of the agent associated with the test suite';

CREATE TABLE `TEST_EXECUTION` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key for test execution records.',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test execution was created.',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating if the test execution is deleted.',
  `FAIL` int DEFAULT NULL COMMENT 'Number of failed test cases.',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp of the last modification of the test execution.',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique identifier of the test execution.',
  `PASS` int DEFAULT NULL COMMENT 'Number of passed test cases.',
  `PERCENTAGE` double DEFAULT NULL COMMENT 'Percentage of passed test cases.',
  `RESULT` varchar(2000) DEFAULT NULL COMMENT 'Overall result of the test execution (e.g., PASS/FAIL).',
  `TOTAL_TEST_CASES` int DEFAULT NULL COMMENT 'Total number of test cases executed.',
  PRIMARY KEY (`ID`) COMMENT 'Primary key for test execution records.'
);

ALTER TABLE TEST_EXECUTION ADD COLUMN TEST_SUITE_ID VARCHAR(50) DEFAULT NULL COMMENT 'Test Suite Identifier';

ALTER TABLE AGENT_TEST_CASES ADD COLUMN AGENT_TYPE VARCHAR(50) DEFAULT NULL COMMENT 'Type of associated agent';
ALTER TABLE TEST_SUITE ADD COLUMN AGENT_TYPE VARCHAR(50) DEFAULT NULL COMMENT 'Type of associated agent';

ALTER TABLE ASSERTIONS DROP CONSTRAINT FKew18w4dilss6dmpsuup0rce1k;
ALTER TABLE ASSERTIONS DROP COLUMN TESTCASE_ID;
ALTER TABLE TEST_EXECUTION MODIFY COLUMN `RESULT` LONGTEXT DEFAULT NULL COMMENT 'Overall result of the test execution.';

-- 27 June
ALTER TABLE PROMPT ADD COLUMN REASONING_EFFORT VARCHAR (250) DEFAULT NULL COMMENT 'Reasoning effort required for the prompt';

-- 30 June
CREATE TABLE `AGENT_TESTCASE_ASSERTIONS` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the mapping between agent test case and assertion',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when this mapping was created',
  `EXPECTED_VALUE` text COMMENT 'Expected value specific to this assertion within the test case',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when this mapping was last modified',
  `AGENT_TESTCASE_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the agent test case',
  `ASSERTION_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the assertion type',
  PRIMARY KEY (`ID`),
  KEY `FKimkyod15ovv91col9ch50hk59` (`AGENT_TESTCASE_ID`),
  KEY `FKtoo0m4yg3r00vgl3fkee3sq3b` (`ASSERTION_ID`),
  CONSTRAINT `FKimkyod15ovv91col9ch50hk59` FOREIGN KEY (`AGENT_TESTCASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`),
  CONSTRAINT `FKtoo0m4yg3r00vgl3fkee3sq3b` FOREIGN KEY (`ASSERTION_ID`) REFERENCES `ASSERTIONS` (`ID`)
);

-- 1 JULY
ALTER TABLE TEST_EXECUTION DROP COLUMN `TEST_SUITE_ID`;
ALTER TABLE TEST_EXECUTION ADD COLUMN `TEST_SUITE_ID` int DEFAULT NULL;
ALTER TABLE TEST_EXECUTION ADD CONSTRAINT `FKhfsaxy411932bh3qw9u8mfld4` FOREIGN KEY (`TEST_SUITE_ID`) REFERENCES `TEST_SUITE` (`ID`);

ALTER TABLE TEST_SUITE ADD CONSTRAINT UNIQUE_NANO_ID_TEST_SUITE UNIQUE (NANO_ID) COMMENT 'Unique identifier of the test suite';
ALTER TABLE TEST_EXECUTION ADD CONSTRAINT UNIQUE_NANO_ID_TEST_EXECUTION UNIQUE (NANO_ID);

ALTER TABLE AGENT_TESTCASE_ASSERTIONS ADD COLUMN TOOL_NAME VARCHAR(100) DEFAULT NULL COMMENT 'Name of the tool associated with the assertion';

ALTER TABLE TEST_SUITE ADD CONSTRAINT `UNIQUE_TEST_SUITE_NAME` UNIQUE (NAME) COMMENT 'Constraint for unique test suite name';
ALTER TABLE TEST_EXECUTION ADD COLUMN `ASSERTION_FAIL` int DEFAULT NULL COMMENT 'Number of failed assertions.';
ALTER TABLE TEST_EXECUTION ADD COLUMN `ASSERTION_PASS` int DEFAULT NULL COMMENT 'Number of passed assertions.';
ALTER TABLE `ASSERTIONS` MODIFY COLUMN `MATCH_STRATEGY` enum('CONTAINS','EXACT','REGEX','EVALUATION') NOT NULL COMMENT 'Strategy used to compare actual and expected values.';

--4 July
CREATE TABLE `TEST_RESULT` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the test result',
  `ACCURACY` double DEFAULT NULL COMMENT 'Overall accuracy of the test suite execution',
  `CONSISTENCY` double DEFAULT NULL COMMENT 'Consistency score over multiple runs',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test result was created',
  `DELETED` bit(1) DEFAULT NULL COMMENT 'Flag indicating soft delete status',
  `DESCRIPTION` varchar(1200) DEFAULT NULL COMMENT 'Human-readable description of the test execution',
  `EXECUTION_ID` varchar(50) DEFAULT NULL COMMENT 'External execution tracking identifier',
  `LATENCY` bigint DEFAULT NULL COMMENT 'Average latency across all test cases',
  `MODEL` varchar(30) DEFAULT NULL COMMENT 'Model used for the test execution (e.g., GPT-4)',
  `MODIFIED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the record was last modified',
  `NANO_ID` varchar(50) DEFAULT NULL COMMENT 'Unique nanoid for UI/client-level tracking',
  `NUMBER_OF_RUNS` int DEFAULT NULL COMMENT 'Number of times the test suite was executed',
  `TEST_CASES_FAILED` int DEFAULT NULL COMMENT 'Number of test cases that failed',
  `TEST_CASES_PASSED` int DEFAULT NULL COMMENT 'Number of test cases that passed',
  `TEST_SUITE_ID` int DEFAULT NULL COMMENT 'Foreign key referencing the test suite',
  `STATUS` varchar(20) DEFAULT NULL COMMENT 'Execution status (e.g., PASSED, FAILED)',
  PRIMARY KEY (`ID`),
  KEY `FKsbcvlk0s5wfmirfqlqrxk98xg` (`TEST_SUITE_ID`),
  CONSTRAINT `FKsbcvlk0s5wfmirfqlqrxk98xg` FOREIGN KEY (`TEST_SUITE_ID`) REFERENCES `TEST_SUITE` (`ID`)
);

CREATE TABLE `TEST_CASE_RESULT` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the test case result',
  `ACCURACY` double DEFAULT NULL COMMENT 'Accuracy of the specific test case execution',
  `ASSERTIONS_FAILED` int DEFAULT NULL COMMENT 'Number of failed assertions in the test case',
  `ASSERTIONS_PASSED` int DEFAULT NULL COMMENT 'Number of passed assertions in the test case',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the test case result was created',
  `IS_PASSED` bit(1) DEFAULT NULL COMMENT 'True if all assertions in the test case passed',
  `LATENCY` bigint DEFAULT NULL COMMENT 'Latency for this specific test case',
  `TEST_RESULT_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the test result',
  `TEST_CASE_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the original test case definition',
  PRIMARY KEY (`ID`),
  KEY `FK6o46umacd00w4ym4i0ipjlt55` (`TEST_RESULT_ID`),
  KEY `FKgape278nx21va01o1njhu51gc` (`TEST_CASE_ID`),
  CONSTRAINT `FK6o46umacd00w4ym4i0ipjlt55` FOREIGN KEY (`TEST_RESULT_ID`) REFERENCES `TEST_RESULT` (`ID`),
  CONSTRAINT `FKgape278nx21va01o1njhu51gc` FOREIGN KEY (`TEST_CASE_ID`) REFERENCES `AGENT_TEST_CASES` (`ID`)
);

CREATE TABLE `ASSERTION_RESULT` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'Primary key of the assertion result',
  `ACCURACY_SCORE` double DEFAULT NULL COMMENT 'Accuracy score of this specific assertion',
  `ACTUAL` text COMMENT 'Actual output received from the model',
  `ASSERTION_TYPE` varchar(30) DEFAULT NULL COMMENT 'Type of assertion (e.g., EXACT_MATCH, SEMANTIC)',
  `COMMENT` text COMMENT 'Reviewer or system comment for the assertion',
  `CREATED_TIME` datetime(6) DEFAULT NULL COMMENT 'Timestamp when the assertion result was created',
  `EXPECTED` text COMMENT 'Expected output for the assertion',
  `IS_PASSED` bit(1) DEFAULT NULL COMMENT 'Whether the assertion passed',
  `MATCH_STRATEGY` varchar(30) DEFAULT NULL COMMENT 'Strategy used to match actual and expected outputs',
  `USER_QUESTION` text COMMENT 'Original user query or prompt',
  `TEST_CASE_RESULT_ID` bigint DEFAULT NULL COMMENT 'Foreign key referencing the test case result',
  PRIMARY KEY (`ID`),
  KEY `FK53iu7xtygddsyoi3ansrh89l6` (`TEST_CASE_RESULT_ID`),
  CONSTRAINT `FK53iu7xtygddsyoi3ansrh89l6` FOREIGN KEY (`TEST_CASE_RESULT_ID`) REFERENCES `TEST_CASE_RESULT` (`ID`)
);

DROP TABLE TEST_EXECUTION;
ALTER TABLE TEST_RESULT ADD CONSTRAINT UNIQUE_NANO_ID_TEST_RESULT UNIQUE (NANO_ID) COMMENT 'Unique identifier of the test result.';

--5 July
ALTER TABLE ASSERTIONS ADD CONSTRAINT UNIQUE_ASSERTION UNIQUE (ASSERTION_TYPE, MATCH_STRATEGY) COMMENT 'Ensures no duplicate assertion type + strategy combination';
ALTER TABLE ASSERTIONS DROP COLUMN EXPECTED_VALUE;
ALTER TABLE AGENT_TESTCASE_ASSERTIONS ADD CONSTRAINT UNIQUE_AGENT_TESTCASE_ASSERTIONS UNIQUE (AGENT_TESTCASE_ID, ASSERTION_ID) COMMENT 'Ensures each assertion is used only once per agent test case';
ALTER TABLE AGENT_TEST_CASES ADD CONSTRAINT UNIQUE_NAME_AGENT_TEST_CASES UNIQUE (NAME) COMMENT 'Ensures unique test case names for validation and display';
ALTER TABLE TESTCASES_AGENT_TESTCASES DROP FOREIGN KEY FK2a21al663vh7tjxiy8v6hoett;
ALTER TABLE TESTCASES_AGENT_TESTCASES DROP INDEX UK2firg1w8uo64y9xt9ihqndp7j;
ALTER TABLE TESTCASES_AGENT_TESTCASES ADD CONSTRAINT FK2a21al663vh7tjxiy8v6hoett FOREIGN KEY (AGENT_TESTCASE_ID) REFERENCES AGENT_TEST_CASES(ID);

--07 July
ALTER TABLE TICKET_TRIGGER ADD COLUMN NANO_ID VARCHAR(50) UNIQUE COMMENT 'Unique identifier of the record';
ALTER TABLE ASSERTION_RESULT ADD COLUMN NANO_ID VARCHAR(50) UNIQUE COMMENT 'Unique identifier of the record';
ALTER TABLE TEST_CASE_RESULT ADD COLUMN NANO_ID VARCHAR(50) UNIQUE COMMENT 'Unique identifier of the record';

--08 July
ALTER TABLE TEST_RESULT ADD COLUMN PROVIDER VARCHAR(30) DEFAULT NULL COMMENT 'Provider of the executed test';
ALTER TABLE TEST_CASE_RESULT ADD COLUMN FAILURE_REASON TEXT DEFAULT NULL COMMENT 'Reason for failure of test-case in case of error';
ALTER TABLE TEST_CASE_RESULT ADD COLUMN `IS_ERROR` bit(1) DEFAULT NULL COMMENT 'True if an error occurred in test case';
ALTER TABLE TEST_CASE_RESULT ADD COLUMN `RUN_NUMBER` int DEFAULT NULL COMMENT 'Run number of test cases of a specific execution';

ALTER TABLE TEST_RESULT MODIFY COLUMN `MODEL` varchar(50) DEFAULT NULL COMMENT 'Model used for the test execution (e.g., GPT-4)';

--09 july
ALTER TABLE TEST_CASE_RESULT ADD COLUMN PROMPT_TOKEN INT DEFAULT NULL COMMENT 'Token count used for prompt';
ALTER TABLE TEST_CASE_RESULT ADD COLUMN GENERATION_TOKENS INT DEFAULT NULL COMMENT 'Token count used for generation';
ALTER TABLE TEST_RESULT ADD COLUMN PROMPT_TOKEN INT DEFAULT NULL COMMENT 'Aggregated prompt tokens used in the test result';
ALTER TABLE TEST_RESULT ADD COLUMN GENERATION_TOKENS INT DEFAULT NULL COMMENT 'Aggregated generation tokens used in the test result';

ALTER TABLE ASSERTION_RESULT ADD COLUMN PARAMETER_NAME VARCHAR(50) DEFAULT NULL COMMENT 'Parameter name of the input schema of tool associated with the assertion';
ALTER TABLE AGENT_TESTCASE_ASSERTIONS ADD COLUMN PARAMETER_NAME VARCHAR(50) DEFAULT NULL COMMENT 'Parameter name of the input schema of tool associated with the assertion';
ALTER TABLE AGENT_TESTCASE_ASSERTIONS DROP FOREIGN KEY FKimkyod15ovv91col9ch50hk59;
ALTER TABLE AGENT_TESTCASE_ASSERTIONS DROP INDEX UNIQUE_AGENT_TESTCASE_ASSERTIONS;
ALTER TABLE AGENT_TESTCASE_ASSERTIONS ADD CONSTRAINT UNIQUE_AGENT_TESTCASE_ASSERTIONS UNIQUE (AGENT_TESTCASE_ID, ASSERTION_ID, PARAMETER_NAME);
ALTER TABLE AGENT_TESTCASE_ASSERTIONS ADD CONSTRAINT FKimkyod15ovv91col9ch50hk59 FOREIGN KEY (AGENT_TESTCASE_ID) REFERENCES AGENT_TEST_CASES(ID);
