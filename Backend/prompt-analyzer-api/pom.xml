<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.enttribe</groupId>
        <artifactId>prompt-analyzer</artifactId>
        <version>1.2.0</version>
    </parent>

    <artifactId>prompt-analyzer-api</artifactId>
    <packaging>jar</packaging>
    <name>Prompt Analyzer API</name>

    <dependencies>
        <dependency>
            <groupId>com.enttribe.platform</groupId>
            <artifactId>one-platform-api</artifactId>
            <version>7.0.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jnr</groupId>
                    <artifactId>jnr-posix</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-csv</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.owasp.esapi</groupId>
                    <artifactId>esapi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-cassandra</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.kafka</groupId>
                    <artifactId>spring-kafka</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-cassandra</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.xmlgraphics</groupId>
                    <artifactId>batik-transcoder</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.cloud</groupId>
                    <artifactId>google-cloud-pubsub</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-cassandra</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-vault-config</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-cassandra</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jdk8</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jsr310</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-redis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.enttribe.commons</groupId>
                    <artifactId>detrace-log</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.ben-manes.caffeine</groupId>
                    <artifactId>caffeine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-log4j2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jakarta.mail</groupId>
                    <artifactId>jakarta.mail-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.mail</groupId>
                    <artifactId>jakarta.mail</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-validation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.opentracing.contrib</groupId>
                    <artifactId>opentracing-spring-jaeger-cloud-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.micrometer</groupId>
                    <artifactId>micrometer-registry-prometheus</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.opentelemetry</groupId>
                    <artifactId>opentelemetry-exporter-otlp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.opentelemetry</groupId>
                    <artifactId>io.opentelemetry</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.opentelemetry</groupId>
                    <artifactId>opentelemetry-sdk-extension-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-fileupload</groupId>
                    <artifactId>commons-fileupload</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-fileupload</groupId>
                    <artifactId>commons-fileupload</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>reposilite-repository-releases</id>
            <url>https://artifact.visionwaves.com/releases</url>
        </repository>
    </distributionManagement>
</project>
