/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.aspect;

import com.enttribe.product.security.spring.userdetails.CustomerInfo;
import com.enttribe.promptanalyzer.constants.FilterConstants;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.hibernate.Filter;
import org.hibernate.Session;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.ResolvableType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "prompt.saas", name = "enable", havingValue = "true")
public class TenantFilterAspect {

  private final CustomerInfo customerInfo;
  @PersistenceContext private EntityManager entityManager;

  @Before("execution(* com.enttribe.promptanalyzer.dao..*(..))")
  public void applyTenantFilter(JoinPoint joinPoint) {
    log.debug("called applyTenantFilter");
    Object target = joinPoint.getTarget();
    Class<?> daoClass = target.getClass();

    // Try to resolve the generic parameter (entity class)
    ResolvableType resolvableType = ResolvableType.forClass(daoClass).as(JpaRepository.class);
    Class<?> entityClass = resolvableType.getGeneric(0).resolve();
    Class<?> superclass = entityClass.getSuperclass();
    if (superclass == null) {
      return;
    }
    this.applyFilter(superclass.getName());
  }

  private void applyFilter(String filterType) {
    log.info("applyFilter called");

    try {
      if (filterType.contains("BaseEntityGlobal")) {
        Session session = this.entityManager.unwrap(Session.class);
        if (session != null) {
          Filter filter = session.enableFilter(FilterConstants._CUSTOMER_ID_FILTER);
          log.info("_CUSTOMER_ID_FILTER: {}", this.customerInfo.getCustomerWrapper().getId());
          filter.setParameter("customerId", this.customerInfo.getCustomerWrapper().getId());
        }
      } else {
        log.info("BaseEntity not extended or not found");
      }
    } catch (Exception e) {
      log.error("error occurred in applyFilter : {}", e.getMessage(), e);
      throw new RuntimeException(e);
    }
  }
}
