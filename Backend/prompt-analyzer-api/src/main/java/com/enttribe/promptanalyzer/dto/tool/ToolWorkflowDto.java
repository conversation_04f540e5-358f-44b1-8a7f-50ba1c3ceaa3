/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.tool;

import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents workflow-specific tool configurations and
 * parameters. Manages tool workflow metadata, authentication, and required parameters for workflow
 * integration. Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
public class ToolWorkflowDto {

  private String applicationName;
  private String category;
  private String name;
  private String description;
  private String displayName;
  private String hostName;
  private ToolAuthDto toolAuthentication;
  private List<Map<String, String>> requiredParameters;
  private String status;
  private String tags;
  private String toolImage;
  private Boolean returnDirect;
}
