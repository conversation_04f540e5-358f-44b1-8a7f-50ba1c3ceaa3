/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.*;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a trigger configuration. Stores details about triggers, including their
 * properties and metadata.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@Entity
@Table(name = "TICKET_TRIGGER")
public class Trigger {

  /** Unique ID of the trigger. */
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Id
  @Column(name = "ID", columnDefinition = "INT")
  private Integer id;

  /** Description of the trigger. */
  @Column(name = "DESCRIPTION")
  private String description;

  /** Name of the trigger. */
  @Column(name = "NAME")
  private String name;

  /** Display name of the trigger. */
  @Column(name = "DISPLAY_NAME")
  private String displayName;

  /** Metadata associated with the trigger. */
  @Column(name = "TRIGGER_META_DATA", columnDefinition = "LONGTEXT")
  private String metadata;

  /** Indicates if the trigger is marked as deleted. */
  @Column(name = "DELETED")
  private Boolean deleted = false;

  /** Application associated with the trigger. */
  @Column(name = "APPLICATION")
  private String application;

  /** Entity related to the trigger. */
  @Column(name = "ENTITY")
  private String entity;

  /** Type of the trigger. */
  @Column(name = "TYPE")
  private String type;

  @Column(name = "NANO_ID", length = 50)
  private String nanoId;

  /** Timestamp when the trigger was created. */
  @Column(name = "CREATED_TIME")
  private Date createdTime;

  /** Timestamp when the trigger was last modified. */
  @Column(name = "MODIFIED_TIME")
  private Date modifiedTime;
}
