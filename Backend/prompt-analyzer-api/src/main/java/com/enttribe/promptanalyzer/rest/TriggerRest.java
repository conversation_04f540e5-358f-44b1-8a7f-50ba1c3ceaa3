/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * REST controller for managing trigger operations. Provides endpoints for creating, updating,
 * searching, and counting triggers. All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "TriggerRest",
    url = "${prompt-analyzer-service.url}",
    path = "/trigger",
    primary = false)
public interface TriggerRest {

  /**
   * Searches for triggers with pagination and sorting options. Requires ROLE_API_TRIGGER_READ
   * security role.
   *
   * @param filter Optional filter criteria for searching triggers
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching triggers as TriggerResponseDto objects
   * @apiNote Response Codes: 200 - Search Trigger successfully 500 - Error occurred during search
   */
  @GetMapping(path = "/search")
  @Operation(
      summary = "Search Trigger",
      description =
          "Searches for triggers with support for filtering, pagination, and sorting options.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TRIGGER_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description =
                "Search Trigger successfully based on the provided parameters like filtering, and sorting"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  List<TriggerResponseDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);
}
