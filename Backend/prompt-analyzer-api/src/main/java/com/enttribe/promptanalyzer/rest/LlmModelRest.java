/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.LlmModelSdkDto;
import com.enttribe.promptanalyzer.dto.prompt.ProviderModelDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * REST controller for managing LLM (Language Learning Model) operations. Provides endpoints for
 * creating and retrieving LLM models, with security requirements for each operation. Supports SDK
 * integration and provider-specific queries.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "LlmModelRest",
    url = "${prompt-analyzer-service.url}",
    path = "/llm-model",
    primary = false)
public interface LlmModelRest {

  /**
   * Retrieves models for a specific provider. Requires ROLE_API_LLMMODEL_GET_BY_PROVIDER security
   * role.
   *
   * @param provider The provider name to get models for
   * @return ProviderModelDto containing the provider's models
   * @apiNote Response Codes: 200 - Successfully retrieves all LLM models for the provider 500 -
   *     Error occurred during retrieval
   */
  @Operation(
      summary = "Get models by provider",
      description =
          "Retrieves all LLM models associated with a specific provider, including their configurations and capabilities.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_LLMMODEL_GET_BY_PROVIDER})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description =
                "Successfully retrieves all LLM models associated with a specific provider"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/getModelsByProvider")
  ProviderModelDto getModelsByProvider(String provider);

  /**
   * Retrieves all providers and their associated models. Requires ROLE_API_LLMMODEL_GET_ALL
   * security role.
   *
   * @return List of ProviderModelDto containing all providers and their models
   * @apiNote Response Codes: 200 - Successfully retrieved all providers and models 500 - Error
   *     occurred during retrieval
   */
  @Operation(
      summary = "Get all providers and models",
      description =
          "Retrieves a comprehensive list of all providers and their associated LLM models in the system.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_LLMMODEL_GET_ALL})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Successfully retrieved all providers and models"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/all")
  List<ProviderModelDto> getAllProvidersWithModels();

  /**
   * Retrieves LLM models for SDK integration by application name. Requires
   * ROLE_API_LLMMODEL_GET_FOR_SDK security role.
   *
   * @param appName The application name to get models for
   * @return List of LlmModelSdkDto for the specified application
   * @apiNote Response Codes: 200 - Successfully retrieved models for SDK 500 - Error occurred
   *     during retrieval
   */
  @Operation(
      summary = "Get LLM models for SDK by app name",
      description =
          "Retrieves LLM models configured for SDK integration, filtered by the specified application name.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_LLMMODEL_GET_FOR_SDK})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Retrieves LLM models for SDK integration filtered by application name"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/getLlmModelsForSDK/{appName}")
  List<LlmModelSdkDto> getLlmModelsForSDK(@PathVariable String appName);

  /**
   * Retrieves LLM models by type for SDK integration. Requires ROLE_API_LLMMODEL_GET_BY_TYPE
   * security role.
   *
   * @param type The type of models to retrieve
   * @return List of LlmModelSdkDto matching the specified type
   * @apiNote Response Codes: 200 - Successfully retrieved models by type 500 - Error occurred
   *     during retrieval
   */
  @Operation(
      summary = "Get LLM models by type",
      description =
          "Retrieves LLM models filtered by their type, specifically configured for SDK integration.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_LLMMODEL_GET_BY_TYPE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Successfully retrieved models by using type for SDK"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/getLlmModelsByType/type/{type}")
  List<LlmModelSdkDto> getLlmModelsByTypeForSDK(@PathVariable String type);
}
