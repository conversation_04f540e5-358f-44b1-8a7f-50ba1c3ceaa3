/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.prompt;

import com.enttribe.promptanalyzer.model.Message;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents a complete prompt configuration with validation
 * constraints. Used for creating and updating prompt configurations with parameter validation.
 * Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class PromptDto {

  private String id;
  private String application;
  private String category;
  private String promptId;
  private String status;

  @DecimalMin(value = "0.0", message = "Temperature must be greater than or equal to 0.0")
  @DecimalMax(value = "2.0", message = "Temperature must be less than or equal to 2.0")
  private Double temperature;

  @Min(value = 1, message = "Max tokens must be at least 1 and cannot be negative")
  private Integer maxTokens;

  private String version;

  @DecimalMin(value = "0.0", message = "topP must be greater than or equal to 0.0")
  @DecimalMax(value = "1.0", message = "topP must be less than or equal to 1.0")
  private Double topP;

  private String name;
  private String type;
  private String assertionTemplate;
  private String defaultFormat;
  private List<Message> messages;
  private String model;
  private String inference;
  private String provider;
  private Boolean jsonMode;
  private Boolean llmGuard;
  private String tags;
  private String reasoningEffort;
}
