/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.knowledge_base;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * A Data Transfer Object (DTO) that represents the response from knowledge base operations.
 * Contains comprehensive information about knowledge base entries including vectors and metadata.
 * Author: VisionWaves Version: 1.0
 */
@Builder
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class KnowledgeBaseResponseDto {

  private String id;
  private String name;
  private String description;
  private Map<String, byte[]> byteCodeMap;
  private String docId;
  private String docMetaData;
  private String vectorMetaData;
  private byte[] documentVector;
  private Boolean isContext;
  private Integer topK;
  private Double similarityThreshold;
  private String type;
  private String className;
  private String fileName;
  private String filter;
  private String websiteUrl;
  private String collectionName;
  private String tags;
  private String integration;
  private List<String> tables;
  private Boolean returnDirect;
  private String s3FileNames;
}
