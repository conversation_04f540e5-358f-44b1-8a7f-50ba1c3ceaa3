/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.HashMap;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * REST controller for managing tool callback provider operations. Provides endpoints for retrieving
 * tool callback information.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "ToolCallbackProviderRest",
    url = "${prompt-analyzer-service.url}",
    primary = false)
public interface ToolCallbackProviderRest {

  /**
   * Fetches a list of tools from the external service. Requires ROLE_API_TOOL_CALLBACK_READ
   * security role.
   *
   * @return A list of tools as a list of hash maps.
   */
  @Operation(
      summary = "Get list of tools",
      description = "provides list of MCP tools",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TOOL_CALLBACK_GET_LIST})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tools retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/getListOfTools")
  List<HashMap<String, String>> getToolCallbackProvider();
}
