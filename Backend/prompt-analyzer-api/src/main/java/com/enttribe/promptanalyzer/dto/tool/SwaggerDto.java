/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.tool;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents Swagger/OpenAPI specification details and
 * configurations. Handles API documentation, endpoint information, and related metadata for
 * Swagger-documented services. Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SwaggerDto {

  private String name;
  private String description;
  private String url;
  private String httpMethod;
  private String parameters;
  private String hostname;
  private String swaggerJson;
  private String applicationName;
  private String category;
  private String status;
  private String tags;
  private ToolAuthDto toolAuthentication;
  private String toolImage;
  private Boolean returnDirect;
}
