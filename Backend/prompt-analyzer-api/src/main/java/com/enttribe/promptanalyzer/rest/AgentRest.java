/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.dto.nififlow.NifiFlowDto;
import com.enttribe.promptanalyzer.model.AgentHistory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing agent-related operations. Provides endpoints for creating agents,
 * searching agents, and managing NiFi triggers.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "AgentRest",
    url = "${prompt-analyzer-service.url}",
    path = "/agent",
    primary = false)
public interface AgentRest {

  @Operation(
      summary = "Executes test suite for agent",
      description = "Executes test suite for agent with set of test cases",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_EXECUTE_TEST_SUITE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Plan generated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/executeTestSuite")
  Map<String, String> executeTestSuite(@RequestBody Map<String, Object> request);

  @Operation(
      summary = "Re-executes test case of a test-suite",
      description = "Re-executes test case of a test-suite that has failed due to an error",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_EXECUTE_TEST})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Test re-executed successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/reExecuteTest")
  Map<String, String> reExecuteTest(@RequestBody Map<String, Object> request);

  @Operation(
      summary = "Gives input schema",
      description = "Gives input schema of a tool based on its name.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_GET_INPUT_SCHEMA})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Plan generated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/getInputSchema")
  List<String> getInputSchema(@RequestBody Map<String, String> request);

  /**
   * Generates a plan based on user query for custom agent. Requires agent plan creation
   * permissions.
   *
   * @param agent Custom agent details containing the user query
   * @return Map containing the generated plan response
   */
  @Operation(
      summary = "Generate plan for user query",
      description =
          "Analyzes a user query and generates an execution plan for a custom agent. This endpoint processes natural language input to create structured action plans for agent execution.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_PLAN_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Plan generated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("getPlanforUserQuery")
  Map<String, String> getPlanforUserQuery(@RequestBody CustomAgentDto agent);

  /**
   * Retrieves agent execution history by process group ID. Requires agent history access
   * permissions.
   *
   * @param processGroupId The process group ID to get history for
   * @return AgentHistory containing the execution history
   */
  @Operation(
      summary = "Get agent execution history",
      description =
          "Retrieves the complete execution history for an agent based on its process group ID. Provides detailed information about past agent activities and outcomes.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_GET_HISTORY})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Agent history retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/getAgentHistory")
  AgentHistory getAgentHistory(@RequestParam(name = "processGroupId") String processGroupId);

  /**
   * Creates a trigger in NiFi based on flow configuration. Requires agent trigger creation
   * permissions.
   *
   * @param flowDto Flow configuration details
   * @return Map containing the result of trigger creation
   */
  @Operation(
      summary = "Create trigger in NiFi",
      description =
          "Creates a new trigger in NiFi based on the provided flow configuration. This endpoint sets up automated triggers for agent actions within the NiFi workflow system.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_TRIGGER_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Trigger created successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/createTriggerInNifi")
  Map<String, String> createTriggerInNifi(@RequestBody NifiFlowDto flowDto);

  /**
   * Generates a trigger name and description based on user query. Requires agent trigger name
   * creation permissions.
   *
   * @param requestBody Map containing the user query under "userQuery" key
   * @return Map containing the generated trigger name and description
   */
  @Operation(
      summary = "Create trigger name",
      description =
          "Generates a meaningful trigger name and description based on the user's query. This endpoint helps create human-readable identifiers for automated triggers.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_TRIGGER_NAME_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Trigger name created successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/createTriggerName")
  Map<String, String> createTriggerName(@RequestBody Map<String, String> requestBody);
}
