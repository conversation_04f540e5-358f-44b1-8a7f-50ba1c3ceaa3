/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.exception;

/**
 * Exception thrown to indicate a business logic error. Extends {@link RuntimeException} to provide
 * unchecked exception handling for business-related errors.
 *
 * <AUTHOR>
 * @version 1.0
 */
public class BusinessException extends RuntimeException {

  /**
   * Constructs a new BusinessException with the specified detail message.
   *
   * @param message the detail message
   */
  public BusinessException(String message) {
    super(message);
  }

  /**
   * Constructs a new BusinessException with the specified detail message and cause.
   *
   * @param message the detail message
   * @param cause the cause of the exception
   */
  public BusinessException(String message, Throwable cause) {
    super(message, cause);
  }
}
