/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Trigger DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to trigger
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for trigger
 * configuration, management, and execution.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Trigger configuration and management DTOs</li>
 *   <li>Support for trigger execution and automation</li>
 *   <li>Facilitation of trigger data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Trigger DTOs are used in controllers, services, and clients to transfer trigger-related
 * data for automation and event-driven operations.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.trigger;
