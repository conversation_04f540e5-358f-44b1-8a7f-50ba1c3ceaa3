/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.core.generic.exceptions.application.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.Map;

/**
 * JPA attribute converter for converting a map of byte arrays to a JSON string and vice versa.
 * Facilitates the storage of bytecode maps in a database column.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Converter
public class ByteCodeMapConverter implements AttributeConverter<Map<String, byte[]>, String> {

  /**
   * Converts a map of byte arrays to a JSON string for database storage.
   *
   * @param attribute the map of byte arrays to convert
   * @return the JSON string representation of the map
   * @throws BusinessException if an error occurs during serialization
   */
  @Override
  public String convertToDatabaseColumn(Map<String, byte[]> attribute) {
    try {
      return new ObjectMapper().writeValueAsString(attribute);
    } catch (JsonProcessingException e) {
      throw new BusinessException("Error serializing bytecode map", e);
    }
  }

  /**
   * Converts a JSON string from the database to a map of byte arrays.
   *
   * @param dbData the JSON string from the database
   * @return the map of byte arrays
   * @throws BusinessException if an error occurs during deserialization
   */
  @Override
  public Map<String, byte[]> convertToEntityAttribute(String dbData) {
    try {
      return new ObjectMapper().readValue(dbData, new TypeReference<>() {});
    } catch (JsonProcessingException e) {
      throw new BusinessException("Error deserializing bytecode map", e);
    }
  }
}
