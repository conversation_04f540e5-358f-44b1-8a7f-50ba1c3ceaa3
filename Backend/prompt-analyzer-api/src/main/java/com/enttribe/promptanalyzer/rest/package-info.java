/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * REST API package for the Prompt Analyzer application.
 *
 * <p>This package contains REST controller and client interfaces for exposing and consuming
 * API endpoints in the Prompt Analyzer application. These interfaces define the contract
 * for HTTP-based communication with the application's services.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Spring REST controller and Feign client interfaces</li>
 *   <li>API endpoint definitions for CRUD and business operations</li>
 *   <li>Support for OpenAPI/Swagger documentation</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>REST interfaces are used by clients and other services to interact with the Prompt Analyzer
 * application's business logic and data. They provide a standardized way to access application
 * functionality over HTTP.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.rest;
