/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Knowledge Base DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to knowledge base
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for knowledge
 * base requests, responses, and document management.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Knowledge base request and response DTOs</li>
 *   <li>Support for document and knowledge management</li>
 *   <li>Facilitation of knowledge base data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Knowledge base DTOs are used in controllers, services, and clients to transfer knowledge-related
 * data for storage, retrieval, and management purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.knowledge_base;
