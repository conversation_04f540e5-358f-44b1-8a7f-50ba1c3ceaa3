/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.trigger;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents the response for trigger operations. Extends
 * TriggerRequestDto with additional response-specific fields for comprehensive trigger information.
 * Author: VisionWaves Version: 1.0
 */
@Setter
@Getter
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class TriggerResponseDto {

  private String id;
  private String description;
  private String name;
  private String displayName;
  private String metadata;
  private String application;
  private String entity;
  private String type;
}
