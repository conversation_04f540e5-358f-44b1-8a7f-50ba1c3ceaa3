/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.assertion;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.enttribe.promptanalyzer.model.AgentTestCase;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for assertion search responses. Contains a list of assertions and pagination
 * information.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssertionResponseDto {

  private String id;
  private List<AgentTestCase> testCase;
  private AssertionType assertionType;
  private MatchStrategy matchStrategy;
  private String expectedValue;
  private String description;
  private Boolean deleted = false;
  private Date createdTime;
  private Date modifiedTime;
}
