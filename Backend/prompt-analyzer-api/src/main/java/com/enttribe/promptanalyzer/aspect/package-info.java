/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */

/**
 * Package containing Aspect-Oriented Programming (AOP) components for cross-cutting concerns.
 *
 * <p>This package provides aspects that handle cross-cutting concerns across the application, such
 * as tenant filtering, security, logging, and other functionality that needs to be applied
 * consistently across multiple components without modifying their core logic.
 *
 * <p>The aspects in this package are designed to:
 *
 * <ul>
 *   <li>Implement cross-cutting concerns using Spring AOP
 *   <li>Provide automatic tenant filtering for multi-tenant applications
 *   <li>Handle security and access control aspects
 *   <li>Maintain separation of concerns by keeping cross-cutting logic separate
 * </ul>
 *
 * <p>Key components in this package:
 *
 * <ul>
 *   <li>{@link com.enttribe.promptanalyzer.aspect.TenantFilterAspect} - Aspect that automatically
 *       applies tenant filtering to DAO operations, ensuring data isolation between different
 *       customers in a multi-tenant environment. This aspect intercepts DAO method calls and
 *       applies customer-specific filters to ensure data security and isolation.
 * </ul>
 *
 * <p>Usage Notes:
 *
 * <ul>
 *   <li>Aspects in this package use Spring AOP with AspectJ annotations
 *   <li>Tenant filtering is conditionally enabled based on configuration properties
 *   <li>All aspects include proper logging and error handling
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.aspect;
