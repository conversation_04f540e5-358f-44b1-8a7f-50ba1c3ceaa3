/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.tag;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for tag responses. This class represents the tag data sent from server to
 * client. Uses the Builder pattern for object creation and ignores unknown JSON properties during
 * deserialization.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@RequiredArgsConstructor
@AllArgsConstructor
public class TagResponseDto {

  private String id;
  private String name;
  private String type;
}
