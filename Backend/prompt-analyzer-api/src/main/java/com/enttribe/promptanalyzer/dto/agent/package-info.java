/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Agent DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to agent operations
 * in the Prompt Analyzer application. These DTOs encapsulate request and response data
 * structures for agent APIs and service interactions.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Agent API request and response DTOs</li>
 *   <li>Support for agent management and operations</li>
 *   <li>Facilitation of data transfer between layers</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Agent DTOs are used in controllers, services, and clients to transfer agent-related
 * data in a structured and maintainable way.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.agent;
