/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Audit DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to auditing
 * in the Prompt Analyzer application. These DTOs encapsulate data for audit events,
 * exception tracking, and tool usage monitoring.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Audit event and exception DTOs</li>
 *   <li>Support for tool usage and audit tracking</li>
 *   <li>Facilitation of audit data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Audit DTOs are used in controllers, services, and clients to transfer audit-related
 * data for monitoring and compliance purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.audit;
