/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Function package for the Prompt Analyzer application.
 *
 * <p>This package contains custom annotations and functional utilities used for
 * parameter validation, request body handling, and other advanced features in
 * the Prompt Analyzer application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Custom annotations for validation and schema enforcement</li>
 *   <li>Functional programming utilities</li>
 *   <li>Support for advanced request/response handling</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Annotations and utilities in this package are used to enhance the expressiveness
 * and robustness of the application's API and service layers.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.function;
