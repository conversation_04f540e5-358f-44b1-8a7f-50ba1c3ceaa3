/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.Set;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "TEST_SUITE")
public class TestSuite extends BaseEntityGlobal {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Integer id;

  @Column(name = "NANO_ID", length = 50, unique = true)
  private String nanoId;

  @Column(name = "NAME", length = 50)
  private String name;

  @Column(name = "AGENT_ID", length = 50)
  private String agentId;

  @Column(name = "AGENT_NAME", length = 50)
  private String agentName;

  @Column(name = "AGENT_TYPE", length = 50)
  private String agentType;

  @Column(name = "DELETED")
  private Boolean deleted = false;

  @ManyToMany(mappedBy = "testSuites")
  private Set<AgentTestCase> agentTestCases = new HashSet<>();
}
