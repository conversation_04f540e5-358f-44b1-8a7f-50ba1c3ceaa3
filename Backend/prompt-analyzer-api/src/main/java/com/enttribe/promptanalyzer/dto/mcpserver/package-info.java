/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * MCP Server DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to MCP server
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for MCP server
 * configuration, requests, and responses.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>MCP server configuration and request DTOs</li>
 *   <li>Support for MCP server management</li>
 *   <li>Facilitation of MCP server data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>MCP server DTOs are used in controllers, services, and clients to transfer MCP server-related
 * data for configuration and management purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.mcpserver;
