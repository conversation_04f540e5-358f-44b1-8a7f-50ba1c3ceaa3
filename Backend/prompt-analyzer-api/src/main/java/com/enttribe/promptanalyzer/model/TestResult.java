/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "TEST_RESULT")
public class TestResult {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Long id;

  @ManyToOne
  @JoinColumn(name = "TEST_SUITE_ID")
  private TestSuite testSuite;

  @Column(name = "EXECUTION_ID", length = 50)
  private String executionId;

  @Column(name = "ACCURACY")
  private Double accuracy;

  @Column(name = "LATENCY")
  private Long latency;

  @Column(name = "CONSISTENCY")
  private Double consistency;

  @Column(name = "NUMBER_OF_RUNS")
  private Integer numberOfRuns;

  @Column(name = "TEST_CASES_PASSED")
  private Integer testCasesPassed;

  @Column(name = "TEST_CASES_FAILED")
  private Integer testCasesFailed;

  @Column(name = "PROMPT_TOKEN")
  private Integer promptToken;

  @Column(name = "GENERATION_TOKENS")
  private Integer generationTokens;

  @Column(name = "MODEL", length = 30)
  private String model;

  @Column(name = "PROVIDER", length = 30)
  private String provider;

  @Column(name = "DESCRIPTION", length = 1200)
  private String description;

  @Column(name = "STATUS", length = 20)
  private String status;

  @OneToMany(
      mappedBy = "testResult",
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY)
  private Set<TestCaseResult> testCaseResults = new HashSet<>();

  @Column(name = "NANO_ID", length = 50, unique = true)
  private String nanoId;

  @Column(name = "DELETED")
  private Boolean deleted = false;

  @Column(name = "CREATED_TIME")
  private Date createdTime;

  @Column(name = "MODIFIED_TIME")
  private Date modifiedTime;
}
