/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Tag DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to tag
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for tag
 * creation, management, and categorization.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Tag creation and management DTOs</li>
 *   <li>Support for tag categorization and labeling</li>
 *   <li>Facilitation of tag data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Tag DTOs are used in controllers, services, and clients to transfer tag-related
 * data for categorization and management purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.tag;
