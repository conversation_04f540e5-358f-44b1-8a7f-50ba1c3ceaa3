/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.prompt;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that manages prompt variables and content for different roles in a
 * conversation context. Author: VisionWaves Version: 1.0
 */
@Setter
@Getter
public class PromptVariableDto {

  private String promptName;
  private List<String> userPrompt;
  private List<String> systemPrompt;
  private List<String> assistantPrompt;
}
