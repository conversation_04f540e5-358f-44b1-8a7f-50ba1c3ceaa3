/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.audit;

import com.enttribe.core.generic.utils.BypassValidation;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) class that represents Tool Audit data. Used for transferring TOOL
 * Author: VisionWaves Version: 1.0
 */
@Setter
@Getter
public class ToolAuditDto {
  private Integer id;

  @BypassValidation(reason = "sourceCode contains tags")
  private String requestText;

  @BypassValidation(reason = "sourceCode contains tags")
  private String chatOptions;

  private String auditId;

  @BypassValidation(reason = "sourceCode contains tags")
  private String toolCallRequest;

  @BypassValidation(reason = "sourceCode contains tags")
  private String toolDefinitions;

  @BypassValidation(reason = "sourceCode contains tags")
  private String toolResponse;

  private String agentName;
  private String promptId;
  private String promptName;
  private Integer totalToken;
  private Integer promptToken;
  private Integer generationTokens;
  private Long responseTime;
  private Date creationTime;
  private Date startTime;
  private Date endTime;
  private String model;
  private String provider;
  private String totalCost;
  private String applicationName;
  private String status;

  @BypassValidation(reason = "sourceCode contains tags")
  private String toolCallDefinitions;
}
