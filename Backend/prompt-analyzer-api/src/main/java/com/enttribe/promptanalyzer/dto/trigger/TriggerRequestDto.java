/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.trigger;

import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents trigger creation and update requests. Handles the
 * essential information needed to create or modify trigger configurations in the system. Author:
 * VisionWaves Version: 1.0
 */
@Setter
@Getter
public class TriggerRequestDto {

  private String id;
  private String description;
  private String name;
  private String displayName;
  private String metadata;
}
