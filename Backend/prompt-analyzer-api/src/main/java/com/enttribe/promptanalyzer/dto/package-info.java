/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Data Transfer Object (DTO) package for the Prompt Analyzer application.
 *
 * <p>This package contains DTO classes that are used to transfer data between
 * different layers of the Prompt Analyzer application. These DTOs encapsulate
 * request and response data structures for API endpoints, service interactions,
 * and other application boundaries.</p>
 *
 * <h2>Package Contents:</h2>
 * <ul>
 *   <li><strong>AgentApiResponse</strong> - DTO for agent API responses</li>
 *   <li><strong>AssertionRequestDto</strong> - DTO for assertion requests</li>
 *   <li><strong>KnowledgeBaseRequestDto</strong> - DTO for knowledge base operations</li>
 *   <li><strong>PromptConvertorDto</strong> - DTO for prompt conversion</li>
 *   <li>...and other DTO classes for various features</li>
 * </ul>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Encapsulation of API request and response data</li>
 *   <li>Separation of concerns between layers</li>
 *   <li>Facilitation of serialization and deserialization</li>
 *   <li>Support for validation and documentation</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>DTOs are typically used in controller, service, and client classes to
 * transfer structured data. They help ensure that only necessary data is
 * exposed and transferred between layers, improving maintainability and security.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto;
