/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Test Case DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to test case
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for test case
 * creation, management, and evaluation.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Test case creation and management DTOs</li>
 *   <li>Support for test case evaluation and reporting</li>
 *   <li>Facilitation of test case data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Test case DTOs are used in controllers, services, and clients to transfer test case-related
 * data for testing and quality assurance purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.testcase;
