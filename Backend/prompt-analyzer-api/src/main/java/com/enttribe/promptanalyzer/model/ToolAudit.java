/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity representing an audit record for tool calls. Captures details about tool interactions and
 * responses for auditing purposes.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "TOOL_AUDIT")
public class ToolAudit {

  /** Unique ID of the tool audit record. */
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Id
  @Column(name = "ID")
  @JsonIgnore
  private Integer id;

  /** Request messages for the tool call. */
  @Column(name = "REQUEST_TEXT", columnDefinition = "LONGTEXT")
  private String requestText;

  /** Chat options used for the tool call. */
  @Column(name = "CHAT_OPTIONS", columnDefinition = "TEXT")
  private String chatOptions;

  /** Audit identifier for tracking purposes. */
  @Column(name = "AUDIT_ID")
  private String auditId;

  /** Tool call request details. */
  @Column(name = "TOOL_CALL_REQUEST", columnDefinition = "LONGTEXT")
  private String toolCallRequest;

  /** Tool response details. */
  @Column(name = "TOOL_RESPONSE", columnDefinition = "LONGTEXT")
  private String toolResponse;

  /** Tool definition details. */
  @Column(name = "TOOL_DEFINITION", columnDefinition = "LONGTEXT")
  private String toolDefinitions;

  /** Name of the agent that made the tool call. */
  @Column(name = "AGENT_NAME", length = 100)
  private String agentName;

  /** Prompt ID associated with the tool call. */
  @Column(name = "PROMPT_ID")
  private String promptId;

  /** Name of the prompt associated with the tool call. */
  @Column(name = "PROMPT_NAME")
  private String promptName;

  /** Total number of tokens used. */
  @Column(name = "TOTAL_TOKEN")
  private Integer totalToken;

  /** Number of tokens in the prompt. */
  @Column(name = "PROMPT_TOKEN")
  private Integer promptToken;

  /** Number of tokens generated in the response. */
  @Column(name = "GENERATION_TOKENS")
  private Integer generationTokens;

  /** Time taken to generate the response in milliseconds. */
  @Column(name = "RESPONSE_TIME")
  private Long responseTime;

  /** Timestamp when the audit record was created. */
  @Column(name = "CREATION_TIME")
  private Date creationTime;

  @Column(name = "START_TIME")
  private Date startTime;

  @Column(name = "END_TIME")
  private Date endTime;

  @Column(name = "PROVIDER")
  private String provider;

  @Column(name = "TOTAL_COST", columnDefinition = "DOUBLE DEFAULT 0.0")
  private double totalCost;

  @Column(name = "MODEL")
  private String model;

  @Column(name = "APPLICATION_NAME")
  private String applicationName;

  @Column(name = "STATUS")
  private String status;
}
