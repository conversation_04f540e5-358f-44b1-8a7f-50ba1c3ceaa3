/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.prompt;

import com.enttribe.core.generic.utils.BypassValidation;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that captures audit information for prompt executions. Tracks
 * performance metrics, token usage, and execution details for analysis and monitoring. Author:
 * VisionWaves Version: 1.0
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PromptAuditDto {

  private String id;
  private String model;

  @BypassValidation(reason = "response contains tags")
  private String responseText;

  private Long totalToken;
  private Long promptToken;
  private Double responseTime;
  private Long generationTokens;
  private String applicationName;
  private String category;
  private String promptId;

  @BypassValidation(reason = "sourceCode contains tags")
  private Map<String, String> metaTags;

  @BypassValidation(reason = "sourceCode contains tags")
  private Map<String, Object> chatOptions;

  @BypassValidation(reason = "sourceCode contains tags")
  private String functionArgs; // This is for Agentic audit

  private String auditId;
  private String promptName;
  private Integer httpStatus;

  @BypassValidation(reason = "request contains tags")
  private String requestText;

  private String status;

  @BypassValidation(reason = "error contains tags")
  private String errorMessage;

  @BypassValidation(reason = "sourceCode contains tags")
  private String provider;

  private Date creationTime;
  private Date startTime;
  private Date endTime;
  private String agentName;

  @BypassValidation(reason = "sourceCode contains tags")
  private String toolCallDefinitions;

  private double totalCost;
}
