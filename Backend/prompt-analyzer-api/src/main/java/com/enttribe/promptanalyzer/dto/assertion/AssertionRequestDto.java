/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.assertion;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for creating new assertions. Contains all fields required for assertion
 * creation, excluding auto-generated fields.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class AssertionRequestDto {

  private String id;

  @NotNull(message = "Assertion type is required.")
  private AssertionType assertionType;

  @NotNull(message = "Match strategy is required.")
  private MatchStrategy matchStrategy;

  @NotBlank(message = "Expected value is required.")
  private String expectedValue;

  private String jsonPointer;
  private String description;
  private String toolName;
}
