/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.testcase.TestCaseResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * REST controller for managing test case operations. Provides endpoints for creating, updating,
 * deleting, and searching test cases. All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "TestCaseRest",
    url = "${prompt-analyzer-service.url}",
    path = "/test-case",
    primary = false)
public interface TestCaseRest {

  /**
   * Creates a new test case. Requires ROLE_API_TESTCASE_WRITE security role.
   *
   * @param requestDto The test case details to create
   * @return Map containing the result of the create operation
   * @apiNote Response Codes: 200 - Create TestCase successfully 500 - Error occurred during
   *     creation
   */
  @Operation(
      summary = "Create TestCase",
      description = "Creates a new test case with the provided configuration and details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Create TestCase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> create(@RequestBody TestCaseRequestDto requestDto);

  /**
   * Creates a test case in batch. Requires ROLE_API_TESTCASE_WRITE security role.
   *
   * @param requestDtos The test case details to create
   * @return Map containing the result of the create operation
   * @apiNote Response Codes: 200 - Create TestCase successfully 500 - Error occurred during
   *     creation
   */
  @Operation(
      summary = "Create TestCase",
      description =
          "Creates multiple test cases in batch with the provided configurations and details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_CREATE_BATCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Create TestCase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/create-batch", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> createBatch(@RequestBody List<TestCaseRequestDto> requestDtos);

  /**
   * Updates an existing test case. Requires ROLE_API_TESTCASE_WRITE security role.
   *
   * @param updatedTestcase The updated test case data
   * @return Map containing the result of the update operation
   * @apiNote Response Codes: 200 - Update TestCase successfully 500 - Error occurred during update
   */
  @Operation(
      summary = "update TestCase",
      description = "Updates an existing test case with the provided configuration and details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Update TestCase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> update(@Valid @RequestBody TestCaseRequestDto updatedTestcase);

  /**
   * Deletes a test case by ID. Only the creator can perform this operation.
   *
   * @param requestBody Map containing the ID of the test case to delete
   * @return Map containing the result of the delete operation
   */
  @Operation(
      summary = "Delete TestCase by Id (Creator Only)",
      description =
          "Deletes a test case identified by its ID from the system. Only the creator of the test case can perform this operation.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_DELETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Delete TestCase successfully."),
        @ApiResponse(
            responseCode = APIConstants.FORBIDDEN_CODE,
            description = APIConstants.FORBIDDEN_MESSAGE),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/delete")
  Map<String, String> deleteTestcase(@RequestBody Map<String, String> requestBody);

  /**
   * Searches for test cases with pagination and sorting options. Requires ROLE_API_TESTCASE_READ
   * security role.
   *
   * @param filter Optional filter criteria for searching test cases
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching test cases converted to DTOs
   * @apiNote Response Codes: 200 - Get All Testcase successfully 500 - Error occurred during search
   */
  @Operation(
      summary = "search TestCase",
      description =
          "Searches for test cases with support for filtering, pagination, and sorting options.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Get All Testcase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/search")
  List<TestCaseResponseDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts the number of test cases matching the optional filter. Requires ROLE_API_TESTCASE_READ
   * security role.
   *
   * @param filter Optional filter criteria
   * @return Total count of matching test cases
   * @apiNote Response Codes: 200 - Count All TestCase successfully 500 - Error occurred during
   *     count operation
   */
  @Operation(
      summary = "count TestCase",
      description = "Counts the total number of test cases matching the provided filter criteria.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "count All TestCase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/count")
  Long count(@RequestParam(required = false) String filter);

  /**
   * Imports test cases from a CSV file. Requires ROLE_API_TESTCASE_WRITE security role.
   *
   * @param file The CSV file containing test case data
   * @return ResponseEntity containing the result of the import operation
   * @apiNote Response Codes: 200 - Import TestCase successfully 500 - Error occurred during import
   */
  @Operation(
      summary = "Import TestCase",
      description = "Imports test cases from an uploaded CSV file into the system.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_IMPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Import TestCase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/importTestCase", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  ResponseEntity<Resource> importTestCases(@RequestParam("file") MultipartFile file);

  /**
   * Exports test cases by their IDs. Requires ROLE_API_TESTCASE_READ security role.
   *
   * @param testCaseIds List of test case IDs to export
   * @return ResponseEntity containing the exported test cases as a CSV file
   * @apiNote Response Codes: 200 - Export TestCase successfully 500 - Error occurred during export
   */
  @Operation(
      summary = "Export TestCase by IDs",
      description =
          "Exports specific test cases identified by their IDs in CSV format for download.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TESTCASE_EXPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Export TestCase successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/exportTestCasesByIds", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  ResponseEntity<Resource> exportTestCasesByIds(@RequestBody List<String> testCaseIds);
}
