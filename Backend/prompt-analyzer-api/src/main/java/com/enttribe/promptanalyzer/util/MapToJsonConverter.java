/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.core.generic.exceptions.application.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.HashMap;
import java.util.Map;

/**
 * JPA attribute converter for converting a map of objects to a JSON string and vice versa.
 * Facilitates the storage of dynamic key-value pairs in a database column.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Converter
public class MapToJsonConverter implements AttributeConverter<Map<String, Object>, String> {

  private static final ObjectMapper objectMapper = new ObjectMapper();

  @Override
  public String convertToDatabaseColumn(Map<String, Object> attribute) {
    try {
      return attribute == null ? "{}" : objectMapper.writeValueAsString(attribute);
    } catch (JsonProcessingException e) {
      throw new BusinessException("Error serializing object map", e);
    }
  }

  @Override
  public Map<String, Object> convertToEntityAttribute(String dbData) {
    try {
      if (dbData == null || dbData.trim().isEmpty()) {
        return new HashMap<>();
      }
      return objectMapper.readValue(dbData, new TypeReference<Map<String, Object>>() {});
    } catch (JsonProcessingException e) {
      throw new BusinessException("Error deserializing object map", e);
    }
  }
}
