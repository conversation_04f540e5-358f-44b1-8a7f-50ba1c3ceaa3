/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.*;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a user query. Stores details about queries made by users for processing and
 * analysis.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "QUERY")
@Getter
@Setter
public class Query {

  /** Unique ID of the query. */
  @GeneratedValue(strategy = jakarta.persistence.GenerationType.IDENTITY)
  @Id
  @Column(name = "ID", columnDefinition = "INT")
  private Integer id;

  /** ID of the user who made the query. */
  @Column(name = "USER_ID")
  private String userId;

  /** Type of the query. */
  @Column(name = "TYPE", length = 50)
  private String type;

  /** The question or content of the query. */
  @Column(name = "QUESTION", columnDefinition = "LONGTEXT")
  private String question;

  /** Indicates if the query is marked as deleted. */
  @Column(name = "DELETED")
  private Boolean deleted = false;

  @Column(name = "NANO_ID", length = 50)
  private String nanoId;

  @Column(name = "CREATED_TIME")
  private Date createdTime;

  @Column(name = "MODIFIED_TIME")
  private Date modifiedTime;
}
