/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseRequestDto;
import com.enttribe.promptanalyzer.dto.agent.AgentTestCaseResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * REST interface for managing Agent Test Case entities.
 *
 * <p>Provides endpoints for creating, updating, retrieving, deleting, searching, and counting agent
 * test cases. Supports both entity and DTO-based operations, with optional filtering and
 * pagination.
 *
 * <p>This interface is used as both a Spring REST controller and a Feign client for remote calls.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "AgentTestCaseRest",
    url = "${prompt-analyzer-service.url}",
    path = "/agent-test-case",
    primary = false)
public interface AgentTestCaseRest {

  /**
   * Updates an existing agent test case.
   *
   * @param dto the agent test case request DTO with updated information
   * @return the updated agent test case as a response DTO
   */
  @Operation(
      summary = "Update Agent Test Case",
      description = "Updates an existing agent test case.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_TESTCASE_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> update(@RequestBody AgentTestCaseRequestDto dto);

  /**
   * Searches for agent test cases with optional filtering, pagination, and sorting.
   *
   * @param filter the filter string (optional)
   * @param offset the starting index for pagination (required)
   * @param size the number of results to return (required)
   * @param orderBy the field to order by (optional)
   * @param orderType the order direction (asc/desc, optional)
   * @return a list of agent test case response DTOs matching the criteria
   */
  @Operation(
      summary = "Search Agent Test Cases",
      description = "Searches for agent test cases.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_TESTCASE_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Search successful"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/search")
  List<AgentTestCaseResponseDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts the number of agent test cases matching the optional filter.
   *
   * @param filter the filter string (optional)
   * @return the count of agent test cases matching the filter
   */
  @Operation(
      summary = "Count Agent Test Cases",
      description = "Counts agent test cases.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_TESTCASE_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count successful"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/count")
  Long count(@RequestParam(required = false) String filter);

  /**
   * Soft deletes an agent test case by its ID.
   *
   * @param id the nanoId of the agent test case to soft delete
   * @return a map containing the result of the soft delete operation
   */
  @Operation(
      summary = "Delete Agent Test Case by Id (Creator Only)",
      description = "Deletes an agent test case by its ID.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_AGENT_TESTCASE_DELETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Delete Agent Test Case successfully."),
        @ApiResponse(
            responseCode = APIConstants.FORBIDDEN_CODE,
            description = APIConstants.FORBIDDEN_MESSAGE),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/deleteById/{id}")
  Map<String, String> deleteById(@PathVariable String id);
}
