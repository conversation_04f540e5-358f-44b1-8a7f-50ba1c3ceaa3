/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Result DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to result
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for test
 * and assertion results, and related reporting.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Test and assertion result DTOs</li>
 *   <li>Support for result reporting and analysis</li>
 *   <li>Facilitation of result data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Result DTOs are used in controllers, services, and clients to transfer result-related
 * data for reporting and analysis purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.result;
