/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "AGENT_TESTCASE_ASSERTIONS")
@NoArgsConstructor
@Getter
@Setter
public class AgentTestCaseAssertion {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Long id;

  @ManyToOne
  @JoinColumn(name = "AGENT_TESTCASE_ID")
  private AgentTestCase agentTestCase;

  @ManyToOne
  @JoinColumn(name = "ASSERTION_ID")
  private Assertion assertion;

  @Column(name = "EXPECTED_VALUE", columnDefinition = "TEXT")
  private String expectedValue;

  @Column(name = "PARAMETER_NAME", length = 50)
  private String parameterName = "";

  @Column(name = "TOOL_NAME", length = 100)
  private String toolName;

  @Column(name = "CREATED_TIME")
  private Date createdTime;

  @Column(name = "MODIFIED_TIME")
  private Date modifiedTime;
}
