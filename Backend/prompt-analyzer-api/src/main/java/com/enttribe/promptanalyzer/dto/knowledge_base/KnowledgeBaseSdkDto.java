/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.knowledge_base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) specifically designed for SDK interactions with the knowledge base.
 * Provides a simplified interface for external SDK implementations. Author: VisionWaves Version:
 * 1.0
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KnowledgeBaseSdkDto {

  private String id;
  private String name;
  private String type;
  private String description;
  private String docType;
  private String filter;
  private Integer topK;
  private Double similarityThreshold;
  private String fileName;
  private Boolean isContext;
  private Boolean returnDirect;
}
