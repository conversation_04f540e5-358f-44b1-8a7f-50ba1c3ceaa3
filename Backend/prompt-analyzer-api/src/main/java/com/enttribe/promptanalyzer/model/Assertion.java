/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.enttribe.promptanalyzer.enums.AssertionType;
import com.enttribe.promptanalyzer.enums.MatchStrategy;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "ASSERTIONS")
public class Assertion {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Long id;

  @Column(name = "NANO_ID", length = 50)
  private String nanoId;

  @Enumerated(EnumType.STRING)
  @Column(name = "ASSERTION_TYPE", nullable = false)
  private AssertionType assertionType;

  @Enumerated(EnumType.STRING)
  @Column(name = "MATCH_STRATEGY", nullable = false)
  private MatchStrategy matchStrategy = MatchStrategy.EXACT;

  @Column(name = "DESCRIPTION", length = 1200)
  private String description;

  @Column(name = "DELETED")
  private Boolean deleted = false;

  @Column(name = "CREATED_TIME")
  private Date createdTime;

  @Column(name = "MODIFIED_TIME")
  private Date modifiedTime;
}
