/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a test plan. Stores details about test plans, including their name, status,
 * and associated test cases.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@Entity
@Table(name = "TEST_PLAN")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TestPlan {

  /** Unique ID of the test plan. */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Integer id;

  /** Indicates if the test plan is enabled. */
  @Column(name = "ENABLED")
  private Boolean enabled = true;

  /** Name of the test plan. */
  @Column(name = "NAME", nullable = false)
  private String name;

  /** Test cases associated with the test plan. */
  @ManyToMany(mappedBy = "testPlans")
  private Set<TestCase> testcases = new HashSet<>();
}
