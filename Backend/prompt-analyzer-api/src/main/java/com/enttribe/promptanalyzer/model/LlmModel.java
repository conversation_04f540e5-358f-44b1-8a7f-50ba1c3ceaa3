/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a Large Language Model (LLM) configuration. Stores details about the model,
 * provider, and related settings.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@Entity
@Table(name = "LLM_MODEL")
public class LlmModel {

  /** Unique ID of the LLM model entry. */
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Id
  @Column(name = "ID", columnDefinition = "INT")
  private Integer id;

  /** Name or identifier of the model. */
  @Column(name = "MODEL")
  private String model;

  /** Provider of the model. */
  @Column(name = "PROVIDER")
  private String provider;

  /** Inference OF model. */
  @Column(name = "INFERENCE")
  private String inference;

  /** Base URL for accessing the model. */
  @Column(name = "BASE_URL")
  private String baseUrl;

  /** API key for authentication. */
  @Column(name = "API_KEY")
  private String apiKey;

  /** Type of the model. */
  @Column(name = "TYPE")
  private String type;

  /** Language supported by the model. */
  @Column(name = "LANGUAGE")
  private String language;

  /** Voice settings for the model, if applicable. */
  @Column(name = "VOICE")
  private String voice;

  /** Current status of the model. */
  @Column(name = "STATUS")
  private String status;

  /** The cost per million input tokens for model. Default value is set to 0.0 if not provided. */
  @Column(name = "INPUT_COST", columnDefinition = "DOUBLE DEFAULT 0.0")
  private double inputCost;

  /** The cost per million output tokens for model. Default value is set to 0.0 if not provided. */
  @Column(name = "OUTPUT_COST", columnDefinition = "DOUBLE DEFAULT 0.0")
  private double outputCost;
}
