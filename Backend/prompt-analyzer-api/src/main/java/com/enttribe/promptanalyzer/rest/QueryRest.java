/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.query.QueryResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing query operations. Provides endpoints for creating, searching, and
 * managing queries with support for pagination and filtering.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "QueryRest",
    url = "${prompt-analyzer-service.url}",
    path = "/query",
    primary = false)
public interface QueryRest {

  /**
   * Searches for queries with pagination and sorting options.
   *
   * @param filter Optional filter criteria for searching queries
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching queries as QueryResponseDto objects
   */
  @Operation(
      summary = "Search for queries",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_QUERY_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Queries retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/search")
  List<QueryResponseDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts the number of queries matching the optional filter.
   *
   * @param filter Optional filter criteria
   * @return Total count of matching queries
   */
  @Operation(
      summary = "Count matching queries",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_QUERY_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Count retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/count")
  Long count(@RequestParam(required = false) String filter);
}
