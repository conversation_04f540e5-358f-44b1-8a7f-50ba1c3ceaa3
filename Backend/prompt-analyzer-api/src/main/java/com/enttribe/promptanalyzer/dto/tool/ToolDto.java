/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.tool;

import com.enttribe.core.generic.utils.BypassValidation;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A comprehensive Data Transfer Object (DTO) that represents a complete tool configuration. Serves
 * as the primary tool representation including all tool-related information, configurations, and
 * associations. Author: VisionWaves Version: 1.0
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolDto {

  private String id;

  @NotEmpty(message = "Application name is required")
  private String applicationName;

  private String category;

  @NotEmpty(message = "Tool name is required")
  private String name;

  private String displayName;
  private String version;
  private Boolean deleted = false;
  private String toolId;
  private String language;
  private String type;

  @BypassValidation(reason = "sourceCode contains tags")
  private String sourceCode;

  private List<SwaggerDto> swaggerApis;
  private ApiToolDto apiTool;

  @BypassValidation(reason = "description contains tags")
  @NotEmpty(message = "Description is required")
  private String description;

  private String className;
  private String requestType;
  private String tags;

  @BypassValidation(reason = "toolJson contains tags")
  private String toolJson;

  private String status;
  private ToolAuthDto toolAuthentication;
  private String toolImage;
  private Long agentId;
  private String httpMethod;
  private String parameters;
  private String url;
  private String connectorName;

  @BypassValidation(reason = "operationConfig contains tags")
  private String operationConfig;

  private Boolean returnDirect;
  private String entityName;
  private String intentName;
}
