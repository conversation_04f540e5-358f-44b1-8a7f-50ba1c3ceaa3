/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.enttribe.promptanalyzer.util.MapToJsonConverter;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "AGENT_TEST_CASES")
public class AgentTestCase extends BaseEntityGlobal {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Long id;

  @Column(name = "NANO_ID", length = 50, nullable = false)
  private String nanoId;

  @Column(name = "AGENT_ID", length = 50)
  private String agentId;

  @Column(name = "AGENT_NAME", length = 50)
  private String agentName;

  @Column(name = "AGENT_TYPE", length = 50)
  private String agentType;

  @Column(name = "NAME", length = 50, unique = true, nullable = false)
  private String name;

  @Column(name = "DESCRIPTION", length = 1500)
  private String description;

  @Column(name = "HISTORY", columnDefinition = "LONGTEXT")
  private String history;

  @Column(name = "ADVISORS", length = 500)
  private String advisors;

  @Column(name = "DELETED")
  private boolean deleted = false;

  @ManyToOne
  @JoinColumn(name = "PROMPT_ID")
  private Prompt prompt;

  @OneToMany
  @JoinTable(
      name = "TESTCASE_TOOLS",
      joinColumns = @JoinColumn(name = "TESTCASE_ID"),
      inverseJoinColumns = @JoinColumn(name = "TOOL_ID"))
  private Set<Tool> tools = new HashSet<>();

  @OneToMany
  @JoinTable(
      name = "TESTCASE_KNOWLEDGE_BASES",
      joinColumns = @JoinColumn(name = "TESTCASE_ID"),
      inverseJoinColumns = @JoinColumn(name = "KNOWLEDGE_BASE_ID"))
  private Set<KnowledgeBase> knowledgeBases = new HashSet<>();

  @Column(name = "USER_QUESTION", columnDefinition = "TEXT")
  private String userQuestion;

  @OneToMany(mappedBy = "agentTestCase", cascade = CascadeType.ALL, orphanRemoval = true)
  private Set<AgentTestCaseAssertion> agentTestCaseAssertions = new HashSet<>();

  @Convert(converter = MapToJsonConverter.class)
  @Column(name = "VARIABLE_MAP", columnDefinition = "TEXT")
  private Map<String, Object> variableMap = new HashMap<>();

  @ManyToMany
  @JoinTable(
      name = "TESTCASES_AGENT_TESTCASES",
      joinColumns = @JoinColumn(name = "AGENT_TESTCASE_ID"),
      inverseJoinColumns = @JoinColumn(name = "TEST_SUITE_ID"))
  private Set<TestSuite> testSuites = new HashSet<>();
}
