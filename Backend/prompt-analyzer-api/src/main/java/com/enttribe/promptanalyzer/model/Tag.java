/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity class representing a Tag in the system. Tags are used to categorize and label various
 * items in the application. This entity is mapped to the "TAG" table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Setter
@Getter
@Entity
@Table(name = "TAG")
public class Tag extends BaseEntityGlobal {

  /** Unique identifier for the tag. Auto-generated using identity strategy. */
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Id
  @Column(name = "ID", columnDefinition = "INT")
  private Integer id;

  /** The type or category of the tag. Used for grouping related tags together. */
  @Column(name = "TYPE")
  private String type;

  /** The display name of the tag. Used for user-friendly representation of the tag. */
  @Column(name = "NAME")
  private String name;

  /** Flag indicating if the tag has been soft deleted. Default value is false. */
  @Column(name = "DELETED")
  private Boolean deleted = false;

  @Column(name = "NANO_ID", length = 50)
  private String nanoId;
}
