/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.knowledge_base;

import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents vector search responses from the knowledge base.
 * Contains filter criteria, metadata, and document identifiers for vector search results. Author:
 * VisionWaves Version: 1.0
 */
@Setter
@Getter
@AllArgsConstructor
public class VectorResponseDto {

  private String filter;
  private Map<String, Object> metadata;
  private List<String> docIds;
}
