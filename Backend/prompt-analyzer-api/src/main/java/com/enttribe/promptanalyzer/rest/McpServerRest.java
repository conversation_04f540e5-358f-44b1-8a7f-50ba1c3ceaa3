/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for managing MCP server operations. Provides endpoints for creating, updating,
 * searching, counting, and deleting MCP servers.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "McpServerRest",
    url = "${prompt-analyzer-service.url}",
    path = "/mcpserver",
    primary = false)
public interface McpServerRest {

  /**
   * Creates a new MCP server.
   *
   * @param serverDto The MCP server details to create
   * @return Map containing the result of the operation
   */
  @Operation(
      summary = "Create new MCP server",
      description = "Creates a new MCP server with the provided configuration and details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_MCP_SERVER_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "MCP server created successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/create")
  Map<String, String> create(@RequestBody McpServerDto serverDto);

  /**
   * Updates an existing MCP server.
   *
   * @param serverDto The MCP server details to update
   * @return Map containing the result of the operation
   */
  @Operation(
      summary = "Update MCP server",
      description =
          "Updates an existing MCP server's configuration and details with the provided information.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_MCP_SERVER_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "MCP server updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/update")
  Map<String, String> update(@RequestBody McpServerDto serverDto);

  /**
   * Searches for MCP servers based on provided filters and pagination parameters.
   *
   * @param filter Optional filter string to search servers
   * @param offset Optional pagination offset
   * @param size Optional pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching MCP servers
   */
  @Operation(
      summary = "Search for MCP servers",
      description =
          "Searches for MCP servers based on provided filters, with support for pagination and sorting options.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_MCP_SERVER_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "MCP servers retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/search")
  List<McpServerDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) Integer offset,
      @RequestParam(required = false) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts MCP servers based on provided filter.
   *
   * @param filter Optional filter string to count matching servers
   * @return Map containing the count of matching servers
   */
  @Operation(
      summary = "Count MCP servers",
      description = "Counts the total number of MCP servers matching the provided filter criteria.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_MCP_SERVER_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "MCP servers counted successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/count")
  Long count(@RequestParam(required = false) String filter);

  /**
   * Soft deletes an MCP server by ID.
   *
   * @param id The ID of the MCP server to delete
   * @return Map containing the result of the operation
   */
  @Operation(
      summary = "Delete MCP server (Creator Only)",
      description = "Performs a soft delete operation on the MCP server with the specified ID.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_MCP_SERVER_DELETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "MCP server deleted successfully"),
        @ApiResponse(
            responseCode = APIConstants.FORBIDDEN_CODE,
            description = APIConstants.FORBIDDEN_MESSAGE),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/delete/{id}")
  Map<String, String> softDelete(@PathVariable String id);

  /**
   * Retrieves MCP servers by their IDs. Requires ROLE_API_MCP_SERVER_GET_BY_IDS security role.
   *
   * @param ids List of MCP server IDs to retrieve
   * @return List of McpServerDto containing the server details
   */
  @Operation(
      summary = "Retrieve MCP servers by IDs",
      description = "Retrieves detailed information for multiple MCP servers based on their IDs.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_MCP_SERVER_GET_BY_IDS})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "MCP servers retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/getMcpServerByIds")
  List<McpServerDto> getMcpServerByIds(@RequestBody List<String> ids);
}
