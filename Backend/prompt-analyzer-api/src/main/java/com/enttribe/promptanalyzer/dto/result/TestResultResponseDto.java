/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.result;

import java.util.Date;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestResultResponseDto {

  private String id;
  private String testSuiteName;
  private String executionId;
  private Double accuracy;
  private Long latency;
  private Double consistency;
  private Integer numberOfRuns;
  private Integer testCasesPassed;
  private Integer testCasesFailed;
  private Integer promptToken;
  private Integer generationTokens;
  private String model;
  private String description;
  private String status;
  private Boolean deleted;
  private Date createdTime;
  private Date modifiedTime;
  private int testCaseResultsCount;
  private String agentName;
  private String agentType;
}
