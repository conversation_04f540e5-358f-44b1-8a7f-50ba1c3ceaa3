/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Prompt DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to prompt
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for prompt
 * creation, management, and evaluation.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Prompt creation and management DTOs</li>
 *   <li>Support for prompt evaluation and conversion</li>
 *   <li>Facilitation of prompt data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Prompt DTOs are used in controllers, services, and clients to transfer prompt-related
 * data for prompt engineering and management purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.prompt;
