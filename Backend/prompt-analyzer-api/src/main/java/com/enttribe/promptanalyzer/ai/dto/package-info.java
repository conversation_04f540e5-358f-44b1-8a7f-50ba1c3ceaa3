/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * AI DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to AI operations
 * in the Prompt Analyzer application. These DTOs encapsulate data for AI requests,
 * responses, and configuration.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>AI request and response DTOs</li>
 *   <li>Support for AI model configuration</li>
 *   <li>Facilitation of AI data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>AI DTOs are used in controllers, services, and clients to transfer AI-related
 * data for processing and integration purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.ai.dto;
