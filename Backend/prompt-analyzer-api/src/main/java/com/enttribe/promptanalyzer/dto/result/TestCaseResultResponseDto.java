/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.result;

import java.util.Date;
import java.util.List;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestCaseResultResponseDto {

  private String id;
  private Double accuracy;
  private Long latency;
  private Boolean isPassed;
  private Boolean isError;
  private String failureReason;
  private String userQuestion;
  private Integer runNumber;
  private Integer promptToken;
  private Integer generationTokens;
  private Integer assertionsPassed;
  private Integer assertionsFailed;
  private int assertionResultsCount;
  private List<AssertionResultResponseDto> assertionResults;
  private Date createdTime;
  private String name;
  private String description;
  private String advisors;
}
