/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Enums package for the Prompt Analyzer application.
 *
 * <p>This package contains enumeration types used throughout the Prompt Analyzer
 * application. Enums provide type-safe representations of fixed sets of constants
 * for use in business logic, configuration, and data modeling.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Type-safe constant definitions</li>
 *   <li>Support for business logic and configuration</li>
 *   <li>Improved code readability and maintainability</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Enums are used in models, DTOs, and services to represent fixed sets of values
 * such as statuses, types, and categories.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.enums;
