/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Model package for the Prompt Analyzer application.
 *
 * <p>This package contains entity and model classes that represent the core business objects
 * and database entities for the Prompt Analyzer application. These classes are typically
 * mapped to database tables and are used throughout the application for data persistence
 * and business logic.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>JPA entity mappings for database tables</li>
 *   <li>Core business object representations</li>
 *   <li>Support for persistence and domain logic</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Model classes are used in repositories, services, and controllers to manage and transfer
 * persistent data. They form the backbone of the application's data layer.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.model;
