/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "TEST_CASE_RESULT")
public class TestCaseResult {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Long id;

  @Column(name = "ACCURACY")
  private Double accuracy;

  @Column(name = "LATENCY")
  private Long latency;

  @Column(name = "IS_PASSED")
  private boolean isPassed;

  @Column(name = "IS_ERROR")
  private boolean isError = false;

  @Column(name = "FAILURE_REASON", columnDefinition = "TEXT")
  private String failureReason;

  @Column(name = "ASSERTIONS_PASSED")
  private Integer assertionsPassed;

  @Column(name = "ASSERTIONS_FAILED")
  private Integer assertionsFailed;

  @Column(name = "RUN_NUMBER")
  private Integer runNumber;

  @Column(name = "PROMPT_TOKEN")
  private Integer promptToken;

  @Column(name = "GENERATION_TOKENS")
  private Integer generationTokens;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "TEST_CASE_ID")
  private AgentTestCase agentTestCase;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "TEST_RESULT_ID")
  private TestResult testResult;

  @OneToMany(
      mappedBy = "testCaseResult",
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.LAZY)
  private Set<AssertionResult> assertionResults = new HashSet<>();

  @Column(name = "CREATED_TIME")
  private Date createdTime;

  @Column(name = "NANO_ID", length = 50, unique = true)
  private String nanoId;
}
