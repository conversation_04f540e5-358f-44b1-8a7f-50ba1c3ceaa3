/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Processor DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to processor
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for processor
 * configuration, requests, and responses.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Processor configuration and request DTOs</li>
 *   <li>Support for processor management</li>
 *   <li>Facilitation of processor data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Processor DTOs are used in controllers, services, and clients to transfer processor-related
 * data for configuration and management purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.processor;
