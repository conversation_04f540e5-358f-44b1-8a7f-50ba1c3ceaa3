/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * REST controller for managing knowledge base operations. Provides endpoints for creating,
 * updating, searching, and managing knowledge base documents.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(
    name = "KnowledgeBaseRest",
    url = "${prompt-analyzer-service.url}",
    path = "/knowledge-base",
    primary = false)
public interface KnowledgeBaseRest {

  /**
   * Retrieves knowledge base data by ID.
   *
   * @param id The ID of the knowledge base entry
   * @return KnowledgeBaseResponseDto containing the requested data
   */
  @Operation(
      summary = "Get knowledge base entry by ID",
      description = "returns knowledge base record by id",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_GET_BY_ID})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Knowledge base entry retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/findById/{id}")
  KnowledgeBaseResponseDto getKnowledgeBaseById(@PathVariable Integer id);

  @Operation(
      summary = "Get knowledge base entry by ID",
      description = "returns knowledge base record by id",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_FIND_BY_ID})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Knowledge base entry retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/v1/findById/{id}")
  KnowledgeBaseResponseDto getKnowledgeBaseByIdV1(@PathVariable String id);

  /**
   * Processes website-type knowledge bases.
   *
   * <p>This endpoint triggers the processing of all knowledge bases of type 'website', performing
   * any necessary extraction, transformation, or loading operations required to make the website
   * data available for use in the system.
   *
   * @return a map containing the result of the processing operation, typically with status and
   *     message
   */
  @Operation(
      summary = "Process website knowledge base",
      description = "processes website type knowledge bases",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Website knowledge base processed successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/processWebSiteKB")
  Map<String, String> processWebSiteKB();

  /**
   * Saves a new document to the knowledge base.
   *
   * @param documentDto The document data and file to save
   * @return Map containing the result of the save operation
   */
  @Operation(
      summary = "Save document to knowledge base",
      description = "saves different types of knowledge-bases",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_DOCUMENT_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Document saved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/saveDocument")
  Map<String, String> saveDocument(@ModelAttribute DocumentRequestDto documentDto);

  @Operation(
      summary = "Save website to knowledge base",
      description = "saves website type knowledge base",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_WEBSITE_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Website saved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/saveWebSite")
  Map<String, String> saveWebSite(@RequestBody DocumentRequestDto documentDto);

  /**
   * Updates an existing document in the knowledge base.
   *
   * @param nanoId The ID of the document to update
   * @param documentDto The updated document data and file
   * @return Map containing the result of the update operation
   * @throws JsonProcessingException if there's an error processing JSON data
   */
  @Operation(
      summary = "Update document in knowledge base",
      description = "update existing knowledge base",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_DOCUMENT_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Document updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/updateDocument/{nanoId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  Map<String, String> updateDocument(
      @PathVariable String nanoId, @ModelAttribute DocumentRequestDto documentDto)
      throws JsonProcessingException;

  /**
   * Counts knowledge base entries matching the optional filter.
   *
   * @param filter Optional filter criteria
   * @return Total count of matching entries
   */
  @Operation(
      summary = "Count knowledge base entries",
      description = "returns knowledge base count with filter",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Count retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/count")
  Long count(@RequestParam(required = false) String filter);

  /**
   * Searches knowledge base entries with pagination and sorting.
   *
   * @param filter Optional filter criteria
   * @param offset Required pagination offset
   * @param size Required pagination size
   * @param orderBy Optional field to order results by
   * @param orderType Optional order direction (asc/desc)
   * @return List of matching knowledge base entries
   */
  @Operation(
      summary = "Search knowledge base entries",
      description = "returns knowledge base list with filter and pagination",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Knowledge base entries retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/search")
  List<KnowledgeBaseResponseDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Soft deletes a knowledge base entry by ID.
   *
   * @param id The ID of the entry to delete
   * @return Map containing the result of the delete operation
   */
  @Operation(
      summary = "Delete knowledge base entry by ID (Creator Only)",
      description = "soft deletes the record by id",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_DELETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Knowledge base entry deleted successfully"),
        @ApiResponse(
            responseCode = APIConstants.FORBIDDEN_CODE,
            description = APIConstants.FORBIDDEN_MESSAGE),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/deleteById/{id}")
  Map<String, String> deleteById(@PathVariable String id);

  /**
   * Retrieves multiple knowledge base entries by their IDs.
   *
   * @param ids List of knowledge base IDs to retrieve
   * @return List of matching knowledge base entries in SDK format
   */
  @Operation(
      summary = "Get knowledge base entries by IDs",
      description = "returns multiple knowledge bases by ids",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_GET_BY_IDS})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Knowledge base entries retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/getKnowledgeBaseByIds")
  List<KnowledgeBaseSdkDto> getKnowledgeBaseByIds(@RequestBody List<String> ids);

  @Operation(
      summary = "Update tag by ID",
      description = "updates tag of existing knowledge base",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_TAG_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tag updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/updateTagById/{id}")
  Map<String, String> updateTagById(@PathVariable String id, @RequestBody Map<String, String> tags);

  /**
   * Retrieves the names of tables based on the provided request parameters.
   *
   * <p>This endpoint queries the connected database or data source using the parameters provided in
   * the request body and returns a list of table names that match the criteria.
   *
   * @param requestBody a map containing request parameters such as database name or connection info
   * @return a list of table names matching the request criteria
   */
  @Operation(
      summary = "Get Table Names",
      description = "Retrieves the names of tables based on the provided database",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_SHOW_TABLES})
      })
  @ApiResponses(
      value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved table names"),
        @ApiResponse(responseCode = "500", description = "Error occurred during retrieval")
      })
  @PostMapping("/show-tables")
  List<String> getTablesName(Map<String, String> requestBody);

  @Operation(
      summary = "Check if a knowledge base URL exists",
      description = "checks whether given knowledge base exists by website url",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_EXISTS})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Existence check completed successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/existsWebsiteUrl", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, Object> existsWebsiteUrl(@RequestBody Map<String, String> requestMap);

  /**
   * Saves a new content to the knowledge base.
   *
   * @param documentDto The document data and content to save
   * @return Map containing the result of the save operation
   */
  @Operation(
      summary = "Save content to knowledge base",
      description = "saves knowledge base based on input content",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_CONTENT_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Content saved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/saveContent", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> saveContent(@RequestBody DocumentRequestDto documentDto);

  /**
   * Imports CSV data into the Milvus vector database for custom agent processors.
   *
   * <p>This endpoint accepts a CSV file upload and processes its contents to import data into the
   * Milvus vector database, enabling vector-based search and retrieval for custom agents.
   *
   * @param file the CSV file containing data to be imported
   * @return a ResponseEntity containing the result of the import operation as a String
   */
  @Operation(
      summary = "Import CSV data to Milvus vector database",
      description = "imports data for custom agent processors from csv",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_MILVUS_IMPORT_DATA})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "CSV data imported to Milvus successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/milvusImportCsv")
  ResponseEntity<String> milvusImportCsv(@RequestParam("file") MultipartFile file);

  /**
   * Searches knowledge base by name and answers user question using vector metadata.
   *
   * @param requestMap Map containing knowledgeBaseName and userQuestion
   * @return Response containing the answer based on vector search
   */
  @Operation(
      summary = "Get direct answer from knowledge base using vector search",
      description = "returns answer from knowledge base",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_ANSWER_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Answer generated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/getAnswer")
  Map<String, String> getAnswer(@RequestBody Map<String, String> requestMap);

  /**
   * Exports knowledge bases as CSV.
   *
   * @return CSV file as a downloadable resource
   */
  @Operation(
      summary = "Export knowledge bases as CSV",
      description = "exports knowledge base as csv file",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_EXPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Knowledge bases exported successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  ResponseEntity<Resource> exportKnowledgeBases(@RequestBody List<String> ids);

  /**
   * Imports knowledge bases from CSV file.
   *
   * @param file CSV file containing knowledge base data
   * @return Map containing import results
   */
  @Operation(
      summary = "Import knowledge bases from CSV",
      description = "imports knowledge base from csv file",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_IMPORT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Knowledge bases imported successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  ResponseEntity<Resource> importKnowledgeBases(@RequestParam("file") MultipartFile file);

  /**
   * Updates the return direct flag for a knowledge base entry.
   *
   * @param requestMap Map containing the ID and value of the return direct flag
   * @return Map containing the result of the update operation
   */
  @Operation(
      summary = "Update return direct flag for knowledge base entry",
      description = "updates return direct flag of knowledge base",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Return direct flag updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/updateReturnDirect")
  Map<String, String> updateReturnDirect(@RequestBody Map<String, String> requestMap);

  /**
   * Checks if a knowledge base name exists or not.
   *
   * @param name The name of the knowledge base to check
   * @return Map containing the result of the existence check
   */
  @Operation(
      summary = "Check if a knowledge base name exists or not",
      description = "checks whether given knowledge base exists by name",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_EXISTS_BY_NAME})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Existence check completed successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/existsKnowledgeBaseName/{name}")
  Map<String, Boolean> existsKnowledgeBaseName(@PathVariable String name);

  @Operation(
      summary = "Download file from GCS",
      description = "downloads file from Google Cloud Storage",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_DOWNLOAD_FILE})
      })
  @GetMapping("/downloadFileFromS3/{fileName}")
  byte[] downloadFileFromS3(@PathVariable("fileName") String fileName);
}
