/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "ASSERTION_RESULT")
public class AssertionResult {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  @JsonIgnore
  private Long id;

  @Column(name = "ACTUAL", columnDefinition = "TEXT")
  private String actual;

  @Column(name = "EXPECTED", columnDefinition = "TEXT")
  private String expected;

  @Column(name = "IS_PASSED")
  private boolean isPassed;

  @Column(name = "USER_QUESTION", columnDefinition = "TEXT")
  private String userQuestion;

  @Column(name = "ASSERTION_TYPE", length = 30)
  private String assertionType;

  @Column(name = "MATCH_STRATEGY", length = 30)
  private String matchStrategy;

  @Column(name = "COMMENT", columnDefinition = "TEXT")
  private String comment;

  @Column(name = "ACCURACY_SCORE")
  private Double accuracyScore;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "TEST_CASE_RESULT_ID")
  private TestCaseResult testCaseResult;

  @Column(name = "CREATED_TIME")
  private Date createdTime;

  @Column(name = "NANO_ID", length = 50, unique = true)
  private String nanoId;

  @Column(name = "PARAMETER_NAME", length = 50)
  private String parameterName = "";
}
