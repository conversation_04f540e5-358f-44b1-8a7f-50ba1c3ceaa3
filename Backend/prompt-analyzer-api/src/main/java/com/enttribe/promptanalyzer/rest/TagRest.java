/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.tag.TagRequestDto;
import com.enttribe.promptanalyzer.dto.tag.TagResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * REST client interface for tag management operations. Provides CRUD operations for managing tags
 * through Feign client. Author: VisionWaves Version: 1.0
 */
@FeignClient(
    name = "TagRest",
    url = "${prompt-analyzer-service.url}",
    path = "/tag",
    primary = false)
public interface TagRest {

  /**
   * Searches tags with pagination and filtering. Requires ROLE_API_TAG_SEARCH role.
   *
   * @param filter optional search criteria
   * @param offset pagination start index
   * @param size items per page
   * @param orderBy sort field
   * @param orderType sort direction (asc/desc)
   * @return list of matching tags
   */
  @Operation(
      summary = "Search for tags",
      description =
          "Searches for tags with support for filtering, pagination, and sorting options.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TAG_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tags retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/search")
  List<TagResponseDto> search(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);

  /**
   * Counts total tags matching the filter. Requires ROLE_API_TAG_COUNT role.
   *
   * @param filter optional search criteria
   * @return total count of matching tags
   */
  @Operation(
      summary = "Count matching tags",
      description = "Counts the total number of tags matching the provided filter criteria.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TAG_COUNT})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Count retrieved successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping(path = "/count")
  Long count(@RequestParam(required = false) String filter);

  /**
   * Creates a new tag. Requires ROLE_API_TAG_CREATE role.
   *
   * @param dto tag creation details
   * @return operation result map
   */
  @Operation(
      summary = "Create a new tag",
      description = "Creates a new tag with the provided configuration and details.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TAG_CREATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tag created successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> save(@RequestBody TagRequestDto dto);

  /**
   * Deletes a tag by its ID. Only the creator can perform this operation.
   *
   * @param id tag identifier
   * @return operation result map
   */
  @Operation(
      summary = "Delete Tag by Id (Creator Only)",
      description =
          "Deletes a tag identified by its ID from the system. Only the creator of the tag can perform this operation.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TAG_DELETE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Delete Tag successfully."),
        @ApiResponse(
            responseCode = APIConstants.FORBIDDEN_CODE,
            description = APIConstants.FORBIDDEN_MESSAGE),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/deleteById/{id}")
  Map<String, String> deleteById(@PathVariable String id);

  /**
   * Updates an existing tag. Requires ROLE_API_TAG_UPDATE role.
   *
   * @param dto updated tag details
   * @return operation result map
   */
  @Operation(
      summary = "Update an existing tag",
      description =
          "Updates an existing tag's configuration and details with the provided information.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_TAG_UPDATE})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Tag updated successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
  Map<String, String> update(@RequestBody TagRequestDto dto);
}
