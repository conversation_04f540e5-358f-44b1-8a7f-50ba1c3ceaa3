/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Assertion DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to assertions
 * in the Prompt Analyzer application. These DTOs encapsulate data for assertion requests,
 * responses, and templates.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Assertion request and response DTOs</li>
 *   <li>Support for assertion templates and evaluation</li>
 *   <li>Facilitation of assertion data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Assertion DTOs are used in controllers, services, and clients to transfer assertion-related
 * data for validation and testing purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.assertion;
