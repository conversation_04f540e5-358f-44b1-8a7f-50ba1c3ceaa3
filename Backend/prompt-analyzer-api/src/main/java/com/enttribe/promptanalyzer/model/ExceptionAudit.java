/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity for logging exception details. Captures information about exceptions for auditing
 * purposes.
 *
 * <AUTHOR>
 * @version 1.0
 */
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "EXCEPTION_AUDIT")
public class ExceptionAudit {

  /** Unique ID of the audit record. */
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Id
  @Column(name = "ID")
  @JsonIgnore
  private Integer id;

  /** When the exception occurred. */
  @Column(name = "TIMESTAMP")
  private Date timestamp;

  /** Exception message. */
  @Column(name = "EXCEPTION_MESSAGE", length = 2500)
  private String exceptionMessage;

  /** Exception stack trace. */
  @Column(name = "EXCEPTION_TRACE", columnDefinition = "LONGTEXT")
  private String exceptionTrace;

  /** Method name where the exception occurred. */
  @Column(name = "METHOD_NAME")
  private String methodName;

  /** Method parameters at the time of exception. */
  @Column(name = "METHOD_PARAMETERS", columnDefinition = "TEXT")
  private String methodParameters;

  /** Contextual identifier. */
  @Column(name = "IDENTIFIER", columnDefinition = "TEXT")
  private String identifier;

  /** Application name. */
  @Column(name = "APPLICATION_NAME")
  private String applicationName;

  /** Associated prompt ID. */
  @Column(name = "PROMPT_ID")
  private String promptId;

  /** Exception category. */
  @Column(name = "CATEGORY")
  private String category;

  /** Audit tracking ID. */
  @Column(name = "AUDIT_ID", length = 400)
  private String auditId;
}
