/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Exception package for the Prompt Analyzer application.
 *
 * <p>This package contains custom exception classes and global error handlers
 * for the Prompt Analyzer application. These classes provide structured error
 * handling and reporting mechanisms throughout the application.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Custom exception definitions</li>
 *   <li>Global exception handling</li>
 *   <li>Support for meaningful error messages and codes</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Exception classes are used in all layers of the application to signal and
 * handle error conditions in a consistent and maintainable way.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.exception;
