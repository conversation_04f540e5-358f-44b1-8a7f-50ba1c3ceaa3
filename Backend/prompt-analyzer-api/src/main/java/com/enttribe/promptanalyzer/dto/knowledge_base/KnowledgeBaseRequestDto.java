/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.knowledge_base;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) for creating and configuring knowledge base instances. Handles
 * database connections, API configurations, and similarity settings. Author: VisionWaves Version:
 * 1.0
 */
@Setter
@Getter
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class KnowledgeBaseRequestDto {

  private String name;
  private String type;
  private String description;
  private String dbName;
  private String dbUsername;
  private String dbPassword;
  private String apiEndpoint;
  private String apiType;
  private String apiAuthType;
  private String apiAuthValue;
  private Boolean isContext;
  private Integer topK;
  private Double similarityThreshold;
  private String sourceCode;
  private String tags;
  private Boolean returnDirect;
}
