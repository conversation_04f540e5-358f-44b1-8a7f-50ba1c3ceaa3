/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Tool DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to tool
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for tool
 * configuration, management, and integration.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Tool configuration and management DTOs</li>
 *   <li>Support for tool integration and workflows</li>
 *   <li>Facilitation of tool data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Tool DTOs are used in controllers, services, and clients to transfer tool-related
 * data for integration, configuration, and management purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.tool;
