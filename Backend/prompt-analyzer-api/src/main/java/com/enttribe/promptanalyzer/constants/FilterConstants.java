/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.constants;

public class FilterConstants {

  public static final String _CUSTOMER_ID_FILTER = "_CUSTOMER_ID_FILTER";
  public static final String CUSTOMER_ID_FILTER_LOCAL = "CUSTOMER_ID_FILTER_LOCAL";
  public static final String CUSTOMER_ID_FILTER_GLOBAL = "CUSTOMER_ID_FILTER_GLOBAL";

  private FilterConstants() {
    throw new IllegalStateException("Utility class cannot be instantiated");
  }
}
