/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.microintent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for Micro Intent Response. This class is currently empty but can be extended in the future to
 * include fields and methods related to micro intent responses.
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MicroIntentResponseDto {

  private String id;
  private String entityName;
  private String intentName;
  private String name;
  private String description;
  private String displayName;
  private String sequenceChain;
}
