/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.knowledge_base;

import com.enttribe.core.generic.utils.BypassValidation;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

/**
 * A Data Transfer Object (DTO) that encapsulates document upload and processing requests for the
 * knowledge base system. Handles various document types and configurations. Author: VisionWaves
 * Version: 1.0
 */
@Setter
@Getter
public class DocumentRequestDto {

  private String name;
  private String type;
  private String description;
  private String websiteUrl;
  private String collectionName;
  private String docType;
  private Integer topK;
  private Double similarityThreshold;

  @BypassValidation(reason = "file contains tags")
  private List<MultipartFile> file;

  private Boolean deleted;
  private String tags;
  private String integration;
  private List<String> tables;

  @BypassValidation(reason = "content contains tags")
  private String content;

  private Boolean returnDirect;
}
