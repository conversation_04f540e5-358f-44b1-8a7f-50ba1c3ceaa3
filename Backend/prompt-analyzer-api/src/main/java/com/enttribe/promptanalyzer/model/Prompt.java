/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.util.*;
import lombok.Getter;
import lombok.Setter;

/**
 * Entity representing a prompt configuration. Stores details about prompts, including settings and
 * relationships with messages, tools, and models.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "PROMPT")
@Getter
@Setter
public class Prompt extends BaseEntityGlobal {

  /** Unique ID of the prompt. */
  @GeneratedValue(strategy = jakarta.persistence.GenerationType.IDENTITY)
  @Id
  @Column(name = "ID", columnDefinition = "INT")
  private Integer id;

  /** Application associated with the prompt. */
  @Column(name = "APPLICATION", length = 40)
  private String application;

  /** Category of the prompt. */
  @Column(name = "CATEGORY", length = 20)
  private String category;

  /** Indicates if the prompt is marked as deleted. */
  @Column(name = "DELETED")
  private Boolean deleted = false;

  /** Unique identifier for the prompt. */
  @Column(name = "PROMPT_ID")
  private String promptId;

  /** Unique ID of the prompt. */
  @Column(name = "NANO_ID", unique = true, length = 50)
  private String nanoId;

  /** Status of the prompt (e.g., DRAFT, PUBLISH, ARCHIVE). */
  @Column(name = "STATUS", columnDefinition = "ENUM('DRAFT','PUBLISH','ARCHIVE')")
  private String status;

  /** Temperature setting for the prompt. */
  @Column(name = "TEMPERATURE", columnDefinition = "Double")
  private Double temperature;

  /** Maximum token limit for the prompt. */
  @Column(name = "MAX_TOKEN", columnDefinition = "INT")
  private Integer maxToken;

  /** Version of the prompt. */
  @Column(nullable = false, length = 10, name = "VERSION")
  private String version;

  /** Top-p sampling parameter for the prompt. */
  @Column(name = "TOP_P")
  private Double topP;

  /** Name of the prompt. */
  @Column(name = "NAME", length = 40)
  private String name;

  /** Indicates if JSON mode is enabled. */
  @Column(name = "JSON_MODE")
  private Boolean jsonMode = false;

  /** Test cases associated with the prompt. */
  @OneToMany(mappedBy = "prompt", cascade = CascadeType.ALL)
  private Set<TestCase> testcases = new HashSet<>();

  /** Messages associated with the prompt. */
  @OneToMany(
      targetEntity = Message.class,
      mappedBy = "prompt",
      cascade = CascadeType.ALL,
      orphanRemoval = true,
      fetch = FetchType.EAGER)
  private List<Message> messages = new ArrayList<>();

  /** Assertion template for the prompt. */
  @Column(name = "ASSERTION_TEMPLATE", columnDefinition = "LONGTEXT")
  private String assertionTemplate;

  /** Default format for the prompt. */
  @Column(name = "DEFAULT_FORMAT")
  private String defaultFormat;

  /** Type of the prompt (e.g., CONVERSATION, NORMAL). */
  @Column(name = "TYPE", columnDefinition = "ENUM('CONVERSATION', 'NORMAL')")
  private String type;

  /** Associated LLM model for the prompt. */
  @ManyToOne
  @JoinColumn(name = "LLM_MODEL_ID")
  private LlmModel llmModel;

  /** Indicates if LLM guard is enabled. */
  @Column(name = "LLM_GUARD")
  private Boolean llmGuard = false;

  /** Labels or keywords for organizing and filtering prompts. */
  @Column(name = "TAG")
  private String tag;

  /** Indicates the reasoning effort required for the prompt. */
  @Column(name = "REASONING_EFFORT")
  private String reasoningEffort;
}
