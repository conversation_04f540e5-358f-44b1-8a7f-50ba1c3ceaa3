/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Feign client interface for nano ID data migration endpoints. Provides REST endpoints to trigger
 * migration of nano IDs for various entities.
 *
 * <p>Each method corresponds to a migration endpoint for a specific entity type.
 */
@FeignClient(
    name = "DataMigrationRest",
    url = "${prompt-analyzer-service.url}",
    path = "/nano",
    primary = false)
public interface DataMigrationRest {

  /**
   * Triggers migration of nano IDs for prompts.
   *
   * @return a ResponseEntity containing the migration result as a String
   */
  @GetMapping("/prompt")
  ResponseEntity<String> migrateNanoIdsForPrompt();

  /**
   * Triggers migration of nano IDs for knowledge bases.
   *
   * @return a ResponseEntity containing the migration result as a String
   */
  @GetMapping("/knowledgeBase")
  ResponseEntity<String> migrateNanoIdsForKnowledgeBase();

  /**
   * Triggers migration of nano IDs for MCP servers.
   *
   * @return a ResponseEntity containing the migration result as a String
   */
  @GetMapping("/mcp-server")
  ResponseEntity<String> migrateNanoIdsForMcpServer();

  /**
   * Triggers migration of nano IDs for tools.
   *
   * @return a ResponseEntity containing the migration result as a String
   */
  @GetMapping("/tool")
  ResponseEntity<String> migrateNanoIdsForTool();

  /**
   * Triggers migration of nano IDs for test cases.
   *
   * @return a ResponseEntity containing the migration result as a String
   */
  @GetMapping("/testcase")
  ResponseEntity<String> migrateNanoIdsForTestCase();

  /**
   * Triggers migration of nano IDs for tags.
   *
   * @return a ResponseEntity containing the migration result as a String
   */
  @GetMapping("/tag")
  ResponseEntity<String> migrateNanoIdsForTag();
}
