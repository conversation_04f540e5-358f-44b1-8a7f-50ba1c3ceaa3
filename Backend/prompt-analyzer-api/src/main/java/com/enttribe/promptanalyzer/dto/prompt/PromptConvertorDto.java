/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.prompt;

import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that represents a complete prompt configuration for conversion
 * purposes. Includes all necessary parameters for prompt execution and management. Author:
 * VisionWaves Version: 1.0
 */
@Get<PERSON>
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PromptConvertorDto {

  private String id;
  private String application;
  private String category;
  private String promptId;
  private String status;
  private String type;
  private String prompt;
  private Double temperature;
  private Integer maxTokens;
  private String version;
  private Double topP;
  private String name;
  private String assertionTemplate;
  private String defaultFormat;
  private List<MessageConvertorDto> messages;
  private String model;
  private String inference;
  private String provider;
  private List<ToolDto> tools;
  private Boolean jsonMode;
  private Boolean llmGuard;
  private String tags;

  private String promptName;
  private String applicationName;
  private String reasoningEffort;
}
