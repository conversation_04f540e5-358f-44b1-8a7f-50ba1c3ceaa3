/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/*
 * REST client interface for hint management operations.
 * Provides CRUD operations for managing hints through Feign client.
 * Author: VisionWaves
 * Version: 1.0
 */
@FeignClient(
    name = "AgentRest",
    url = "${prompt-analyzer-service.url}",
    path = "/hint",
    primary = false)
public interface HintRest {

  /**
   * Searches for hints in batch with type specification. Requires ROLE_API_HINT_SEARCH_BATCH
   * security role.
   *
   * @param request List of query strings to search for
   * @return List of maps containing search results
   */
  @Operation(
      summary = "Search for hints in batch with type",
      description = "searches hint based on multiple input and type",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_HINT_SEARCH_BATCH_V2})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Hints search successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @PostMapping("/v1/search-batch")
  List<Map<String, Object>> searchPlanBatchV2(@RequestBody Map<String, Object> request);
}
