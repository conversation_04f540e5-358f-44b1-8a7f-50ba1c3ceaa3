/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * Crawl DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to crawl operations
 * in the Prompt Analyzer application. These DTOs encapsulate data for crawling, scraping,
 * and related service interactions.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>Crawl request and response DTOs</li>
 *   <li>Support for web/data crawling operations</li>
 *   <li>Facilitation of crawl data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>Crawl DTOs are used in controllers, services, and clients to transfer crawl-related
 * data for automation and data extraction purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.crawl;
