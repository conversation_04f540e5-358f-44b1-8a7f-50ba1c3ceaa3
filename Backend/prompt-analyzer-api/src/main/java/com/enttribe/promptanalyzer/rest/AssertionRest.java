/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.assertion.AssertionResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * REST interface for managing Assertion entities.
 *
 * <p>Provides endpoints for creating, updating, retrieving, deleting, searching, and counting
 * assertions. Supports both entity and DTO-based operations, with optional filtering and
 * pagination. This interface is used as both a Spring REST controller and a Feign client for remote
 * calls.
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@FeignClient(
    name = "AssertionRest",
    url = "${prompt-analyzer-service.url}",
    path = "/assertion",
    primary = false)
public interface AssertionRest {

  /**
   * Searches for assertions with optional filtering, pagination, and sorting.
   *
   * @param filter the filter string (optional)
   * @param offset the starting index for pagination (required)
   * @param size the number of results to return (required)
   * @param orderBy the field to order by (optional)
   * @param orderType the order direction (asc/desc, optional)
   * @return a list of response DTOs containing assertions and pagination information
   */
  @Operation(
      summary = "Search Assertions",
      description = "Searches for assertions with optional filtering, pagination, and sorting.",
      security = {
        @SecurityRequirement(
            name = APIConstants.DEFAULT_SCHEME,
            scopes = {APIConstants.ROLE_API_ASSERTION_SEARCH})
      })
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = APIConstants.SUCCESS_CODE,
            description = "Search completed successfully"),
        @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
      })
  @GetMapping("/search")
  List<AssertionResponseDto> searchAssertions(
      @RequestParam(required = false) String filter,
      @RequestParam(required = true) Integer offset,
      @RequestParam(required = true) Integer size,
      @RequestParam(required = false) String orderBy,
      @RequestParam(required = false) String orderType);
}
