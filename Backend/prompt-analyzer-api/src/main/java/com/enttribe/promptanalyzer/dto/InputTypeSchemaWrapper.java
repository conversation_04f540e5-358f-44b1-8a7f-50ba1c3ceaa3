/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto;

import com.enttribe.core.generic.exceptions.application.BusinessException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class InputTypeSchemaWrapper {
  private static final ObjectMapper objectMapper = new ObjectMapper();
  private String schema;
  private String type;
  private JsonNode properties;
  private JsonNode required;
  private boolean additionalProperties;

  public InputTypeSchemaWrapper(String jsonSchema) {
    try {
      JsonNode root = objectMapper.readTree(jsonSchema);
      this.schema = root.get("$schema").asText();
      this.type = root.get("type").asText();
      this.properties = root.get("properties");
      this.required = root.get("required");
      this.additionalProperties = root.get("additionalProperties").asBoolean();
    } catch (JsonProcessingException e) {
      throw new BusinessException("Invalid JSON schema" + e);
    }
  }

  public String getSchema() {
    return schema;
  }

  public String getType() {
    return type;
  }

  public String getProperties() {
    return properties.toString();
  }

  public String getRequired() {
    return required.toString();
  }

  public String isAdditionalProperties() {
    return "" + additionalProperties;
  }

  @Override
  public String toString() {
    return String.format(
        "Schema: %s, Type: %s, Properties: %s, Required: %s, AdditionalProperties: %b",
        schema, type, properties.toPrettyString(), required.toPrettyString(), additionalProperties);
  }
}
