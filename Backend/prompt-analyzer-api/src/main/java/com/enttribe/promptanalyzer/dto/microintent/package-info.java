/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */

/**
 * Package containing Data Transfer Objects (DTOs) for micro intent functionality.
 *
 * <p>This package provides DTOs that handle the transfer of micro intent data between different
 * layers of the application. Micro intents represent granular, specific user intentions that can be
 * identified and processed by the system.
 *
 * <p>The DTOs in this package are designed to:
 *
 * <ul>
 *   <li>Encapsulate micro intent response data
 *   <li>Provide structured data transfer for micro intent operations
 *   <li>Support serialization and deserialization of micro intent information
 * </ul>
 *
 * <p>Key components in this package:
 *
 * <ul>
 *   <li>{@link com.enttribe.promptanalyzer.dto.microintent.MicroIntentResponseDto} - DTO for
 *       encapsulating micro intent response data including entity name, intent name, and
 *       descriptive information
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.microintent;
