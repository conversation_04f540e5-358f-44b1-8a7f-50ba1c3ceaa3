/*
 * Copyright © 2025–2026 visionwaves. All rights reserved.
 *
 * This source code is proprietary and confidential information of Visionwave.
 * Unauthorized copying, modification, distribution, or use in whole or in part
 * is strictly prohibited without the express written permission of Visionwave.
 *
 * NiFi Flow DTO package for the Prompt Analyzer application.
 *
 * <p>This package contains Data Transfer Object (DTO) classes related to NiFi flow
 * operations in the Prompt Analyzer application. These DTOs encapsulate data for NiFi
 * processor creation, flow management, and related operations.</p>
 *
 * <h2>Key Features:</h2>
 * <ul>
 *   <li>NiFi processor and flow DTOs</li>
 *   <li>Support for NiFi flow management</li>
 *   <li>Facilitation of NiFi flow data transfer</li>
 * </ul>
 *
 * <h2>Usage:</h2>
 * <p>NiFi flow DTOs are used in controllers, services, and clients to transfer NiFi-related
 * data for workflow automation and management purposes.</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
package com.enttribe.promptanalyzer.dto.nififlow;
