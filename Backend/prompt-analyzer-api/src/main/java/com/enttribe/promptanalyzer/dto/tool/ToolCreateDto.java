/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.tool;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.json.JSONObject;

@Setter
@Getter
@RequiredArgsConstructor
@AllArgsConstructor
public class ToolCreateDto {

  private String endpoint;
  private String method;
  private JSONObject methodObj;
  private String hostname;
  private ToolAuthDto toolAuthentication;
  private Map<String, JSONObject> definitionsMap;
  private SwaggerDto swaggerDto;
  private String toolImage;
}
