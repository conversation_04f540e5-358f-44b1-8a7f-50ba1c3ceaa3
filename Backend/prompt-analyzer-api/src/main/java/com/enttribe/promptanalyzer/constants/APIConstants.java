/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.constants;

/**
 * Constants used throughout the API for authentication, authorization, and responses. Contains role
 * definitions, status codes, and access scopes for different API endpoints. Author: VisionWaves
 * Version: 1.0
 */
public class APIConstants {
  public static final String RESULT = "result";
  public static final String SUCCESS = "success";
  public static final String FAILED = "failed";
  public static final String SUCCESS_CODE = "200";
  public static final String DEFAULT_SCHEME = "default";
  public static final String ERROR = "Internal server error";
  public static final String ERROR_CODE = "500";
  public static final String FORBIDDEN_CODE = "403";
  public static final String FORBIDDEN_MESSAGE = "Access denied - only the creator can delete";

  // Tool scopes - Action-based
  public static final String ROLE_API_TOOL_CREATE = "ROLE_API_TOOL_CREATE";
  public static final String ROLE_API_TOOL_UPDATE = "ROLE_API_TOOL_UPDATE";
  public static final String ROLE_API_TOOL_DELETE = "ROLE_API_TOOL_DELETE";
  public static final String ROLE_API_TOOL_SEARCH = "ROLE_API_TOOL_SEARCH";
  public static final String ROLE_API_TOOL_COUNT = "ROLE_API_TOOL_COUNT";
  public static final String ROLE_API_TOOL_GET_BY_ID = "ROLE_API_TOOL_GET_BY_ID";
  public static final String ROLE_API_TOOL_GET_BY_NANO_ID = "ROLE_API_TOOL_GET_BY_NANO_ID";
  public static final String ROLE_API_TOOL_GET_BY_IDS = "ROLE_API_TOOL_GET_BY_IDS";
  public static final String ROLE_API_TOOL_EXISTS = "ROLE_API_TOOL_EXISTS";
  public static final String ROLE_API_TOOL_EXPORT = "ROLE_API_TOOL_EXPORT";
  public static final String ROLE_API_TOOL_IMPORT = "ROLE_API_TOOL_IMPORT";
  public static final String ROLE_API_TOOL_CHECK_COMPILATION = "ROLE_API_TOOL_CHECK_COMPILATION";
  public static final String ROLE_API_TOOL_GET_BY_APPLICATION = "ROLE_API_TOOL_GET_BY_APPLICATION";
  public static final String ROLE_API_TOOL_GET_BY_AGENT_ID = "ROLE_API_TOOL_GET_BY_AGENT_ID";
  public static final String ROLE_API_TOOL_CHANGE_STATUS = "ROLE_API_TOOL_CHANGE_STATUS";
  public static final String ROLE_API_TOOL_GET_BY_NAME = "ROLE_API_TOOL_GET_BY_NAME";
  public static final String ROLE_API_TOOL_GET_BY_IDS_V1 = "ROLE_API_TOOL_GET_BY_IDS_V1";
  public static final String ROLE_API_TOOL_REGISTER_AS_AGENT = "ROLE_API_TOOL_REGISTER_AS_AGENT";

  // LlmModel scopes - Action-based
  public static final String ROLE_API_LLMMODEL_CREATE = "ROLE_API_LLMMODEL_CREATE";
  public static final String ROLE_API_LLMMODEL_GET_BY_PROVIDER =
      "ROLE_API_LLMMODEL_GET_BY_PROVIDER";
  public static final String ROLE_API_LLMMODEL_GET_ALL = "ROLE_API_LLMMODEL_GET_ALL";
  public static final String ROLE_API_LLMMODEL_GET_FOR_SDK = "ROLE_API_LLMMODEL_GET_FOR_SDK";
  public static final String ROLE_API_LLMMODEL_GET_BY_TYPE = "ROLE_API_LLMMODEL_GET_BY_TYPE";

  // Test case scopes
  public static final String ROLE_API_TESTCASE_GENERATE = "ROLE_API_TESTCASE_GENERATE";
  public static final String ROLE_API_TESTCASE_CREATE = "ROLE_API_TESTCASE_CREATE";
  public static final String ROLE_API_TESTCASE_CREATE_BATCH = "ROLE_API_TESTCASE_CREATE_BATCH";
  public static final String ROLE_API_TESTCASE_UPDATE = "ROLE_API_TESTCASE_UPDATE";
  public static final String ROLE_API_TESTCASE_DELETE = "ROLE_API_TESTCASE_DELETE";
  public static final String ROLE_API_TESTCASE_SEARCH = "ROLE_API_TESTCASE_SEARCH";
  public static final String ROLE_API_TESTCASE_COUNT = "ROLE_API_TESTCASE_COUNT";
  public static final String ROLE_API_TESTCASE_EXPORT = "ROLE_API_TESTCASE_EXPORT";
  public static final String ROLE_API_TESTCASE_IMPORT = "ROLE_API_TESTCASE_IMPORT";

  // Audit API scopes - Action-based
  public static final String ROLE_API_AUDIT_EXCEPTION_SAVE = "ROLE_API_AUDIT_EXCEPTION_SAVE";
  public static final String ROLE_API_AUDIT_PROMPT_SAVE = "ROLE_API_AUDIT_PROMPT_SAVE";
  public static final String ROLE_API_AUDIT_TOOL_SAVE = "ROLE_API_AUDIT_TOOL_SAVE";
  public static final String ROLE_API_AUDIT_PROMPT_GET_BY_AUDIT_ID =
      "ROLE_API_AUDIT_PROMPT_GET_BY_AUDIT_ID";
  public static final String ROLE_API_AUDIT_PROMPT_GET_BY_PROMPT_ID =
      "ROLE_API_AUDIT_PROMPT_GET_BY_PROMPT_ID";
  public static final String ROLE_API_AUDIT_PROMPT_SEARCH = "ROLE_API_AUDIT_PROMPT_SEARCH";
  public static final String ROLE_API_AUDIT_PROMPT_COUNT = "ROLE_API_AUDIT_PROMPT_COUNT";
  public static final String ROLE_API_AUDIT_TOOL_GET_BY_AUDIT_ID =
      "ROLE_API_AUDIT_TOOL_GET_BY_AUDIT_ID";

  // Llm API scopes - Action-based
  public static final String ROLE_API_CHAT_COMPLETE = "ROLE_API_CHAT_COMPLETE";
  public static final String ROLE_API_EXECUTE_PROMPT = "ROLE_API_EXECUTE_PROMPT";
  public static final String ROLE_API_EXECUTE_PROMPT_V1 = "ROLE_API_EXECUTE_PROMPT_V1";
  public static final String ROLE_API_GENERATE_SYSTEM_PROMPT = "ROLE_API_GENERATE_SYSTEM_PROMPT";

  // trigger API scopes
  public static final String ROLE_API_TRIGGER_GET_BY_NAME = "ROLE_API_TRIGGER_GET_BY_NAME";
  public static final String ROLE_API_TRIGGER_UPDATE = "ROLE_API_TRIGGER_UPDATE";
  public static final String ROLE_API_TRIGGER_CREATE = "ROLE_API_TRIGGER_CREATE";
  public static final String ROLE_API_TRIGGER_COUNT = "ROLE_API_TRIGGER_COUNT";
  public static final String ROLE_API_TRIGGER_SEARCH = "ROLE_API_TRIGGER_SEARCH";

  // Agent API scopes
  public static final String ROLE_API_AGENT_GET_HISTORY = "ROLE_API_AGENT_GET_HISTORY";
  public static final String ROLE_API_AGENT_PLAN_CREATE = "ROLE_API_AGENT_PLAN_CREATE";
  public static final String ROLE_API_AGENT_TRIGGER_CREATE = "ROLE_API_AGENT_TRIGGER_CREATE";
  public static final String ROLE_API_AGENT_TRIGGER_NAME_CREATE =
      "ROLE_API_AGENT_TRIGGER_NAME_CREATE";

  // Processor API scopes - Action-based
  public static final String ROLE_API_PROCESSOR_CREATE = "ROLE_API_PROCESSOR_CREATE";
  public static final String ROLE_API_PROCESSOR_UPDATE = "ROLE_API_PROCESSOR_UPDATE";
  public static final String ROLE_API_PROCESSOR_SEARCH = "ROLE_API_PROCESSOR_SEARCH";
  public static final String ROLE_API_PROCESSOR_COUNT = "ROLE_API_PROCESSOR_COUNT";

  // Query API scopes - Action-based
  public static final String ROLE_API_QUERY_CREATE = "ROLE_API_QUERY_CREATE";
  public static final String ROLE_API_QUERY_DELETE = "ROLE_API_QUERY_DELETE";
  public static final String ROLE_API_QUERY_SEARCH = "ROLE_API_QUERY_SEARCH";
  public static final String ROLE_API_QUERY_COUNT = "ROLE_API_QUERY_COUNT";

  // Tag API scopes - Action-based
  public static final String ROLE_API_TAG_CREATE = "ROLE_API_TAG_CREATE";
  public static final String ROLE_API_TAG_UPDATE = "ROLE_API_TAG_UPDATE";
  public static final String ROLE_API_TAG_DELETE = "ROLE_API_TAG_DELETE";
  public static final String ROLE_API_TAG_SEARCH = "ROLE_API_TAG_SEARCH";
  public static final String ROLE_API_TAG_COUNT = "ROLE_API_TAG_COUNT";

  // Knowledge Base scopes - Action-based
  public static final String ROLE_API_KNOWLEDGE_BASE_CREATE = "ROLE_API_KNOWLEDGE_BASE_CREATE";
  public static final String ROLE_API_KNOWLEDGE_BASE_UPDATE = "ROLE_API_KNOWLEDGE_BASE_UPDATE";
  public static final String ROLE_API_KNOWLEDGE_BASE_DELETE = "ROLE_API_KNOWLEDGE_BASE_DELETE";
  public static final String ROLE_API_KNOWLEDGE_BASE_COUNT = "ROLE_API_KNOWLEDGE_BASE_COUNT";
  public static final String ROLE_API_KNOWLEDGE_BASE_SEARCH = "ROLE_API_KNOWLEDGE_BASE_SEARCH";
  public static final String ROLE_API_KNOWLEDGE_BASE_GET_BY_ID =
      "ROLE_API_KNOWLEDGE_BASE_GET_BY_ID";
  public static final String ROLE_API_KNOWLEDGE_BASE_GET_BY_NANO_ID =
      "ROLE_API_KNOWLEDGE_BASE_GET_BY_NANO_ID";
  public static final String ROLE_API_KNOWLEDGE_BASE_GET_BY_NANO_IDS =
      "ROLE_API_KNOWLEDGE_BASE_GET_BY_NANO_IDS";
  public static final String ROLE_API_KNOWLEDGE_BASE_GET_BY_IDS =
      "ROLE_API_KNOWLEDGE_BASE_GET_BY_IDS";
  public static final String ROLE_API_KNOWLEDGE_BASE_EXISTS = "ROLE_API_KNOWLEDGE_BASE_EXISTS";
  public static final String ROLE_API_KNOWLEDGE_BASE_SHOW_TABLES =
      "ROLE_API_KNOWLEDGE_BASE_SHOW_TABLES";
  public static final String ROLE_API_KNOWLEDGE_BASE_MILVUS_IMPORT_DATA =
      "ROLE_API_KNOWLEDGE_BASE_MILVUS_IMPORT_DATA";
  public static final String ROLE_API_KNOWLEDGE_BASE_EXPORT = "ROLE_API_KNOWLEDGE_BASE_EXPORT";
  public static final String ROLE_API_KNOWLEDGE_BASE_IMPORT = "ROLE_API_KNOWLEDGE_BASE_IMPORT";
  public static final String ROLE_API_KNOWLEDGE_BASE_EXISTS_BY_NAME =
      "ROLE_API_KNOWLEDGE_BASE_EXISTS_BY_NAME";

  // MCP Server scopes - Action-based
  public static final String ROLE_API_MCP_SERVER_CREATE = "ROLE_API_MCP_SERVER_CREATE";
  public static final String ROLE_API_MCP_SERVER_UPDATE = "ROLE_API_MCP_SERVER_UPDATE";
  public static final String ROLE_API_MCP_SERVER_DELETE = "ROLE_API_MCP_SERVER_DELETE";
  public static final String ROLE_API_MCP_SERVER_SEARCH = "ROLE_API_MCP_SERVER_SEARCH";
  public static final String ROLE_API_MCP_SERVER_COUNT = "ROLE_API_MCP_SERVER_COUNT";
  public static final String ROLE_API_MCP_SERVER_GET_BY_IDS = "ROLE_API_MCP_SERVER_GET_BY_IDS";

  // Hint API scopes - Action-based
  public static final String ROLE_API_HINT_SEARCH = "ROLE_API_HINT_SEARCH";
  public static final String ROLE_API_HINT_SEARCH_BATCH = "ROLE_API_HINT_SEARCH_BATCH";
  public static final String ROLE_API_HINT_SEARCH_BATCH_V1 = "ROLE_API_HINT_SEARCH_BATCH_V1";
  public static final String ROLE_API_HINT_CREATE = "ROLE_API_HINT_CREATE";

  // Time Parser scopes - Action-based
  public static final String ROLE_API_TIME_PARSER_PARSE = "ROLE_API_TIME_PARSER_PARSE";
  public static final String ROLE_API_TIME_PARSER_UPDATE = "ROLE_API_TIME_PARSER_UPDATE";

  // Tool Callback Provider scopes
  public static final String ROLE_API_TOOL_CALLBACK_GET_LIST = "ROLE_API_TOOL_CALLBACK_GET_LIST";

  // Prompt API scopes - Action-based
  public static final String ROLE_API_PROMPT_CREATE = "ROLE_API_PROMPT_CREATE";
  public static final String ROLE_API_PROMPT_UPDATE = "ROLE_API_PROMPT_UPDATE";
  public static final String ROLE_API_PROMPT_DELETE = "ROLE_API_PROMPT_DELETE";
  public static final String ROLE_API_PROMPT_SEARCH = "ROLE_API_PROMPT_SEARCH";
  public static final String ROLE_API_PROMPT_COUNT = "ROLE_API_PROMPT_COUNT";
  public static final String ROLE_API_PROMPT_GET_BY_ID = "ROLE_API_PROMPT_GET_BY_ID";
  public static final String ROLE_API_PROMPT_GET_BY_NANO_ID = "ROLE_API_PROMPT_GET_BY_NANO_ID";
  public static final String ROLE_API_PROMPT_GET_BY_PROMPT_ID = "ROLE_API_PROMPT_GET_BY_PROMPT_ID";
  public static final String ROLE_API_PROMPT_GET_BY_NAME = "ROLE_API_PROMPT_GET_BY_NAME";
  public static final String ROLE_API_PROMPT_GET_BY_APP_NAME = "ROLE_API_PROMPT_GET_BY_APP_NAME";
  public static final String ROLE_API_PROMPT_EXPORT = "ROLE_API_PROMPT_EXPORT";
  public static final String ROLE_API_PROMPT_IMPORT = "ROLE_API_PROMPT_IMPORT";
  public static final String ROLE_API_PROMPT_FILTER = "ROLE_API_PROMPT_FILTER";
  public static final String ROLE_API_PROMPT_EXPORT_BY_IDS = "ROLE_API_PROMPT_EXPORT_BY_IDS";
  public static final String ROLE_API_PROMPT_SEARCH_EXISTS = "ROLE_API_PROMPT_SEARCH_EXISTS";
  public static final String ROLE_API_PROMPT_GET_DIS_APP_NAME = "ROLE_API_PROMPT_GET_DIS_APP_NAME";
  public static final String ROLE_API_PROMPT_GET_DIS_APP_AND_CATE =
      "ROLE_API_PROMPT_GET_DIS_APP_AND_CATE";
  public static final String ROLE_API_PROMPT_UPDATE_ASSERTION_TEMP =
      "ROLE_API_PROMPT_UPDATE_ASSERTION_TEMP";
  public static final String ROLE_API_PROMPT_SEARCH_V1 = "ROLE_API_PROMPT_SEARCH_V1";
  public static final String ROLE_API_PROMPT_UPDATE_TAG = "ROLE_API_PROMPT_UPDATE_TAG";
  public static final String ROLE_API_KNOWLEDGE_BASE_DOWNLOAD_FILE =
      "ROLE_API_KNOWLEDGE_BASE_DOWNLOAD_FILE";

  // Test Suite API scopes - Action-based
  public static final String ROLE_API_TESTSUITE_DELETE = "ROLE_API_TESTSUITE_DELETE";
  public static final String ROLE_API_TESTSUITE_SEARCH = "ROLE_API_TESTSUITE_SEARCH";
  public static final String ROLE_API_TESTSUITE_COUNT = "ROLE_API_TESTSUITE_COUNT";
  public static final String ROLE_API_TESTSUITE_UPDATE = "ROLE_API_TESTSUITE_UPDATE";
  public static final String ROLE_API_TESTSUITE_CREATE = "ROLE_API_TESTSUITE_CREATE";
  public static final String ROLE_API_ADD_AGENT_TEST_CASES = "ROLE_API_ADD_AGENT_TEST_CASES";

  // Assertion API scopes - Action-based
  public static final String ROLE_API_ASSERTION_CREATE = "ROLE_API_ASSERTION_CREATE";
  public static final String ROLE_API_ASSERTION_UPDATE = "ROLE_API_ASSERTION_UPDATE";
  public static final String ROLE_API_ASSERTION_DELETE = "ROLE_API_ASSERTION_DELETE";
  public static final String ROLE_API_ASSERTION_SEARCH = "ROLE_API_ASSERTION_SEARCH";
  public static final String ROLE_API_ASSERTION_COUNT = "ROLE_API_ASSERTION_COUNT";
  public static final String ROLE_API_ASSERTION_GET_BY_ID = "ROLE_API_ASSERTION_GET_BY_ID";

  // Test Execution API scopes - Action-based
  public static final String ROLE_API_TEST_EXECUTION_CREATE = "ROLE_API_TEST_EXECUTION_CREATE";
  public static final String ROLE_API_TEST_EXECUTION_UPDATE = "ROLE_API_TEST_EXECUTION_UPDATE";
  public static final String ROLE_API_TEST_EXECUTION_DELETE = "ROLE_API_TEST_EXECUTION_DELETE";
  public static final String ROLE_API_TEST_EXECUTION_SEARCH = "ROLE_API_TEST_EXECUTION_SEARCH";
  public static final String ROLE_API_TEST_EXECUTION_COUNT = "ROLE_API_TEST_EXECUTION_COUNT";
  public static final String ROLE_API_TEST_EXECUTION_GET_BY_ID =
      "ROLE_API_TEST_EXECUTION_GET_BY_ID";

  public static final String ROLE_API_AGENT_EXECUTE_TEST = "ROLE_API_AGENT_EXECUTE_TEST";
  public static final String ROLE_API_AGENT_EXECUTE_TEST_SUITE =
      "ROLE_API_AGENT_EXECUTE_TEST_SUITE";
  public static final String ROLE_API_GET_INPUT_SCHEMA = "ROLE_API_GET_INPUT_SCHEMA";

  // Result API scopes - Action-based
  public static final String ROLE_API_TEST_COUNT = "ROLE_API_TEST_COUNT";
  public static final String ROLE_API_TEST_SEARCH = "ROLE_API_TEST_SEARCH";
  public static final String ROLE_API_TEST_SUITE_EXPORT = "ROLE_API_TEST_SUITE_EXPORT";
  public static final String ROLE_API_TEST_SUITE_IMPORT = "ROLE_API_TEST_SUITE_IMPORT";

  public static final String ROLE_API_PROMPT_VIEW_BY_ID = "ROLE_API_PROMPT_VIEW_BY_ID";
  public static final String ROLE_API_TOOL_SWAGGER_CREATE = "ROLE_API_TOOL_SWAGGER_CREATE";
  public static final String ROLE_API_TOOL_WORKFLOW_CREATE = "ROLE_API_TOOL_WORKFLOW_CREATE";
  public static final String ROLE_API_TOOL_UPDATE_TAG = "ROLE_API_TOOL_UPDATE_TAG";
  public static final String ROLE_API_TOOL_FIND_BY_IDS = "ROLE_API_TOOL_FIND_BY_IDS";
  public static final String ROLE_API_TOOL_GET_DTO_BY_AGENT_ID =
      "ROLE_API_TOOL_GET_DTO_BY_AGENT_ID";
  public static final String ROLE_API_AGENT_TESTCASE_CREATE = "ROLE_API_AGENT_TESTCASE_CREATE";
  public static final String ROLE_API_AGENT_TESTCASE_UPDATE = "ROLE_API_AGENT_TESTCASE_UPDATE";
  public static final String ROLE_API_AGENT_TESTCASE_DELETE = "ROLE_API_AGENT_TESTCASE_DELETE";
  public static final String ROLE_API_AGENT_TESTCASE_SEARCH = "ROLE_API_AGENT_TESTCASE_SEARCH";
  public static final String ROLE_API_AGENT_TESTCASE_BY_ID_SEARCH =
      "ROLE_API_AGENT_TESTCASE_BY_ID_SEARCH";
  public static final String ROLE_API_RESULT_TESTCASE_SEARCH = "ROLE_API_RESULT_TESTCASE_SEARCH";
  public static final String ROLE_API_AGENT_TESTCASE_COUNT = "ROLE_API_AGENT_TESTCASE_COUNT";
  public static final String ROLE_API_RESULT_COUNT = "ROLE_API_RESULT_COUNT";
  public static final String ROLE_API_KNOWLEDGE_BASE_DOCUMENT_CREATE =
      "ROLE_API_KNOWLEDGE_BASE_DOCUMENT_CREATE";
  public static final String ROLE_API_KNOWLEDGE_BASE_WEBSITE_CREATE =
      "ROLE_API_KNOWLEDGE_BASE_WEBSITE_CREATE";
  public static final String ROLE_API_KNOWLEDGE_BASE_CONTENT_CREATE =
      "ROLE_API_KNOWLEDGE_BASE_CONTENT_CREATE";
  public static final String ROLE_API_KNOWLEDGE_BASE_TAG_UPDATE =
      "ROLE_API_KNOWLEDGE_BASE_TAG_UPDATE";
  public static final String ROLE_API_KNOWLEDGE_BASE_DOCUMENT_UPDATE =
      "ROLE_API_KNOWLEDGE_BASE_DOCUMENT_UPDATE";
  public static final String ROLE_API_KNOWLEDGE_BASE_ANSWER_SEARCH =
      "ROLE_API_KNOWLEDGE_BASE_ANSWER_SEARCH";
  public static final String ROLE_API_KNOWLEDGE_BASE_FIND_BY_ID =
      "ROLE_API_KNOWLEDGE_BASE_FIND_BY_ID";
  public static final String ROLE_API_PROMPT_VERSION_SEARCH = "ROLE_API_PROMPT_VERSION_SEARCH";
  public static final String ROLE_API_PROMPT_APPLICATION_SEARCH =
      "ROLE_API_PROMPT_APPLICATION_SEARCH";
  public static final String ROLE_API_PROMPT_FIND_BY_ID = "ROLE_API_PROMPT_FIND_BY_ID";
  public static final String ROLE_API_PROMPT_GET_BY_ID_V1 = "ROLE_API_PROMPT_GET_BY_ID_V1";
  public static final String ROLE_API_TESTSUITE_BY_ID_SEARCH = "ROLE_API_TESTSUITE_BY_ID_SEARCH";
  public static final String ROLE_API_TESTSUITE_FIND_BY_ID_SEARCH =
      "ROLE_API_TESTSUITE_FIND_BY_ID_SEARCH";
  public static final String ROLE_API_TESTSUITE_BY_ID_COUNT = "ROLE_API_TESTSUITE_BY_ID_COUNT";
  public static final String ROLE_API_RESULT_SEARCH = "ROLE_API_ASSERTION_SEARCH";
  public static final String ROLE_API_ASSERTION_RESULT_COUNT = "ROLE_API_ASSERTION_RESULT_COUNT";
  public static final String ROLE_API_HINT_SEARCH_BATCH_V2 = "ROLE_API_HINT_SEARCH_BATCH_V2";
  public static final String ROLE_API_X101_TOOL_GET = "ROLE_API_X101_TOOL_GET";

  private APIConstants() {}
}
