/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import jakarta.persistence.*;
import java.util.HashSet;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity representing a test case. Stores details about test cases, including input data,
 * assertions, and associations with test plans and prompts.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@Setter
@Entity
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "TESTCASE")
public class TestCase extends BaseEntityGlobal {

  /** Unique ID of the test case. */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Integer id;

  /** Unique identifier for the test case. */
  @Column(name = "TESTCASE_ID")
  private String testcaseId;

  /** Indicates if the test case is marked as deleted. */
  @Column(name = "DELETED")
  private Boolean deleted = false;

  /** JSON input data for the test case. */
  @Column(name = "INPUT_JSON", columnDefinition = "TEXT")
  private String inputJson;

  /** Remarks or comments about the test case. */
  @Column(name = "REMARK", columnDefinition = "TEXT")
  private String remark;

  /** Assertions for validating the test case. */
  @Column(name = "ASSERTIONS", columnDefinition = "TEXT")
  private String assertions;

  /** Test plans associated with the test case. */
  @ManyToMany
  @JoinTable(
      name = "TEST_PLAN_TEST",
      joinColumns = @JoinColumn(name = "TEST_ID"),
      inverseJoinColumns = @JoinColumn(name = "TEST_PLAN_ID"))
  private Set<TestPlan> testPlans = new HashSet<>();

  /** Associated prompt for the test case. */
  @ManyToOne
  @JoinColumn(name = "PROMPT_ID", nullable = false)
  private Prompt prompt;

  @Column(name = "NANO_ID", length = 50)
  private String nanoId;
}
