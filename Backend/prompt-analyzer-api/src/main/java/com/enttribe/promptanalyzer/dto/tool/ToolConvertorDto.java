/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.dto.tool;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * A Data Transfer Object (DTO) that handles tool conversion operations and configurations. Provides
 * a comprehensive structure for tool data transformation and management, including bytecode
 * handling and tool metadata. Author: VisionWaves Version: 1.0
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ToolConvertorDto {

  private String id;
  private String applicationName;
  private String category;
  private String name;
  private String displayName;
  private String version;
  private String toolId;
  private String language;
  private String type;
  private String sourceCode;
  private String description;
  private String tags;
  private String className;
  private String requestType;
  private String status;
  private String toolImage;
  private Map<String, byte[]> byteCodeMap;
  private Long agentId;
  private String apiTool;
  private Boolean returnDirect;
  private String toolName;
}
