{"populateUser": {"userid": 140223, "username": "yogesh.admin", "firstname": "<PERSON><PERSON><PERSON>", "lastname": "Admin", "email": "<EMAIL>", "userFullname": "<PERSON><PERSON><PERSON>", "customerWrapper": {"id": 1, "name": "Platform-SaaS", "client_id": "WEB", "realm_name": "Platform-SaaS", "client_secret": "mock-secret"}, "privileges": ["READ", "WRITE", "ADMIN"], "applicationName": "PLATFORM", "moduleName": ["CORE", "ADMIN"], "activeUserSpaceId": 1, "activeUserSpaceName": "<PERSON><PERSON><PERSON>", "imagePath": "/images/default.png", "aoaProfileJson": "[{\"applicationName\":\"PLATFORM\",\"columnBasedAccess\":{\"users\":[\"id\",\"name\",\"email\"]},\"encryptedColumnNames\":{\"users\":[\"email\",\"phone\"]}}]", "department": ["Engineering"], "location": ["Bangalore"], "division": "Technology", "divisionId": 1, "orgRoleName": "Admin", "orgRoleId": 1, "activeDivisionId": 1}, "populateCustomer": {"id": 1, "name": "Platform-SaaS", "client_id": "WEB", "realm_name": "Platform-SaaS", "client_secret": "mock-secret"}}